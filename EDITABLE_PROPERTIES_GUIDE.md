# Editable Properties Quick Reference Guide

This guide provides a comprehensive reference of all editable properties for each funnel section type.

## 🦸 Hero Section

### Basic Content
- `title` - Main headline text
- `subtitle` - Subheadline text
- `ctaText` - Primary CTA button text
- `ctaUrl` - Primary CTA button link
- `secondaryCtaText` - Secondary CTA text (optional)
- `secondaryCtaUrl` - Secondary CTA link (optional)

### Branding
- `logoUrl` - Brand logo URL
- `tagline` - Brand tagline text

### Background & Styling
- `backgroundImage` - Background image URL
- `backgroundVideo` - Background video URL
- `backgroundColor` - Background color/gradient
- `textColor` - Text color
- `height` - Section height

### Overlay
- `overlay` - Enable/disable overlay (boolean)
- `overlayOpacity` - Overlay opacity (0-1)
- `overlayColor` - Overlay color

### Layout & Animation
- `layout` - Layout style: "centered", "left", "right", "split"
- `animationEnabled` - Enable animations (boolean)
- `animationType` - Animation type: "fade", "slide", "zoom", "none"

---

## ⭐ Features Section

### Section Header
- `heading` - Section title
- `subheading` - Section subtitle/intro

### Styling
- `backgroundColor` - Background color
- `backgroundPattern` - Background pattern URL
- `textColor` - Text color
- `columns` - Number of features per row
- `layout` - Display layout: "grid", "cards", "carousel", "columns"

### Features Array
Each feature object contains:
- `title` - Feature title
- `description` - Feature description
- `icon` - Feature icon (emoji or text)
- `iconUrl` - Custom icon image URL (optional)
- `ctaText` - Optional "Learn more" link text
- `ctaUrl` - Optional "Learn more" link URL

---

## 💰 Pricing Section

### Section Header
- `heading` - Section title
- `subheading` - Section subtitle

### Pricing Options
- `billingToggle` - Enable monthly/yearly toggle (boolean)
- `currency` - Currency symbol (e.g., "$", "€", "£")
- `disclaimer` - Pricing notes/disclaimers

### Styling
- `backgroundColor` - Background color
- `accentColor` - Accent color for highlights
- `textColor` - Text color

### Plans Array
Each plan object contains:
- `name` - Plan name
- `price` - Current display price
- `priceMonthly` - Monthly price (for toggle)
- `priceYearly` - Yearly price (for toggle)
- `period` - Billing period (e.g., "/month")
- `description` - Plan description
- `features` - Array of feature strings
- `highlighted` - Mark as "Most Popular" (boolean)
- `ctaText` - CTA button text
- `ctaUrl` - CTA button link

---

## 💬 Testimonials Section

### Section Header
- `heading` - Section title
- `subheading` - Section subtitle (optional)

### Styling
- `backgroundColor` - Background color
- `backgroundPattern` - Background pattern URL
- `textColor` - Text color
- `columns` - Number of testimonials per row
- `layout` - Display layout: "carousel", "grid", "single"

### Testimonials Array
Each testimonial object contains:
- `quote` - Testimonial text
- `author` - Reviewer name
- `role` - Reviewer position/title
- `company` - Company name
- `avatar` - Reviewer photo URL
- `companyLogo` - Company logo URL (optional)
- `rating` - Star rating (1-5)

---

## 📝 Form Section (Lead Capture)

### Form Header
- `heading` - Form title
- `subheading` - Form intro text/instructions

### Form Configuration
- `submitText` - Submit button text
- `submitUrl` - Form submission endpoint
- `successMessage` - Success message after submission
- `errorMessage` - Error message on failure
- `integration` - Integration service/CRM endpoint

### Privacy
- `privacyText` - Privacy disclaimer text
- `privacyUrl` - Privacy policy link

### Styling
- `backgroundColor` - Background color
- `textColor` - Text color
- `containerStyle` - Form container: "default", "card", "minimal"

### Fields Array
Each field object contains:
- `type` - Field type: "text", "email", "textarea", "tel", "number", "dropdown", "checkbox"
- `label` - Field label
- `placeholder` - Placeholder text (optional)
- `required` - Required field (boolean)
- `options` - Array of options (for dropdown fields)

---

## 📄 Footer Section

### Branding
- `companyName` - Company name
- `logoUrl` - Company logo URL
- `tagline` - Company tagline

### Contact Information
- `contactEmail` - Contact email address
- `contactPhone` - Contact phone number
- `contactAddress` - Physical address

### Newsletter Signup
- `newsletterEnabled` - Enable newsletter form (boolean)
- `newsletterTitle` - Newsletter section title
- `newsletterPlaceholder` - Email input placeholder
- `newsletterButtonText` - Subscribe button text

### Navigation & Links
- `columns` - Array of link column objects
  - Each column has:
    - `title` - Column title
    - `links` - Array of link objects
      - `label` - Link text
      - `url` - Link URL
- `socialLinks` - Array of social media links
  - Each link has:
    - `platform` - Platform name (e.g., "twitter")
    - `url` - Social media URL

### Styling
- `backgroundColor` - Background color
- `textColor` - Text color
- `layout` - Footer layout: "columns", "centered", "minimal"
- `copyright` - Copyright text

---

## How to Edit Properties

### In the Editor UI
1. Click on any section in the canvas
2. The PropertyPanel on the right will show all editable properties
3. Properties are automatically rendered with appropriate UI controls:
   - Text inputs for strings
   - Color pickers for colors
   - Checkboxes for booleans
   - Number inputs for numbers
   - Textareas for long text
   - Dropdowns for predefined options
   - Specialized editors for arrays (features, plans, testimonials, etc.)

### Programmatically
```typescript
// Update a property
onUpdate({
  ...component.properties,
  title: "New Title",
  backgroundColor: "#f0f0f0"
});

// Add to an array
const newFeatures = [
  ...component.properties.features,
  {
    title: "New Feature",
    description: "Description",
    icon: "✨"
  }
];
onUpdate({
  ...component.properties,
  features: newFeatures
});
```

---

## Property Validation

### URL Fields
- Should include protocol (https://)
- Validated on blur
- Examples: `backgroundImage`, `ctaUrl`, `logoUrl`, `iconUrl`

### Color Fields
- Accepts hex codes, RGB, gradients
- Includes color picker UI
- Examples: `backgroundColor`, `textColor`, `overlayColor`

### Boolean Fields
- Rendered as checkboxes
- Default to `false` if not specified
- Examples: `overlay`, `highlighted`, `required`, `newsletterEnabled`

### Array Fields
- Have specialized editors with add/edit/delete
- Can be collapsed/expanded
- Support drag-to-reorder (future enhancement)
- Examples: `features`, `plans`, `testimonials`, `fields`, `columns`

---

## Best Practices

1. **Always provide fallbacks**: Use optional chaining and default values
2. **Keep arrays manageable**: Recommend 3-6 items for optimal UX
3. **Use semantic colors**: Follow brand guidelines
4. **Optimize images**: Compress background images for performance
5. **Test responsive**: Ensure layouts work on mobile
6. **Validate URLs**: Check all links before publishing
7. **A/B test CTAs**: Try different button texts and colors
8. **Accessibility**: Ensure color contrast meets WCAG standards
