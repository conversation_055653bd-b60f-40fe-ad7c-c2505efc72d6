# Pricing Section Quick Start Guide

## Overview
The Pricing Section component is **fully editable** in the FunnelFlow AI drag-and-drop editor. This guide shows you how to customize it.

## 🎯 Quick Access

### Open the Editor
1. Navigate to your funnel
2. Click **"Edit with Drag & Drop"** button
3. The visual editor opens

### Add a Pricing Section (if needed)
1. Look at the **left sidebar** (Component Palette)
2. Find **"💰 Pricing Section"** under "Layout / Container"
3. Either:
   - **Drag** it onto the canvas, OR
   - **Click** it to add to the current page

## ✏️ Edit Pricing Section

### Select the Section
- Click on the pricing section in the canvas
- The **right sidebar** (PropertyPanel) will show all editable properties

### What You Can Edit

#### Section Header
- **Heading** - Main title (e.g., "Choose Your Plan")
- **Subheading** - Subtitle text (e.g., "Find the perfect plan for your needs")

#### Section Styling
- **Background Color** - Section background
- **Accent Color** - Color for highlighted plans
- **Text Color** - Main text color

#### Advanced Options
- **Billing Toggle** - Enable monthly/yearly pricing switch
- **Currency** - Currency symbol ($ , €, £, etc.)
- **Disclaimer** - Add pricing notes or disclaimers

## 📋 Edit Individual Plans

### Expand a Plan
1. In the PropertyPanel, find the **"Plans"** section
2. Click on any plan to expand its editor
3. You'll see all editable fields

### Plan Properties

| Field | Description | Example |
|-------|-------------|---------|
| **Plan Name** | Title of the plan | "Starter", "Pro", "Enterprise" |
| **Price** | Current display price | "$29" |
| **Monthly Price** | Price for monthly billing | "$29" |
| **Yearly Price** | Price for yearly billing | "$290" |
| **Period** | Billing period text | "/month", "/year" |
| **Description** | Short plan description | "Perfect for small teams" |
| **Features** | List of features (one per line) | "5 Projects\n10GB Storage\nEmail Support" |
| **CTA Text** | Button text | "Get Started", "Contact Sales" |
| **CTA URL** | Button link | "#signup", "https://..." |
| **Highlight** | Mark as "Popular" | ☑ checkbox |

### Features List
- Type one feature per line in the textarea
- Example:
  ```
  5 Projects
  10GB Storage
  Email Support
  Basic Analytics
  ```
- Each feature will display with a checkmark icon

## ➕ Add or Remove Plans

### Add a New Plan
1. Scroll to the **"Plans"** section
2. Click **"+ Add Plan"** button
3. A new plan is created with default values
4. Expand it and customize all fields

### Delete a Plan
1. Expand the plan you want to remove
2. Scroll to the bottom of the plan editor
3. Click **"Delete Plan"** button (red)
4. Confirm the deletion

### Reorder Plans
- Plans are displayed in the order they appear in the list
- Currently, drag-to-reorder is not implemented
- To reorder: Delete and re-add plans in the desired order

## 🎨 Styling Tips

### Highlight a Plan
- Check the **"Highlight as popular/recommended"** checkbox
- This will:
  - Add a "Popular" badge at the top
  - Apply accent color styling
  - Make the plan stand out visually
  - Scale the plan slightly larger

### Color Scheme
- Use consistent colors across your funnel
- The **Accent Color** affects highlighted plans
- Ensure good contrast for accessibility
- Test on both light and dark backgrounds

### Responsive Design
- Plans automatically adjust to screen size
- On mobile, plans stack vertically
- On desktop, plans display side-by-side
- Test your funnel on multiple devices

## 📊 Common Pricing Patterns

### Basic 3-Tier Pricing
```
Plan 1: "Basic"    - $19/month - Basic features
Plan 2: "Pro"      - $49/month - Advanced features (Highlighted)
Plan 3: "Premium"  - $99/month - All features
```

### 2-Tier with Enterprise
```
Plan 1: "Starter"     - $29/month
Plan 2: "Business"    - $79/month (Highlighted)
Plan 3: "Enterprise"  - Custom - "Contact Sales"
```

### Freemium Model
```
Plan 1: "Free"        - $0 - Limited features
Plan 2: "Professional" - $49/month (Highlighted)
Plan 3: "Team"        - $99/month
```

## 🚀 Best Practices

### Pricing
- Show value clearly with features
- Highlight the most popular plan
- Use consistent currency throughout
- Consider annual discounts

### Features
- List 3-6 key features per plan
- Keep feature text concise
- Use clear, benefit-focused language
- Order features by importance

### CTAs
- Use action-oriented button text
- "Get Started" for self-service plans
- "Contact Sales" for enterprise plans
- Ensure CTAs stand out visually

### Layout
- Odd numbers (3 or 5 plans) work best visually
- Keep plan names short and memorable
- Align features across plans for easy comparison
- Test on mobile devices

## 🔧 Troubleshooting

### "I can't see the PropertyPanel"
- Make sure you've **clicked** on the pricing section
- The selected component should have a blue border
- The PropertyPanel appears on the **right side**

### "My changes aren't showing"
- Changes apply in real-time
- Try clicking outside the input field
- Refresh the canvas if needed
- Use "Save Changes" button to persist

### "I can't find the pricing section"
- Check the **Component Palette** (left sidebar)
- Scroll through the "Layout / Container" category
- Look for the 💰 icon
- Use the search feature if available

### "The editor is not opening"
- Make sure you clicked "Edit with Drag & Drop"
- Check browser console for errors
- Try refreshing the page
- Ensure you have edit permissions

## 📚 Additional Resources

- **Full Documentation**: `/workspace/EDITABLE_PROPERTIES_GUIDE.md`
- **Implementation Details**: `/workspace/SPARK-355-RESOLUTION.md`
- **General Editor Guide**: `/workspace/DRAG_AND_DROP_IMPLEMENTATION.md`

## ✅ Verification

To confirm your pricing section is editable:

1. ✅ Open the drag-and-drop editor
2. ✅ Click on a pricing section
3. ✅ PropertyPanel opens on the right
4. ✅ "Plans" section is visible
5. ✅ Click to expand a plan
6. ✅ All fields are editable
7. ✅ "+ Add Plan" button is visible
8. ✅ Changes appear in real-time

If all steps work, your pricing section is fully functional! 🎉

## 🆘 Support

If you're still experiencing issues:
1. Check the browser console for errors
2. Verify you're on the latest version
3. Review the resolution document: `SPARK-355-RESOLUTION.md`
4. Contact the development team with specific details

---

**Last Updated**: 2025-10-15  
**Issue**: SPARK-355  
**Status**: ✅ Fully Implemented
