# SPARK-304 Resolution: Edit and Preview Individual Funnel Pages

## Issue Summary
**Title**: For each page of the funnel, I need to be able to edit the page and preview it

**Description**: Users needed the ability to edit and preview individual pages within a funnel, rather than only being able to edit the entire funnel at once.

## Solution Implemented

### 1. Enhanced FunnelEditor Component
**File**: `components/editor/FunnelEditor.tsx`

**Changes**:
- Added `initialPageIndex` prop to the `FunnelEditorProps` interface
- Modified the editor to accept and use the `initialPageIndex` prop (default: 0)
- The editor now opens directly to the specified page when launched

```typescript
interface FunnelEditorProps {
  funnel: FunnelSchema;
  onSave: (funnel: FunnelSchema) => void;
  onClose: () => void;
  initialPageIndex?: number; // NEW
}
```

### 2. Created PagePreview Component
**File**: `components/PagePreview.tsx` (NEW)

**Features**:
- Full-screen modal preview of individual funnel pages
- Renders all section types (hero, features, pricing, testimonials, CTA, FAQ)
- Displays page metadata (name, type, section count)
- Shows Whop plan IDs for checkout pages
- Applies funnel theme colors to the preview
- Responsive design with proper styling

**Component Interface**:
```typescript
interface PagePreviewProps {
  page: FunnelPage | null;
  isOpen: boolean;
  onClose: () => void;
  theme?: {
    primaryColor: string;
    backgroundColor: string;
    textColor: string;
  };
}
```

### 3. Updated FunnelBuilder Component
**File**: `components/FunnelBuilder.tsx`

**Changes**:
- Added state management for page editing and previewing:
  - `editingPageIndex`: Tracks which page is being edited
  - `previewingPage`: Tracks which page is being previewed
- Created handler functions:
  - `handleEditPage(pageIndex)`: Opens the editor for a specific page
  - `handlePreviewPage(page)`: Opens the preview modal for a specific page
- Wired up the "Edit Page" button to open the editor with the selected page
- Wired up the "Preview" button to open the preview modal
- Added PagePreview component to the render tree

**Button Implementation**:
```typescript
<Button
  size="1"
  variant="soft"
  onClick={() => handleEditPage(index)}
>
  Edit Page
</Button>
<Button
  size="1"
  variant="ghost"
  onClick={() => handlePreviewPage(page)}
>
  Preview
</Button>
```

## User Flow

### Editing a Specific Page
1. User views the funnel in the FunnelBuilder
2. User clicks the "Edit Page" button on any page card
3. The FunnelEditor opens with that specific page selected and ready to edit
4. User can drag-and-drop components, modify properties, and save changes
5. Changes are saved and the user returns to the funnel view

### Previewing a Specific Page
1. User views the funnel in the FunnelBuilder
2. User clicks the "Preview" button on any page card
3. A full-screen modal opens showing a realistic preview of that page
4. User can see how the page will look with all its sections rendered
5. User can close the preview and return to editing

## Technical Details

### State Management
- Page editing state is tracked using `editingPageIndex` (number | null)
- Page preview state is tracked using `previewingPage` (FunnelPage | null)
- Both states are properly cleaned up when closing the respective modals

### Component Communication
- FunnelEditor receives the initial page index via props
- PagePreview receives the page data and theme via props
- Both components use callback props to notify the parent when closing

### Type Safety
- All new props and state are properly typed using existing TypeScript interfaces
- No new types were needed - the solution uses `FunnelPage`, `FunnelSchema`, etc.

## Benefits

1. **Granular Control**: Users can now edit specific pages without navigating through all pages
2. **Quick Preview**: Users can preview individual pages without affecting the editor state
3. **Better UX**: Direct access to specific page editing and previewing from the funnel overview
4. **Maintained Context**: The editor remembers which page was selected, improving workflow
5. **Visual Feedback**: Preview modal shows exactly how the page will look to end users

## Testing Recommendations

1. **Edit Page Flow**:
   - Click "Edit Page" on different pages and verify correct page is selected
   - Make changes to a page and save, verify changes persist
   - Open editor, switch pages, and verify page switching works correctly

2. **Preview Page Flow**:
   - Click "Preview" on different pages and verify correct content is shown
   - Verify all section types render correctly in preview
   - Check that Whop plan IDs display for checkout pages
   - Verify theme colors are applied correctly

3. **State Management**:
   - Open editor for page 2, close it, verify state is cleaned up
   - Open preview for page 1, close it, open preview for page 3
   - Edit a page, save, then preview the same page to see changes

4. **Edge Cases**:
   - Preview a page with no sections
   - Edit a page and close without saving
   - Rapidly switch between edit and preview modes

## Files Modified
1. `components/editor/FunnelEditor.tsx` - Added initialPageIndex prop
2. `components/FunnelBuilder.tsx` - Added page-specific edit/preview functionality
3. `components/PagePreview.tsx` - NEW - Created preview modal component

## Conclusion

The implementation successfully resolves SPARK-304 by providing users with the ability to edit and preview individual funnel pages. The solution is cleanly integrated into the existing codebase, uses the established component patterns, and provides an intuitive user experience.
