{"name": "funnelflow-ai", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "whop-proxy --command 'next dev --turbopack'", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@frosted-ui/icons": "0.0.1-canary.20", "@google/genai": "^1.0.0", "@hugeicons/core-free-icons": "^1.2.1", "@hugeicons/react": "^1.1.1", "@supabase/supabase-js": "^2.75.1", "@vercel/functions": "^2.0.3", "@whop/api": "^0.0.50", "@whop/react": "0.2.36", "@whop/sdk": "0.0.1-canary.0", "frosted-ui": "0.0.1-canary.77", "next": "15.3.2", "openai": "^6.4.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@whop-apps/dev-proxy": "0.0.1-canary.116", "dotenv-cli": "^8.0.0", "eslint": "^9.37.0", "eslint-config-next": "^15.5.5", "tailwindcss": "^4", "typescript": "^5"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531"}