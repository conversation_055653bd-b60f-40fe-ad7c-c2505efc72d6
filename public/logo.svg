<svg width="381" height="357" viewBox="0 0 381 357" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<rect width="381" height="357" rx="12" fill="url(#paint0_linear_4_2)"/>
<g filter="url(#filter0_d_4_2)">
<rect x="40" y="157.773" width="228.241" height="228.241" rx="12" transform="rotate(-43.7296 40 157.773)" fill="url(#pattern0_4_2)" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_4_2" x="40.9664" y="4.96637" width="320.77" height="320.77" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4_2" result="shape"/>
</filter>
<pattern id="pattern0_4_2" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_4_2" transform="scale(0.01)"/>
</pattern>
<linearGradient id="paint0_linear_4_2" x1="190.5" y1="0" x2="190.5" y2="357" gradientUnits="userSpaceOnUse">
<stop stop-color="#3564FF"/>
<stop offset="1" stop-color="#6DB1FF"/>
</linearGradient>
<image id="image0_4_2" width="100" height="100" preserveAspectRatio="none" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAACx0lEQVR4nO3dv68NQRjG8e0uCRGFhkbUaCR+JBqFUmhUQi0aiRBuhU4l0WqI0CgpSTQKREeoVC7NGURJ8JWTjOZc4tw5++PZeZ/PX7D3PuedfXd2Z6ZpzMzMzMzMzKwCwG7gMfAVeA1cAjYMfV0hAduAz6yWgCvApqGvMRTg6l/CcDBDAW4zn4mHsh7kX/9aOJguASco46GsC8C+wkAcTBeAzQsG4mDaBnxqKRQH0wbgeYuBOJhFAXc7COQPB9ND61siOZjuW18HI9r6OhjR1tfBiLa+JRxMD61vCQfTQ+tbInYwPbW+JWIG03PrWyJWMAO1viViBDNw61ui/mAEWt8S9QYj1PqWqC8YwdY3djDCrW/MYIBz1Gcyuq9kgPXANeAH9UqjqBjgAPCWOJJkMEGqYhzBBKwKzWBcFULBuCqEgsmVcTovPbgJPMhP5e+Bb/NfaygTYBnY2Fkw/wlsB3AQOA6czUPbHeAh8BL4CPwkniRz858FLAFbgT3AEeAUcBG4AdwHngLvKq26JBvMPIAtwE7gMHASOA9cz/NlT4A3wBfGO5SN58l/LYB1wPbcdBwFzszc617ke913tKwA+5vIWF11F3LV3Ruo6j5Mf1BD/1/k0e+97tDQf+/YhsFj/xgGV1oaBuMGgt6QNQ11qakJ472pT69pbzMW1Nv2Jpm21w+G9PNguIapk1/Ek1oPwpOLCw1N3Uwuevpdc2iadjR+QSX45tDVojaL62pBI4hZAaslSQYRsFqSfBCBPiVdHuTd+KL8sbUYL0cQ4wU7YrykTYgXfYrxsmgx3jhAjHDLm0IFIdzyppBBCLa8KXQQQi1vchCZt/gT400wxXibWDHeSFmMtxoXAzxzEEJ8XIUQH+gixkceifGhYGKAy9VvCDYmwC0HMa6HQgfRp7wiyocTKwF2AY/y8d2vXBFmZmZmZmZm1lTnN8huqNegRfX8AAAAAElFTkSuQmCC"/>
</defs>
</svg>
