// Simple in-memory rate limiter
// In production, use Redis or similar for distributed systems

interface RateLimitEntry {
	count: number;
	resetAt: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

export interface RateLimitConfig {
	maxRequests: number;
	windowMs: number;
}

export function checkRateLimit(
	key: string,
	config: RateLimitConfig = {
		maxRequests: 10,
		windowMs: 60 * 60 * 1000, // 1 hour
	},
): { allowed: boolean; remaining: number; resetAt: number } {
	const now = Date.now();
	const entry = rateLimitStore.get(key);

	// Clean up expired entries
	if (entry && now >= entry.resetAt) {
		rateLimitStore.delete(key);
	}

	const currentEntry = rateLimitStore.get(key);

	if (!currentEntry) {
		// First request
		const resetAt = now + config.windowMs;
		rateLimitStore.set(key, { count: 1, resetAt });
		return { allowed: true, remaining: config.maxRequests - 1, resetAt };
	}

	if (currentEntry.count >= config.maxRequests) {
		return {
			allowed: false,
			remaining: 0,
			resetAt: currentEntry.resetAt,
		};
	}

	// Increment count
	currentEntry.count += 1;
	rateLimitStore.set(key, currentEntry);

	return {
		allowed: true,
		remaining: config.maxRequests - currentEntry.count,
		resetAt: currentEntry.resetAt,
	};
}

// Cleanup old entries every 5 minutes
setInterval(
	() => {
		const now = Date.now();
		for (const [key, entry] of rateLimitStore.entries()) {
			if (now >= entry.resetAt) {
				rateLimitStore.delete(key);
			}
		}
	},
	5 * 60 * 1000,
);
