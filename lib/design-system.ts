// Universal Design System for FunnelFlow AI
// Enforces consistent styling, structure, and content rules across all components

export const DESIGN_SYSTEM = {
  // Color Palette
  colors: {
    primary: "#3b82f6",
    primaryHover: "#2563eb",
    primaryLight: "#eff6ff",
    secondary: "#6b7280",
    success: "#10b981",
    warning: "#f59e0b",
    error: "#ef4444",
    text: {
      primary: "#1f2937",
      secondary: "#6b7280",
      light: "#9ca3af",
      white: "#ffffff"
    },
    background: {
      white: "#ffffff",
      light: "#f9fafb",
      lighter: "#f3f4f6",
      dark: "#1f2937"
    },
    border: {
      light: "#f1f5f9",
      medium: "#e5e7eb",
      dark: "#d1d5db"
    }
  },

  // Typography Scale
  typography: {
    hero: {
      fontSize: "56px",
      fontWeight: "800",
      lineHeight: "1.1",
      letterSpacing: "-0.02em",
      maxWords: 7 // STRICT RULE: Hero headlines max 7 words
    },
    h1: {
      fontSize: "48px",
      fontWeight: "800",
      lineHeight: "1.2",
      letterSpacing: "-0.02em"
    },
    h2: {
      fontSize: "36px",
      fontWeight: "700",
      lineHeight: "1.3",
      letterSpacing: "-0.01em"
    },
    h3: {
      fontSize: "24px",
      fontWeight: "600",
      lineHeight: "1.4"
    },
    body: {
      fontSize: "16px",
      fontWeight: "400",
      lineHeight: "1.6"
    },
    bodyLarge: {
      fontSize: "18px",
      fontWeight: "400",
      lineHeight: "1.6"
    },
    caption: {
      fontSize: "14px",
      fontWeight: "500",
      lineHeight: "1.5"
    }
  },

  // Spacing System
  spacing: {
    xs: "8px",
    sm: "12px",
    md: "16px",
    lg: "24px",
    xl: "32px",
    xxl: "48px",
    xxxl: "64px",
    section: "80px", // Standard section padding
    container: "1200px" // Max container width
  },

  // Component Rules
  components: {
    hero: {
      maxHeadlineWords: 7,
      structure: ["headline", "subheadline", "cta", "video"],
      padding: "80px 24px",
      minHeight: "600px",
      ctaSpacing: "40px"
    },
    features: {
      maxFeatures: 6,
      iconSize: "40px",
      cardPadding: "32px 24px",
      gridGap: "32px",
      titleMaxWords: 4
    },
    testimonials: {
      maxTestimonials: 6,
      avatarSize: "48px",
      quoteMaxWords: 25,
      nameMaxWords: 3
    },
    pricing: {
      maxPlans: 4,
      cardPadding: "40px 32px",
      featureMaxWords: 6,
      planNameMaxWords: 3
    },
    form: {
      maxFields: 6,
      inputHeight: "48px",
      buttonHeight: "48px",
      fieldSpacing: "16px"
    }
  },

  // Button Styles
  buttons: {
    primary: {
      background: "#3b82f6",
      color: "#ffffff",
      padding: "16px 32px",
      borderRadius: "8px",
      fontSize: "16px",
      fontWeight: "600",
      border: "none",
      cursor: "pointer",
      transition: "all 0.2s ease",
      hover: {
        background: "#2563eb",
        transform: "translateY(-1px)"
      }
    },
    secondary: {
      background: "transparent",
      color: "#3b82f6",
      padding: "16px 32px",
      borderRadius: "8px",
      fontSize: "16px",
      fontWeight: "600",
      border: "2px solid #3b82f6",
      cursor: "pointer",
      transition: "all 0.2s ease",
      hover: {
        background: "#3b82f6",
        color: "#ffffff"
      }
    }
  },

  // Card Styles
  cards: {
    default: {
      background: "#ffffff",
      borderRadius: "12px",
      border: "1px solid #f1f5f9",
      padding: "32px 24px",
      boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
      transition: "all 0.2s ease",
      hover: {
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
        transform: "translateY(-2px)"
      }
    },
    featured: {
      background: "#ffffff",
      borderRadius: "12px",
      border: "2px solid #3b82f6",
      padding: "40px 32px",
      boxShadow: "0 8px 25px rgba(59, 130, 246, 0.15)",
      position: "relative"
    }
  },

  // Icon System
  icons: {
    size: {
      sm: "16px",
      md: "24px",
      lg: "32px",
      xl: "40px"
    },
    container: {
      background: "#3b82f6",
      borderRadius: "8px",
      padding: "12px",
      color: "#ffffff"
    }
  }
};

// Content validation functions
export const validateContent = {
  heroHeadline: (text: string): boolean => {
    const words = text.trim().split(/\s+/);
    return words.length <= DESIGN_SYSTEM.components.hero.maxHeadlineWords;
  },
  
  featureTitle: (text: string): boolean => {
    const words = text.trim().split(/\s+/);
    return words.length <= DESIGN_SYSTEM.components.features.titleMaxWords;
  },
  
  testimonialQuote: (text: string): boolean => {
    const words = text.trim().split(/\s+/);
    return words.length <= DESIGN_SYSTEM.components.testimonials.quoteMaxWords;
  }
};

// Helper functions for applying styles
export const getComponentStyles = (componentType: string, variant?: string) => {
  const base = DESIGN_SYSTEM.components[componentType as keyof typeof DESIGN_SYSTEM.components];
  return base;
};

export const getTypographyStyle = (type: keyof typeof DESIGN_SYSTEM.typography) => {
  return DESIGN_SYSTEM.typography[type];
};

export const getColorStyle = (colorPath: string) => {
  const keys = colorPath.split('.');
  let value: Record<string, unknown> = DESIGN_SYSTEM.colors;
  for (const key of keys) {
    value = value[key] as Record<string, unknown>;
  }
  return value;
};
