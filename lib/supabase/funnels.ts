/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { supabase } from './client';
import type { Database } from './types';
import type { FunnelSchema } from '@/lib/types/funnel';

export interface SavedFunnel extends FunnelSchema {
  id: string;
  createdAt: string;
  publishedAt?: string;
  status: 'draft' | 'published';
  slug?: string;
  stats?: {
    visits: number;
    conversions: number;
    revenue: number;
  };
}

/**
 * Ensure user exists in database
 */
export async function ensureUser(params: {
  userId: string;
  companyId: string;
  userName?: string;
  username?: string;
  companyTitle?: string;
}) {
  const { userId, companyId, userName, username, companyTitle } = params;

  // Check if user exists
  const { data: existingUser, error: fetchError } = await supabase
    .from('users')
    .select('id')
    .eq('whop_user_id', userId)
    .maybeSingle<{ id: string }>();

  if (existingUser && !fetchError) {
    return existingUser.id;
  }

  // Create new user
  const insertData: Database['public']['Tables']['users']['Insert'] = {
    whop_user_id: userId,
    whop_company_id: companyId,
    user_name: userName || null,
    username: username || null,
    company_title: companyTitle || null,
  };
  
  const usersTable = supabase.from('users');
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore - Supabase client typing issue with insert
  const insertQuery = usersTable.insert(insertData);
  const { data: newUser, error } = await insertQuery
    .select('id')
    .single<{ id: string }>();

  if (error) {
    console.error('Error creating user:', error);
    throw error;
  }

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore - Type narrowing issue
  return newUser.id;
}

/**
 * Get all funnels for a company
 */
export async function getFunnels(companyId: string): Promise<SavedFunnel[]> {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore - Supabase typing issue
  const { data, error } = await supabase
    .from('funnels')
    .select('*')
    .eq('whop_company_id', companyId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching funnels:', error);
    throw error;
  }

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore - Supabase typing issue
  return data.map((funnel) => ({
    id: funnel.id,
    title: funnel.title,
    description: funnel.description || '',
    pages: funnel.pages as unknown as FunnelSchema['pages'],
    sections: funnel.sections as unknown as FunnelSchema['sections'],
    theme: funnel.theme as unknown as FunnelSchema['theme'],
    layout: funnel.layout,
    status: funnel.status,
    createdAt: funnel.created_at,
    publishedAt: funnel.published_at || undefined,
    slug: funnel.slug || undefined,
    stats: {
      visits: funnel.visits,
      conversions: funnel.conversions,
      revenue: Number(funnel.revenue),
    },
  }));
}

/**
 * Get a single funnel by ID
 */
export async function getFunnel(funnelId: string): Promise<SavedFunnel | null> {
  const { data, error } = await supabase
    .from('funnels')
    .select('*')
    .eq('id', funnelId)
    .single();

  if (error) {
    console.error('Error fetching funnel:', error);
    return null;
  }

  return {
    id: data.id,
    title: data.title,
    description: data.description || '',
    pages: data.pages as unknown as FunnelSchema['pages'],
    sections: data.sections as unknown as FunnelSchema['sections'],
    theme: data.theme as unknown as FunnelSchema['theme'],
    layout: data.layout,
    status: data.status,
    createdAt: data.created_at,
    publishedAt: data.published_at || undefined,
    slug: data.slug || undefined,
    stats: {
      visits: data.visits,
      conversions: data.conversions,
      revenue: Number(data.revenue),
    },
  };
}

/**
 * Create a new funnel
 */
export async function createFunnel(
  companyId: string,
  userId: string,
  funnel: FunnelSchema,
  status: 'draft' | 'published' = 'draft'
): Promise<SavedFunnel> {
  // Ensure user exists first
  const dbUserId = await ensureUser({
    userId,
    companyId,
  });

  const { data, error } = await supabase
    .from('funnels')
    .insert({
      user_id: dbUserId,
      whop_company_id: companyId,
      title: funnel.title,
      description: funnel.description,
      pages: funnel.pages as unknown as Database['public']['Tables']['funnels']['Insert']['pages'],
      sections: funnel.sections as unknown as Database['public']['Tables']['funnels']['Insert']['sections'],
      theme: funnel.theme as unknown as Database['public']['Tables']['funnels']['Insert']['theme'],
      layout: funnel.layout,
      status,
      published_at: status === 'published' ? new Date().toISOString() : null,
    })
    .select('*')
    .single();

  if (error) {
    console.error('Error creating funnel:', error);
    throw error;
  }

  return {
    id: data.id,
    title: data.title,
    description: data.description || '',
    pages: data.pages as unknown as FunnelSchema['pages'],
    sections: data.sections as unknown as FunnelSchema['sections'],
    theme: data.theme as unknown as FunnelSchema['theme'],
    layout: data.layout,
    status: data.status,
    createdAt: data.created_at,
    publishedAt: data.published_at || undefined,
    slug: data.slug || undefined,
    stats: {
      visits: data.visits,
      conversions: data.conversions,
      revenue: Number(data.revenue),
    },
  };
}

/**
 * Update an existing funnel
 */
export async function updateFunnel(
  funnelId: string,
  updates: Partial<FunnelSchema> & { status?: 'draft' | 'published' }
): Promise<SavedFunnel> {
  const updateData: Partial<Database['public']['Tables']['funnels']['Update']> = {};

  if (updates.title !== undefined) updateData.title = updates.title;
  if (updates.description !== undefined) updateData.description = updates.description;
  if (updates.pages !== undefined) updateData.pages = updates.pages as unknown as Database['public']['Tables']['funnels']['Update']['pages'];
  if (updates.sections !== undefined) updateData.sections = updates.sections as unknown as Database['public']['Tables']['funnels']['Update']['sections'];
  if (updates.theme !== undefined) updateData.theme = updates.theme as unknown as Database['public']['Tables']['funnels']['Update']['theme'];
  if (updates.layout !== undefined) updateData.layout = updates.layout;
  // @ts-expect-error slug exists in DB Update type
  if ((updates as any).slug !== undefined) (updateData as any).slug = (updates as any).slug;
  if (updates.status !== undefined) {
    updateData.status = updates.status;
    if (updates.status === 'published') {
      updateData.published_at = new Date().toISOString();
    }
  }

  const { data, error } = await supabase
    .from('funnels')
    .update(updateData)
    .eq('id', funnelId)
    .select('*')
    .single();

  if (error) {
    console.error('Error updating funnel:', error);
    throw error;
  }

  return {
    id: data.id,
    title: data.title,
    description: data.description || '',
    pages: data.pages as unknown as FunnelSchema['pages'],
    sections: data.sections as unknown as FunnelSchema['sections'],
    theme: data.theme as unknown as FunnelSchema['theme'],
    layout: data.layout,
    status: data.status,
    createdAt: data.created_at,
    publishedAt: data.published_at || undefined,
    slug: data.slug || undefined,
    stats: {
      visits: data.visits,
      conversions: data.conversions,
      revenue: Number(data.revenue),
    },
  };
}

/**
 * Delete a funnel
 */
export async function deleteFunnel(funnelId: string): Promise<void> {
  const { error } = await supabase
    .from('funnels')
    .delete()
    .eq('id', funnelId);

  if (error) {
    console.error('Error deleting funnel:', error);
    throw error;
  }
}

/**
 * Publish a funnel
 */
export async function publishFunnel(funnelId: string): Promise<SavedFunnel> {
  return updateFunnel(funnelId, { status: 'published' });
}

/**
 * Unpublish a funnel (set to draft)
 */
export async function unpublishFunnel(funnelId: string): Promise<SavedFunnel> {
  return updateFunnel(funnelId, { status: 'draft' });
}
