/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { supabase } from './client';
import type { Database } from './types';

export interface FunnelDomain {
  id: string;
  funnelId: string;
  whopCompanyId: string;
  hostname: string;
  status: 'pending' | 'active' | 'disabled';
  verificationToken: string;
  createdAt: string;
  updatedAt: string;
}

export async function addDomain(params: {
  funnelId: string;
  whopCompanyId: string;
  hostname: string;
}): Promise<FunnelDomain> {
  const insertData: Database['public']['Tables']['funnel_domains']['Insert'] = {
    funnel_id: params.funnelId,
    whop_company_id: params.whopCompanyId,
    hostname: params.hostname.toLowerCase(),
    status: 'pending',
  };

  const { data, error } = await supabase
    .from('funnel_domains')
    // @ts-ignore insert typing
    .insert(insertData)
    .select('*')
    .single();

  if (error) {
    console.error('Error adding domain:', error);
    throw error;
  }

  return mapDomainRow(data);
}

export async function listDomains(funnelId: string): Promise<FunnelDomain[]> {
  const { data, error } = await supabase
    .from('funnel_domains')
    .select('*')
    .eq('funnel_id', funnelId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error listing domains:', error);
    throw error;
  }

  return (data || []).map(mapDomainRow);
}

export async function removeDomain(domainId: string): Promise<void> {
  const { error } = await supabase
    .from('funnel_domains')
    .delete()
    .eq('id', domainId);

  if (error) {
    console.error('Error removing domain:', error);
    throw error;
  }
}

export async function setDomainStatus(domainId: string, status: 'pending' | 'active' | 'disabled'): Promise<FunnelDomain> {
  const { data, error } = await supabase
    .from('funnel_domains')
    .update({ status })
    .eq('id', domainId)
    .select('*')
    .single();

  if (error) {
    console.error('Error updating domain status:', error);
    throw error;
  }

  return mapDomainRow(data);
}

export async function findFunnelByHostname(hostname: string): Promise<{ funnelId: string; whopCompanyId: string } | null> {
  const host = hostname.toLowerCase();
  const { data, error } = await supabase
    .from('funnel_domains')
    .select('funnel_id, whop_company_id, status')
    .eq('hostname', host)
    .maybeSingle();

  if (error) {
    console.error('Error finding funnel by hostname:', error);
    return null;
  }

  if (!data || data.status !== 'active') return null;

  return { funnelId: data.funnel_id as string, whopCompanyId: data.whop_company_id as string };
}

function mapDomainRow(row: Database['public']['Tables']['funnel_domains']['Row']): FunnelDomain {
  return {
    id: row.id,
    funnelId: row.funnel_id,
    whopCompanyId: row.whop_company_id,
    hostname: row.hostname,
    status: row.status,
    verificationToken: row.verification_token,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
  };
}
