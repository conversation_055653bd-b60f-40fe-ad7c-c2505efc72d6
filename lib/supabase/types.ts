// Supabase database types
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          whop_user_id: string
          whop_company_id: string
          user_name: string | null
          username: string | null
          company_title: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          whop_user_id: string
          whop_company_id: string
          user_name?: string | null
          username?: string | null
          company_title?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          whop_user_id?: string
          whop_company_id?: string
          user_name?: string | null
          username?: string | null
          company_title?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      funnels: {
        Row: {
          id: string
          user_id: string | null
          whop_company_id: string
          title: string
          description: string | null
          slug: string | null
          status: 'draft' | 'published'
          pages: Json
          sections: Json
          theme: Json
          layout: 'single-page' | 'multi-step'
          visits: number
          conversions: number
          revenue: number
          created_at: string
          updated_at: string
          published_at: string | null
        }
        Insert: {
          id?: string
          user_id?: string | null
          whop_company_id: string
          title: string
          description?: string | null
          slug?: string | null
          status?: 'draft' | 'published'
          pages?: Json
          sections?: Json
          theme?: Json
          layout?: 'single-page' | 'multi-step'
          visits?: number
          conversions?: number
          revenue?: number
          created_at?: string
          updated_at?: string
          published_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string | null
          whop_company_id?: string
          title?: string
          description?: string | null
          slug?: string | null
          status?: 'draft' | 'published'
          pages?: Json
          sections?: Json
          theme?: Json
          layout?: 'single-page' | 'multi-step'
          visits?: number
          conversions?: number
          revenue?: number
          created_at?: string
          updated_at?: string
          published_at?: string | null
        }
      }
      funnel_domains: {
        Row: {
          id: string
          funnel_id: string
          whop_company_id: string
          hostname: string
          status: 'pending' | 'active' | 'disabled'
          verification_token: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          funnel_id: string
          whop_company_id: string
          hostname: string
          status?: 'pending' | 'active' | 'disabled'
          verification_token?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          funnel_id?: string
          whop_company_id?: string
          hostname?: string
          status?: 'pending' | 'active' | 'disabled'
          verification_token?: string
          created_at?: string
          updated_at?: string
        }
      }
      feature_templates: {
        Row: {
          id: string
          user_id: string | null
          whop_company_id: string
          name: string
          properties: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          whop_company_id: string
          name: string
          properties: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          whop_company_id?: string
          name?: string
          properties?: Json
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
