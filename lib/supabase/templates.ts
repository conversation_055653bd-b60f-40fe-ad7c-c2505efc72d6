/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { supabase } from './client';
import type { Database } from './types';
import { ensureUser } from './funnels';

export interface FeatureTemplate {
  id: string;
  name: string;
  properties: Record<string, unknown>;
  createdAt: string;
}

/**
 * Get all feature templates for a company
 */
export async function getFeatureTemplates(companyId: string): Promise<FeatureTemplate[]> {
  const { data, error } = await supabase
    .from('feature_templates')
    .select('*')
    .eq('whop_company_id', companyId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching templates:', error);
    throw error;
  }

  return data.map((template) => ({
    id: template.id,
    name: template.name,
    properties: template.properties as Record<string, unknown>,
    createdAt: template.created_at,
  }));
}

/**
 * Create a new feature template
 */
export async function createFeatureTemplate(
  companyId: string,
  userId: string,
  name: string,
  properties: Record<string, unknown>
): Promise<FeatureTemplate> {
  // Ensure user exists first
  const dbUserId = await ensureUser({
    userId,
    companyId,
  });

  const { data, error } = await supabase
    .from('feature_templates')
    .insert({
      user_id: dbUserId,
      whop_company_id: companyId,
      name,
      properties: properties as unknown as Database['public']['Tables']['feature_templates']['Insert']['properties'],
    })
    .select('*')
    .single();

  if (error) {
    console.error('Error creating template:', error);
    throw error;
  }

  return {
    id: data.id,
    name: data.name,
    properties: data.properties as Record<string, unknown>,
    createdAt: data.created_at,
  };
}

/**
 * Delete a feature template
 */
export async function deleteFeatureTemplate(templateId: string): Promise<void> {
  const { error } = await supabase
    .from('feature_templates')
    .delete()
    .eq('id', templateId);

  if (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
}
