import type { ComponentCategory, ComponentDefinition, EditorComponent } from "@/lib/types/editor";

export const componentDefinitions: ComponentDefinition[] = [
	// Layout / Container Components
	{
		type: "hero_section",
		name: "Hero Section",
		icon: "🦸",
		category: "layout",
		defaultProperties: {
			title: "Build Funnels That Convert",
			subtitle: "Create high-converting sales funnels with AI-powered tools and proven templates.",
			ctaText: "Start Free Trial",
			ctaUrl: "#",
			backgroundImage: "",
			backgroundVideo: "",
			backgroundColor: "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
			textColor: "#ffffff",
			height: "700px",
			overlay: true,
			overlayOpacity: 0.4,
			overlayColor: "#000000",
			videoUrl: "",
			layout: "centered",
			logoUrl: "",
			tagline: "Trusted by 10,000+ SaaS Founders",
			animationEnabled: true,
			animationType: "fade",
		},
	},
	{
		type: "features_section",
		name: "Features Section",
		icon: "⭐",
		category: "layout",
		defaultProperties: {
			heading: "Powerful Features",
			subheading: "Everything you need to succeed",
			features: [
				{
					title: "Feature 1",
					description: "Powerful tools to help you succeed",
					icon: "⚡",
					iconUrl: "",
					ctaText: "",
					ctaUrl: "",
				},
				{
					title: "Feature 2",
					description: "Advanced analytics and insights",
					icon: "📊",
					iconUrl: "",
					ctaText: "",
					ctaUrl: "",
				},
				{
					title: "Feature 3",
					description: "24/7 customer support",
					icon: "💬",
					iconUrl: "",
					ctaText: "",
					ctaUrl: "",
				},
			],
			columns: 3,
			layout: "grid",
			backgroundColor: "#ffffff",
			backgroundPattern: "",
			textColor: "#111827",
		},
	},
	{
		type: "pricing_section",
		name: "Pricing Section",
		icon: "💰",
		category: "layout",
		defaultProperties: {
			heading: "Simple, Transparent Pricing",
			subheading: "Choose the perfect plan for your needs",
			plans: [
				{
					name: "Starter",
					price: "$29",
					priceMonthly: "$29",
					priceYearly: "$290",
					period: "/month",
					description: "Perfect for getting started",
					features: ["5 Projects", "10GB Storage", "Email Support", "Basic Analytics"],
					highlighted: false,
					ctaText: "Get Started",
					ctaUrl: "#",
				},
				{
					name: "Professional",
					price: "$79",
					priceMonthly: "$79",
					priceYearly: "$790",
					period: "/month",
					description: "For growing businesses",
					features: ["Unlimited Projects", "100GB Storage", "Priority Support", "Advanced Analytics"],
					highlighted: true,
					ctaText: "Get Started",
					ctaUrl: "#",
				},
				{
					name: "Enterprise",
					price: "$199",
					priceMonthly: "$199",
					priceYearly: "$1990",
					period: "/month",
					description: "For large organizations",
					features: ["Unlimited Everything", "Custom Storage", "24/7 Phone Support", "Custom Integrations"],
					highlighted: false,
					ctaText: "Contact Sales",
					ctaUrl: "#",
				},
			],
			billingToggle: false,
			currency: "$",
			disclaimer: "",
			backgroundColor: "#f9fafb",
			accentColor: "#6366f1",
			textColor: "#111827",
		},
	},
	{
		type: "testimonials_section",
		name: "Testimonials Section",
		icon: "💬",
		category: "layout",
		defaultProperties: {
			heading: "What Our Customers Say",
			subheading: "Don't just take our word for it",
			testimonials: [
				{
					quote: "This product has transformed our business. Highly recommend!",
					author: "John Doe",
					role: "CEO",
					company: "Company A",
					avatar: "",
					companyLogo: "",
					rating: 5,
				},
				{
					quote: "Outstanding support and incredible features. Worth every penny!",
					author: "Jane Smith",
					role: "Marketing Director",
					company: "Company B",
					avatar: "",
					companyLogo: "",
					rating: 5,
				},
				{
					quote: "The best investment we've made this year. Amazing results!",
					author: "Mike Johnson",
					role: "Founder",
					company: "Startup C",
					avatar: "",
					companyLogo: "",
					rating: 5,
				},
			],
			columns: 3,
			layout: "grid",
			backgroundColor: "#ffffff",
			backgroundPattern: "",
			textColor: "#111827",
		},
	},
	{
		type: "form_section",
		name: "Form Section",
		icon: "📝",
		category: "layout",
		defaultProperties: {
			heading: "Get In Touch",
			subheading: "We'd love to hear from you",
			submitText: "Send Message",
			submitUrl: "#",
			fields: [
				{ type: "text", label: "Name", placeholder: "Your name", required: true },
				{ type: "email", label: "Email", placeholder: "<EMAIL>", required: true },
				{ type: "textarea", label: "Message", placeholder: "Your message", required: true },
			],
			successMessage: "Thank you! Your message has been sent successfully.",
			errorMessage: "Oops! Something went wrong. Please try again.",
			integration: "",
			privacyText: "By submitting this form, you agree to our Privacy Policy",
			privacyUrl: "/privacy",
			backgroundColor: "#f9fafb",
			containerStyle: "default",
			textColor: "#111827",
		},
	},
	{
		type: "footer_section",
		name: "Footer Section",
		icon: "📄",
		category: "layout",
		defaultProperties: {
			companyName: "Your Company",
			logoUrl: "",
			tagline: "Building the future",
			columns: [
				{
					title: "Product",
					links: [
						{ label: "Features", url: "#" },
						{ label: "Pricing", url: "#" },
						{ label: "Templates", url: "#" },
						{ label: "Integrations", url: "#" },
					],
				},
				{
					title: "Company",
					links: [
						{ label: "About", url: "#" },
						{ label: "Blog", url: "#" },
						{ label: "Careers", url: "#" },
						{ label: "Contact", url: "#" },
					],
				},
				{
					title: "Legal",
					links: [
						{ label: "Privacy", url: "#" },
						{ label: "Terms", url: "#" },
						{ label: "Security", url: "#" },
					],
				},
			],
			socialLinks: [
				{ platform: "twitter", url: "#" },
				{ platform: "facebook", url: "#" },
				{ platform: "linkedin", url: "#" },
			],
			contactEmail: "",
			contactPhone: "",
			contactAddress: "",
			newsletterEnabled: false,
			newsletterTitle: "Subscribe to our newsletter",
			newsletterPlaceholder: "Enter your email",
			newsletterButtonText: "Subscribe",
			backgroundColor: "#111827",
			textColor: "#ffffff",
			copyright: "© 2024 Your Company. All rights reserved.",
			layout: "columns",
		},
	},
	{
		type: "section",
		name: "Section",
		icon: "⬜",
		category: "layout",
		defaultProperties: {
			background: "#ffffff",
			padding: "80px 40px",
			maxWidth: "1200px",
			alignment: "center",
		},
	},
	{
		type: "row",
		name: "Row",
		icon: "▭",
		category: "layout",
		defaultProperties: {
			columns: 2,
			gap: "20px",
			alignment: "top",
		},
	},
	{
		type: "column",
		name: "Column",
		icon: "▯",
		category: "layout",
		defaultProperties: {
			width: "100%",
			padding: "0px",
		},
	},
	{
		type: "flex_container",
		name: "Flex Container",
		icon: "⬚",
		category: "layout",
		defaultProperties: {
			direction: "row",
			justifyContent: "start",
			alignItems: "start",
			gap: "16px",
		},
	},
	{
		type: "popup",
		name: "Popup (Modal)",
		icon: "🗖",
		category: "layout",
		defaultProperties: {
			trigger: "click",
			delay: 0,
			width: "600px",
			title: "Modal Title",
		},
	},
	{
		type: "universal_block",
		name: "Universal Block",
		icon: "▢",
		category: "layout",
		defaultProperties: {
			className: "",
			style: {},
		},
	},

	// Content & Functional Elements
	{
		type: "headline",
		name: "Headline",
		icon: "H",
		category: "content",
		defaultProperties: {
			text: "Your Headline Here",
			level: "h2",
			alignment: "center",
			color: "#111827",
			fontSize: "48px",
			fontWeight: "bold",
			letterSpacing: "-0.02em",
			lineHeight: "1.2",
		},
	},
	{
		type: "sub_headline",
		name: "Sub-Headline",
		icon: "h",
		category: "content",
		defaultProperties: {
			text: "Your Sub-Headline Here",
			alignment: "center",
			color: "#6b7280",
			fontSize: "20px",
			fontWeight: "500",
			lineHeight: "1.5",
			maxWidth: "700px",
		},
	},
	{
		type: "paragraph",
		name: "Paragraph / Text Block",
		icon: "¶",
		category: "content",
		defaultProperties: {
			text: "Your paragraph text goes here. Edit this to add your content.",
			alignment: "left",
			color: "#374151",
			fontSize: "16px",
			lineHeight: "1.6",
		},
	},
	{
		type: "image",
		name: "Image",
		icon: "🖼",
		category: "content",
		defaultProperties: {
			src: "https://via.placeholder.com/800x400",
			alt: "Image description",
			width: "100%",
			height: "auto",
			objectFit: "cover",
			alignment: "center",
		},
	},
	{
		type: "video",
		name: "Video",
		icon: "▶",
		category: "content",
		defaultProperties: {
			src: "",
			poster: "",
			autoplay: false,
			controls: true,
			loop: false,
			width: "100%",
			height: "auto",
		},
	},
	{
		type: "audio",
		name: "Audio Player",
		icon: "🔊",
		category: "content",
		defaultProperties: {
			src: "",
			controls: true,
			autoplay: false,
			loop: false,
		},
	},
	{
		type: "button",
		name: "Button",
		icon: "🔘",
		category: "content",
		defaultProperties: {
			text: "Get Started",
			url: "#",
			variant: "solid",
			size: "lg",
			color: "#6366f1",
			fullWidth: false,
			alignment: "center",
			borderRadius: "8px",
			padding: "14px 32px",
			fontWeight: "600",
		},
	},
	{
		type: "divider",
		name: "Divider / Line Break",
		icon: "―",
		category: "content",
		defaultProperties: {
			style: "solid",
			thickness: "1px",
			color: "#e5e7eb",
			spacing: "20px",
		},
	},
	{
		type: "icon",
		name: "Icon",
		icon: "★",
		category: "content",
		defaultProperties: {
			name: "star",
			size: "32px",
			color: "#6366f1",
			alignment: "center",
		},
	},
	{
		type: "countdown_timer",
		name: "Countdown Timer",
		icon: "⏱",
		category: "content",
		defaultProperties: {
			targetDate: new Date(Date.now() + 86400000).toISOString(),
			format: "dhms",
			alignment: "center",
			size: "24px",
		},
	},
	{
		type: "progress_bar",
		name: "Progress Bar",
		icon: "▬",
		category: "content",
		defaultProperties: {
			value: 50,
			max: 100,
			color: "#6366f1",
			height: "20px",
			showLabel: true,
		},
	},
	{
		type: "spacer",
		name: "Spacer",
		icon: "⇅",
		category: "content",
		defaultProperties: {
			height: "40px",
		},
	},
	{
		type: "social_share",
		name: "Social Share",
		icon: "📤",
		category: "content",
		defaultProperties: {
			platforms: ["facebook", "twitter", "linkedin"],
			url: "",
			title: "",
			alignment: "center",
		},
	},
	{
		type: "custom_html",
		name: "Custom HTML / JavaScript",
		icon: "</>",
		category: "content",
		defaultProperties: {
			html: "<div>Your custom HTML here</div>",
		},
	},
	{
		type: "menu",
		name: "Menu / Navigation",
		icon: "☰",
		category: "content",
		defaultProperties: {
			items: [
				{ label: "Home", url: "/" },
				{ label: "About", url: "/about" },
				{ label: "Contact", url: "/contact" },
			],
			orientation: "horizontal",
			alignment: "center",
		},
	},
	{
		type: "link_list",
		name: "Link List",
		icon: "🔗",
		category: "content",
		defaultProperties: {
			items: [
				{ label: "Link 1", url: "#", description: "Description 1" },
				{ label: "Link 2", url: "#", description: "Description 2" },
			],
		},
	},

	// Form & Input Elements
	{
		type: "input_field",
		name: "Input Field",
		icon: "📝",
		category: "form",
		defaultProperties: {
			label: "Input Label",
			placeholder: "Enter your text",
			fieldType: "text",
			required: false,
			name: "input_field",
		},
	},
	{
		type: "text_area",
		name: "Text Area",
		icon: "📄",
		category: "form",
		defaultProperties: {
			label: "Message",
			placeholder: "Enter your message",
			rows: 4,
			required: false,
			name: "message",
		},
	},
	{
		type: "select_box",
		name: "Select Box (Dropdown)",
		icon: "▼",
		category: "form",
		defaultProperties: {
			label: "Select an option",
			options: [
				{ value: "option1", label: "Option 1" },
				{ value: "option2", label: "Option 2" },
			],
			required: false,
			name: "select",
		},
	},
	{
		type: "radio_buttons",
		name: "Radio Buttons",
		icon: "◉",
		category: "form",
		defaultProperties: {
			label: "Choose one",
			options: [
				{ value: "option1", label: "Option 1" },
				{ value: "option2", label: "Option 2" },
			],
			required: false,
			name: "radio",
		},
	},
	{
		type: "checkbox",
		name: "Checkbox",
		icon: "☑",
		category: "form",
		defaultProperties: {
			label: "I agree to the terms",
			required: false,
			name: "checkbox",
		},
	},
	{
		type: "submit_button",
		name: "Submit Button",
		icon: "✓",
		category: "form",
		defaultProperties: {
			text: "Submit",
			variant: "solid",
			size: "md",
			fullWidth: false,
		},
	},
	{
		type: "hidden_field",
		name: "Hidden Field",
		icon: "👁",
		category: "form",
		defaultProperties: {
			name: "hidden_field",
			value: "",
		},
	},
	{
		type: "search_bar",
		name: "Search Bar",
		icon: "🔍",
		category: "form",
		defaultProperties: {
			placeholder: "Search...",
			buttonText: "Search",
		},
	},

	// Order & Payment Elements
	{
		type: "product_selector",
		name: "Product Selector",
		icon: "🛍",
		category: "payment",
		defaultProperties: {
			products: [
				{
					id: "1",
					name: "Product 1",
					price: 99,
					description: "Product description",
				},
			],
			layout: "grid",
		},
	},
	{
		type: "credit_card_form",
		name: "Credit Card Form",
		icon: "💳",
		category: "payment",
		defaultProperties: {
			showBillingAddress: true,
		},
	},
	{
		type: "order_bump",
		name: "Order Bump",
		icon: "🎁",
		category: "payment",
		defaultProperties: {
			productName: "Additional Product",
			price: 29,
			description: "Add this to your order!",
			imageUrl: "",
		},
	},
	{
		type: "order_summary",
		name: "Order Summary",
		icon: "📋",
		category: "payment",
		defaultProperties: {
			showTax: true,
			showShipping: true,
		},
	},
	{
		type: "two_step_order",
		name: "2-Step Order Form",
		icon: "①②",
		category: "payment",
		defaultProperties: {
			step1Title: "Contact Information",
			step2Title: "Payment Details",
		},
	},
	{
		type: "cart_total",
		name: "Cart Total / Billing Info",
		icon: "💰",
		category: "payment",
		defaultProperties: {
			showBreakdown: true,
		},
	},
	{
		type: "shipping_info",
		name: "Shipping Info",
		icon: "📦",
		category: "payment",
		defaultProperties: {
			requiredFields: ["address", "city", "zip", "country"],
		},
	},

	// Webinar & Automation Elements
	{
		type: "auto_webinar_date",
		name: "Auto Webinar Date",
		icon: "📅",
		category: "webinar",
		defaultProperties: {
			format: "MMMM DD, YYYY",
			timezone: "America/New_York",
		},
	},
	{
		type: "auto_webinar_time",
		name: "Auto Webinar Time",
		icon: "🕐",
		category: "webinar",
		defaultProperties: {
			format: "h:mm A",
			timezone: "America/New_York",
		},
	},
	{
		type: "local_time_display",
		name: "Local Time Display",
		icon: "🌍",
		category: "webinar",
		defaultProperties: {
			format: "h:mm A z",
		},
	},
	{
		type: "webinar_countdown",
		name: "Webinar Countdown",
		icon: "⏰",
		category: "webinar",
		defaultProperties: {
			webinarDate: new Date(Date.now() + 86400000).toISOString(),
			format: "dhms",
		},
	},
	{
		type: "sms_signup",
		name: "SMS Sign-Up",
		icon: "📱",
		category: "webinar",
		defaultProperties: {
			label: "Get SMS Reminders",
			placeholder: "Enter your phone number",
			buttonText: "Sign Up",
		},
	},

	// Content Enhancements
	{
		type: "card",
		name: "Card",
		icon: "🃏",
		category: "enhancement",
		defaultProperties: {
			title: "Card Title",
			description: "Card description goes here. Add your content to make it look professional.",
			imageUrl: "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=400&fit=crop",
			buttonText: "Learn More",
			buttonUrl: "#",
			variant: "elevated",
		},
	},
	{
		type: "bullet_list",
		name: "Bullet List",
		icon: "•",
		category: "enhancement",
		defaultProperties: {
			items: ["Item 1", "Item 2", "Item 3"],
			style: "check",
			color: "#6366f1",
		},
	},
	{
		type: "faq_accordion",
		name: "FAQ Element (Accordion)",
		icon: "❓",
		category: "enhancement",
		defaultProperties: {
			items: [
				{
					question: "What is this?",
					answer: "This is a frequently asked question.",
				},
				{
					question: "How does it work?",
					answer: "It works by clicking to expand and collapse.",
				},
			],
			allowMultipleOpen: false,
		},
	},
	{
		type: "testimonial",
		name: "Testimonial",
		icon: "💬",
		category: "enhancement",
		defaultProperties: {
			quote: "This is an amazing product!",
			author: "John Doe",
			role: "CEO, Company",
			avatar: "",
			rating: 5,
		},
	},
	{
		type: "image_carousel",
		name: "Image Slider / Carousel",
		icon: "🎠",
		category: "enhancement",
		defaultProperties: {
			images: [
				{
					src: "https://via.placeholder.com/800x400",
					alt: "Image 1",
					caption: "",
				},
			],
			autoplay: false,
			interval: 5000,
		},
	},
	{
		type: "custom_form",
		name: "Custom Form",
		icon: "📋",
		category: "enhancement",
		defaultProperties: {
			action: "",
			method: "POST",
		},
	},
	{
		type: "map",
		name: "Map",
		icon: "🗺",
		category: "enhancement",
		defaultProperties: {
			address: "123 Main St, City, State",
			latitude: 40.7128,
			longitude: -74.006,
			zoom: 15,
			height: "400px",
		},
	},
	{
		type: "table",
		name: "Table",
		icon: "⊞",
		category: "enhancement",
		defaultProperties: {
			headers: ["Column 1", "Column 2", "Column 3"],
			rows: [
				["Row 1 Col 1", "Row 1 Col 2", "Row 1 Col 3"],
				["Row 2 Col 1", "Row 2 Col 2", "Row 2 Col 3"],
			],
			striped: true,
			bordered: true,
		},
	},
	{
		type: "embed_code",
		name: "Embed Code",
		icon: "⚡",
		category: "enhancement",
		defaultProperties: {
			code: "<div>Your embed code here</div>",
			height: "auto",
		},
	},
];

export const componentCategories: ComponentCategory[] = [
	{
		id: "layout",
		name: "Layout / Container",
		icon: "🧱",
		components: componentDefinitions.filter((c) => c.category === "layout"),
	},
	{
		id: "content",
		name: "Content & Functional",
		icon: "✏️",
		components: componentDefinitions.filter((c) => c.category === "content"),
	},
	{
		id: "form",
		name: "Form & Input",
		icon: "📋",
		components: componentDefinitions.filter((c) => c.category === "form"),
	},
	{
		id: "payment",
		name: "Order & Payment",
		icon: "💳",
		components: componentDefinitions.filter((c) => c.category === "payment"),
	},
	{
		id: "webinar",
		name: "Webinar & Automation",
		icon: "🎥",
		components: componentDefinitions.filter((c) => c.category === "webinar"),
	},
	{
		id: "enhancement",
		name: "Content Enhancements",
		icon: "💬",
		components: componentDefinitions.filter((c) => c.category === "enhancement"),
	},
];

export function getComponentDefinition(type: string): ComponentDefinition | undefined {
	return componentDefinitions.find((c) => c.type === type);
}

export function createDefaultComponent(type: string): EditorComponent | null {
	const definition = getComponentDefinition(type);
	if (!definition) return null;

	return {
		id: `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
		type: definition.type,
		name: definition.name,
		properties: { ...definition.defaultProperties },
		children: hasChildren(type) ? [] : undefined,
	} as EditorComponent;
}

function hasChildren(type: string): boolean {
	const containerTypes = [
		"section",
		"row",
		"column",
		"flex_container",
		"popup",
		"universal_block",
		"two_step_order",
		"custom_form",
	];
	return containerTypes.includes(type);
}
