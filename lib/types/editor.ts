// Extended component types for drag-and-drop editor

export type ComponentType =
	// Layout / Container Components
	| "hero_section"
	| "features_section"
	| "pricing_section"
	| "testimonials_section"
	| "form_section"
	| "footer_section"
	| "section"
	| "row"
	| "column"
	| "flex_container"
	| "popup"
	| "universal_block"
	// Content & Functional Elements
	| "headline"
	| "sub_headline"
	| "paragraph"
	| "image"
	| "video"
	| "audio"
	| "button"
	| "divider"
	| "icon"
	| "countdown_timer"
	| "progress_bar"
	| "spacer"
	| "social_share"
	| "custom_html"
	| "menu"
	| "link_list"
	// Form & Input Elements
	| "input_field"
	| "text_area"
	| "select_box"
	| "radio_buttons"
	| "checkbox"
	| "submit_button"
	| "hidden_field"
	| "search_bar"
	// Order & Payment Elements
	| "product_selector"
	| "credit_card_form"
	| "order_bump"
	| "order_summary"
	| "two_step_order"
	| "cart_total"
	| "shipping_info"
	// Webinar & Automation Elements
	| "auto_webinar_date"
	| "auto_webinar_time"
	| "local_time_display"
	| "webinar_countdown"
	| "sms_signup"
	// Content Enhancements
	| "card"
	| "bullet_list"
	| "faq_accordion"
	| "testimonial"
	| "image_carousel"
	| "custom_form"
	| "map"
	| "table"
	| "embed_code";

export interface BaseComponent {
	id: string;
	type: ComponentType;
	name: string;
}

// Layout / Container Components
export interface HeroSectionComponent extends BaseComponent {
	type: "hero_section";
	properties: {
		title?: string;
		subtitle?: string;
		ctaText?: string;
		ctaUrl?: string;
		secondaryCtaText?: string;
		secondaryCtaUrl?: string;
		backgroundImage?: string;
		backgroundVideo?: string;
		backgroundColor?: string;
		textColor?: string;
		height?: string;
		overlay?: boolean;
		overlayOpacity?: number;
		overlayColor?: string;
		layout?: "centered" | "left" | "right" | "split";
		logoUrl?: string;
		tagline?: string;
		animationEnabled?: boolean;
		animationType?: "fade" | "slide" | "zoom" | "none";
		videoUrl?: string;
	};
}

export interface FeaturesSectionComponent extends BaseComponent {
	type: "features_section";
	properties: {
		heading?: string;
		subheading?: string;
		features: Array<{
			title: string;
			description: string;
			icon?: string;
			iconUrl?: string;
			ctaText?: string;
			ctaUrl?: string;
		}>;
		columns?: number;
		layout?: "grid" | "cards" | "carousel" | "columns";
		backgroundColor?: string;
		backgroundPattern?: string;
		textColor?: string;
	};
}

export interface PricingSectionComponent extends BaseComponent {
	type: "pricing_section";
	properties: {
		heading?: string;
		subheading?: string;
		plans: Array<{
			name: string;
			price: string;
			priceMonthly?: string;
			priceYearly?: string;
			period?: string;
			description?: string;
			features: string[];
			highlighted?: boolean;
			ctaText?: string;
			ctaUrl?: string;
		}>;
		billingToggle?: boolean;
		currency?: string;
		disclaimer?: string;
		backgroundColor?: string;
		accentColor?: string;
		textColor?: string;
	};
}

export interface TestimonialsSectionComponent extends BaseComponent {
	type: "testimonials_section";
	properties: {
		heading?: string;
		subheading?: string;
		testimonials: Array<{
			quote: string;
			author: string;
			role?: string;
			company?: string;
			avatar?: string;
			companyLogo?: string;
			rating?: number;
		}>;
		columns?: number;
		layout?: "carousel" | "grid" | "single";
		backgroundColor?: string;
		backgroundPattern?: string;
		textColor?: string;
	};
}

export interface FormSectionComponent extends BaseComponent {
	type: "form_section";
	properties: {
		heading?: string;
		subheading?: string;
		submitText?: string;
		submitUrl?: string;
		fields: Array<{
			type: "text" | "email" | "textarea" | "tel" | "number" | "dropdown" | "checkbox";
			label: string;
			placeholder?: string;
			required?: boolean;
			options?: string[];
		}>;
		successMessage?: string;
		errorMessage?: string;
		integration?: string;
		privacyText?: string;
		privacyUrl?: string;
		backgroundColor?: string;
		containerStyle?: "default" | "card" | "minimal";
		textColor?: string;
	};
}

export interface FooterSectionComponent extends BaseComponent {
	type: "footer_section";
	properties: {
		companyName?: string;
		logoUrl?: string;
		tagline?: string;
		columns: Array<{
			title: string;
			links: Array<{
				label: string;
				url: string;
			}>;
		}>;
		socialLinks?: Array<{
			platform: string;
			url: string;
		}>;
		contactEmail?: string;
		contactPhone?: string;
		contactAddress?: string;
		newsletterEnabled?: boolean;
		newsletterTitle?: string;
		newsletterPlaceholder?: string;
		newsletterButtonText?: string;
		backgroundColor?: string;
		textColor?: string;
		copyright?: string;
		layout?: "columns" | "centered" | "minimal";
	};
}

export interface SectionComponent extends BaseComponent {
	type: "section";
	properties: {
		background?: string;
		padding?: string;
		maxWidth?: string;
		alignment?: "left" | "center" | "right";
	};
	children: EditorComponent[];
}

export interface RowComponent extends BaseComponent {
	type: "row";
	properties: {
		columns: number;
		gap?: string;
		alignment?: "top" | "center" | "bottom";
	};
	children: EditorComponent[];
}

export interface ColumnComponent extends BaseComponent {
	type: "column";
	properties: {
		width?: string;
		padding?: string;
	};
	children: EditorComponent[];
}

export interface FlexContainerComponent extends BaseComponent {
	type: "flex_container";
	properties: {
		direction?: "row" | "column";
		justifyContent?: "start" | "center" | "end" | "space-between";
		alignItems?: "start" | "center" | "end";
		gap?: string;
	};
	children: EditorComponent[];
}

export interface PopupComponent extends BaseComponent {
	type: "popup";
	properties: {
		trigger?: "click" | "time" | "exit";
		delay?: number;
		width?: string;
		title?: string;
	};
	children: EditorComponent[];
}

export interface UniversalBlockComponent extends BaseComponent {
	type: "universal_block";
	properties: {
		className?: string;
		style?: Record<string, string>;
	};
	children: EditorComponent[];
}

// Content & Functional Elements
export interface HeadlineComponent extends BaseComponent {
	type: "headline";
	properties: {
		text: string;
		level: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
		alignment?: "left" | "center" | "right";
		color?: string;
		fontSize?: string;
		fontWeight?: string;
		letterSpacing?: string;
		lineHeight?: string;
		fontStyle?: string;
		textDecoration?: string;
		paddingTop?: string;
		paddingRight?: string;
		paddingBottom?: string;
		paddingLeft?: string;
		marginTop?: string;
		marginRight?: string;
		marginBottom?: string;
		marginLeft?: string;
		borderRadius?: string;
		width?: string;
		height?: string;
		backgroundColor?: string;
	};
}

export interface SubHeadlineComponent extends BaseComponent {
	type: "sub_headline";
	properties: {
		text: string;
		alignment?: "left" | "center" | "right";
		color?: string;
		fontSize?: string;
		fontWeight?: string;
		fontStyle?: string;
		textDecoration?: string;
		lineHeight?: string;
		maxWidth?: string;
	};
}

export interface ParagraphComponent extends BaseComponent {
	type: "paragraph";
	properties: {
		text: string;
		alignment?: "left" | "center" | "right";
		color?: string;
		fontSize?: string;
		fontWeight?: string;
		fontStyle?: string;
		textDecoration?: string;
		lineHeight?: string;
		paddingTop?: string;
		paddingRight?: string;
		paddingBottom?: string;
		paddingLeft?: string;
		marginTop?: string;
		marginRight?: string;
		marginBottom?: string;
		marginLeft?: string;
		borderRadius?: string;
		width?: string;
		height?: string;
		backgroundColor?: string;
	};
}

export interface ImageComponent extends BaseComponent {
	type: "image";
	properties: {
		src: string;
		alt: string;
		width?: string;
		height?: string;
		objectFit?: "cover" | "contain" | "fill";
		alignment?: "left" | "center" | "right";
	};
}

export interface VideoComponent extends BaseComponent {
	type: "video";
	properties: {
		src: string;
		poster?: string;
		autoplay?: boolean;
		controls?: boolean;
		loop?: boolean;
		width?: string;
		height?: string;
	};
}

export interface AudioComponent extends BaseComponent {
	type: "audio";
	properties: {
		src: string;
		controls?: boolean;
		autoplay?: boolean;
		loop?: boolean;
	};
}

export interface ButtonComponent extends BaseComponent {
	type: "button";
	properties: {
		text: string;
		url?: string;
		variant?: "solid" | "outline" | "ghost";
		size?: "sm" | "md" | "lg";
		color?: string;
		fullWidth?: boolean;
		alignment?: "left" | "center" | "right";
		borderRadius?: string;
		padding?: string;
		fontWeight?: string;
		fontStyle?: string;
		textDecoration?: string;
	};
}

export interface DividerComponent extends BaseComponent {
	type: "divider";
	properties: {
		style?: "solid" | "dashed" | "dotted";
		thickness?: string;
		color?: string;
		spacing?: string;
	};
}

export interface IconComponent extends BaseComponent {
	type: "icon";
	properties: {
		name: string;
		size?: string;
		color?: string;
		alignment?: "left" | "center" | "right";
	};
}

export interface CountdownTimerComponent extends BaseComponent {
	type: "countdown_timer";
	properties: {
		targetDate: string;
		format?: "dhms" | "hms" | "ms";
		alignment?: "left" | "center" | "right";
		size?: string;
	};
}

export interface ProgressBarComponent extends BaseComponent {
	type: "progress_bar";
	properties: {
		value: number;
		max: number;
		color?: string;
		height?: string;
		showLabel?: boolean;
	};
}

export interface SpacerComponent extends BaseComponent {
	type: "spacer";
	properties: {
		height: string;
	};
}

export interface SocialShareComponent extends BaseComponent {
	type: "social_share";
	properties: {
		platforms: string[];
		url?: string;
		title?: string;
		alignment?: "left" | "center" | "right";
	};
}

export interface CustomHtmlComponent extends BaseComponent {
	type: "custom_html";
	properties: {
		html: string;
	};
}

export interface MenuComponent extends BaseComponent {
	type: "menu";
	properties: {
		items: Array<{ label: string; url: string }>;
		orientation?: "horizontal" | "vertical";
		alignment?: "left" | "center" | "right";
	};
}

export interface LinkListComponent extends BaseComponent {
	type: "link_list";
	properties: {
		items: Array<{ label: string; url: string; description?: string }>;
	};
}

// Form & Input Elements
export interface InputFieldComponent extends BaseComponent {
	type: "input_field";
	properties: {
		label: string;
		placeholder?: string;
		fieldType?: "text" | "email" | "tel" | "number" | "url";
		required?: boolean;
		name: string;
	};
}

export interface TextAreaComponent extends BaseComponent {
	type: "text_area";
	properties: {
		label: string;
		placeholder?: string;
		rows?: number;
		required?: boolean;
		name: string;
	};
}

export interface SelectBoxComponent extends BaseComponent {
	type: "select_box";
	properties: {
		label: string;
		options: Array<{ value: string; label: string }>;
		required?: boolean;
		name: string;
	};
}

export interface RadioButtonsComponent extends BaseComponent {
	type: "radio_buttons";
	properties: {
		label: string;
		options: Array<{ value: string; label: string }>;
		required?: boolean;
		name: string;
	};
}

export interface CheckboxComponent extends BaseComponent {
	type: "checkbox";
	properties: {
		label: string;
		required?: boolean;
		name: string;
	};
}

export interface SubmitButtonComponent extends BaseComponent {
	type: "submit_button";
	properties: {
		text: string;
		variant?: "solid" | "outline" | "ghost";
		size?: "sm" | "md" | "lg";
		fullWidth?: boolean;
	};
}

export interface HiddenFieldComponent extends BaseComponent {
	type: "hidden_field";
	properties: {
		name: string;
		value: string;
	};
}

export interface SearchBarComponent extends BaseComponent {
	type: "search_bar";
	properties: {
		placeholder?: string;
		buttonText?: string;
	};
}

// Order & Payment Elements
export interface ProductSelectorComponent extends BaseComponent {
	type: "product_selector";
	properties: {
		products: Array<{
			id: string;
			name: string;
			price: number;
			description?: string;
		}>;
		layout?: "grid" | "list";
	};
}

export interface CreditCardFormComponent extends BaseComponent {
	type: "credit_card_form";
	properties: {
		showBillingAddress?: boolean;
	};
}

export interface OrderBumpComponent extends BaseComponent {
	type: "order_bump";
	properties: {
		productName: string;
		price: number;
		description: string;
		imageUrl?: string;
	};
}

export interface OrderSummaryComponent extends BaseComponent {
	type: "order_summary";
	properties: {
		showTax?: boolean;
		showShipping?: boolean;
	};
}

export interface TwoStepOrderComponent extends BaseComponent {
	type: "two_step_order";
	properties: {
		step1Title: string;
		step2Title: string;
	};
	children: EditorComponent[];
}

export interface CartTotalComponent extends BaseComponent {
	type: "cart_total";
	properties: {
		showBreakdown?: boolean;
	};
}

export interface ShippingInfoComponent extends BaseComponent {
	type: "shipping_info";
	properties: {
		requiredFields: string[];
	};
}

// Webinar & Automation Elements
export interface AutoWebinarDateComponent extends BaseComponent {
	type: "auto_webinar_date";
	properties: {
		format?: string;
		timezone?: string;
	};
}

export interface AutoWebinarTimeComponent extends BaseComponent {
	type: "auto_webinar_time";
	properties: {
		format?: string;
		timezone?: string;
	};
}

export interface LocalTimeDisplayComponent extends BaseComponent {
	type: "local_time_display";
	properties: {
		format?: string;
	};
}

export interface WebinarCountdownComponent extends BaseComponent {
	type: "webinar_countdown";
	properties: {
		webinarDate: string;
		format?: "dhms" | "hms" | "ms";
	};
}

export interface SmsSignupComponent extends BaseComponent {
	type: "sms_signup";
	properties: {
		label: string;
		placeholder?: string;
		buttonText?: string;
	};
}

// Content Enhancements
export interface CardComponent extends BaseComponent {
	type: "card";
	properties: {
		title?: string;
		description?: string;
		imageUrl?: string;
		buttonText?: string;
		buttonUrl?: string;
		variant?: "elevated" | "outlined" | "flat";
	};
}

export interface BulletListComponent extends BaseComponent {
	type: "bullet_list";
	properties: {
		items: string[];
		style?: "disc" | "circle" | "square" | "check";
		color?: string;
	};
}

export interface FaqAccordionComponent extends BaseComponent {
	type: "faq_accordion";
	properties: {
		items: Array<{ question: string; answer: string }>;
		allowMultipleOpen?: boolean;
	};
}

export interface TestimonialComponent extends BaseComponent {
	type: "testimonial";
	properties: {
		quote: string;
		author: string;
		role?: string;
		avatar?: string;
		rating?: number;
	};
}

export interface ImageCarouselComponent extends BaseComponent {
	type: "image_carousel";
	properties: {
		images: Array<{ src: string; alt: string; caption?: string }>;
		autoplay?: boolean;
		interval?: number;
	};
}

export interface CustomFormComponent extends BaseComponent {
	type: "custom_form";
	properties: {
		action?: string;
		method?: "GET" | "POST";
	};
	children: EditorComponent[];
}

export interface MapComponent extends BaseComponent {
	type: "map";
	properties: {
		address?: string;
		latitude?: number;
		longitude?: number;
		zoom?: number;
		height?: string;
	};
}

export interface TableComponent extends BaseComponent {
	type: "table";
	properties: {
		headers: string[];
		rows: string[][];
		striped?: boolean;
		bordered?: boolean;
	};
}

export interface EmbedCodeComponent extends BaseComponent {
	type: "embed_code";
	properties: {
		code: string;
		height?: string;
	};
}

export type EditorComponent =
	| HeroSectionComponent
	| FeaturesSectionComponent
	| PricingSectionComponent
	| TestimonialsSectionComponent
	| FormSectionComponent
	| FooterSectionComponent
	| SectionComponent
	| RowComponent
	| ColumnComponent
	| FlexContainerComponent
	| PopupComponent
	| UniversalBlockComponent
	| HeadlineComponent
	| SubHeadlineComponent
	| ParagraphComponent
	| ImageComponent
	| VideoComponent
	| AudioComponent
	| ButtonComponent
	| DividerComponent
	| IconComponent
	| CountdownTimerComponent
	| ProgressBarComponent
	| SpacerComponent
	| SocialShareComponent
	| CustomHtmlComponent
	| MenuComponent
	| LinkListComponent
	| InputFieldComponent
	| TextAreaComponent
	| SelectBoxComponent
	| RadioButtonsComponent
	| CheckboxComponent
	| SubmitButtonComponent
	| HiddenFieldComponent
	| SearchBarComponent
	| ProductSelectorComponent
	| CreditCardFormComponent
	| OrderBumpComponent
	| OrderSummaryComponent
	| TwoStepOrderComponent
	| CartTotalComponent
	| ShippingInfoComponent
	| AutoWebinarDateComponent
	| AutoWebinarTimeComponent
	| LocalTimeDisplayComponent
	| WebinarCountdownComponent
	| SmsSignupComponent
	| CardComponent
	| BulletListComponent
	| FaqAccordionComponent
	| TestimonialComponent
	| ImageCarouselComponent
	| CustomFormComponent
	| MapComponent
	| TableComponent
	| EmbedCodeComponent;

export interface EditorPage {
	id: string;
	name: string;
	type: "landing" | "upsell" | "downsell" | "thank-you" | "checkout";
	components: EditorComponent[];
	order: number;
	whopPlanIds?: string[];
}

export interface EditorFunnel {
	id: string;
	title: string;
	description: string;
	pages: EditorPage[];
	theme: {
		primaryColor: string;
		backgroundColor: string;
		textColor: string;
	};
}

// Component categories for the palette
export interface ComponentCategory {
	id: string;
	name: string;
	icon: string;
	components: ComponentDefinition[];
}

export interface ComponentDefinition {
	type: ComponentType;
	name: string;
	icon: string;
	category: string;
	defaultProperties: Record<string, unknown>;
}
