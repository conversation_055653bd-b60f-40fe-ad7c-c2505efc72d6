// Funnel schema types
export interface FunnelSection {
	id: string;
	type: "hero" | "problem" | "solution" | "features" | "testimonials" | "pricing" | "faq" | "cta";
	heading: string;
	content: string;
	imageUrl?: string;
	cta?: {
		text: string;
		url: string;
		style: "primary" | "secondary";
	};
	items?: Array<{
		title: string;
		description: string;
		icon?: string;
	}>;
	// Styling and editor properties
	properties?: {
		padding?: { top: number; right: number; bottom: number; left: number };
		textColor?: string;
		textAlign?: string;
		visible?: boolean;
		animation?: string;
		fontSize?: number | string;
		backgroundColor?: string;
		imageUrl?: string;
		altText?: string;
		linkUrl?: string;
		actionType?: string;
		inputType?: string;
		placeholder?: string;
		[key: string]: unknown;
	};
}

export interface FunnelPage {
	id: string;
	name: string;
	type: "landing" | "upsell" | "downsell" | "thank-you" | "checkout";
	sections: FunnelSection[];
	order: number;
	whopPlanIds?: string[]; // Whop plan IDs for checkout pages
}

export interface FunnelSchema {
	title: string;
	description: string;
	pages: FunnelPage[];
	sections: FunnelSection[]; // Keep for backward compatibility
	theme: {
		primaryColor: string;
		backgroundColor: string;
		textColor: string;
	};
	layout: "single-page" | "multi-step";
}

export interface GenerateFunnelRequest {
	prompt: string;
	userId: string;
	companyId: string;
}

export interface GenerateFunnelResponse {
	success: boolean;
	funnel?: FunnelSchema;
	error?: string;
}
