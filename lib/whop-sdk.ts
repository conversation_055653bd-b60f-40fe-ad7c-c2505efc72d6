import Whop from "@whop/sdk";
import { WhopServerSdk } from "@whop/api";

let _whopClient: Whop | null = null;
let _whopSdk: ReturnType<typeof WhopServerSdk> | null = null;

// New Whop SDK for products and other API calls - lazy initialization
export function getWhopClient(): Whop {
	if (!_whopClient) {
		_whopClient = new Whop({
			appID: process.env.NEXT_PUBLIC_WHOP_APP_ID ?? "fallback",
			apiKey: process.env.WHOP_API_KEY ?? "fallback",
		});
	}
	return _whopClient;
}

// Keep old SDK for authentication and access checking - lazy initialization
export function getWhopSdk() {
	if (!_whopSdk) {
		_whopSdk = WhopServerSdk({
			appId: process.env.NEXT_PUBLIC_WHOP_APP_ID ?? "fallback",
			appApiKey: process.env.WHOP_API_KEY ?? "fallback",
			onBehalfOfUserId: process.env.NEXT_PUBLIC_WHOP_AGENT_USER_ID,
			companyId: process.env.NEXT_PUBLIC_WHOP_COMPANY_ID,
		});
	}
	return _whopSdk;
}
