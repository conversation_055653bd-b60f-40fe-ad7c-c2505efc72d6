import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

// Rewrite custom domains to /f/[host]
export async function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const host = request.headers.get('host');
  if (!host) return NextResponse.next();

  // Skip Next.js internals and API/static assets
  const pathname = url.pathname || '/';
  const isInternal = pathname.startsWith('/_next') || pathname.startsWith('/api') || pathname.startsWith('/favicon') || pathname.startsWith('/logo') || pathname.startsWith('/sidebar-logo.png');
  if (isInternal) return NextResponse.next();

  // Allow first-party routes like /dashboard, /discover
  if (pathname.startsWith('/dashboard') || pathname.startsWith('/discover') || pathname === '/') {
    return NextResponse.next();
  }

  // If host appears to be a custom domain (not the primary site domain), rewrite
  // You may want to set NEXT_PUBLIC_PRIMARY_DOMAIN env var for your app domain.
  const primary = process.env.NEXT_PUBLIC_PRIMARY_DOMAIN;
  const hostnameOnly = host.split(':')[0].toLowerCase();
  if (primary && hostnameOnly.endsWith(primary.toLowerCase())) {
    return NextResponse.next();
  }

  // Rewrite to domain serving route
  url.pathname = `/f/${hostnameOnly}`;
  return NextResponse.rewrite(url);
}

export const config = {
  matcher: [
    '/((?!_next|.*\\.\w+$).*)',
  ],
};
