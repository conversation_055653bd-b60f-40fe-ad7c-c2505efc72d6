# Supabase Integration Setup Guide

This guide will help you set up Supabase for the FunnelFlow AI application.

## Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- Your Supabase project URL and anon key (provided in the Linear issue)

## Step 1: Create Your Supabase Project

If you haven't already:
1. Go to [supabase.com](https://supabase.com)
2. Sign in or create an account
3. Your project is already created at: `https://zkizdauajeccoduwpihv.supabase.co`

## Step 2: Set Up the Database Schema

1. Open your Supabase project dashboard
2. Navigate to the **SQL Editor** in the left sidebar
3. Click **New Query**
4. Copy the entire contents of `supabase-schema.sql` file from this repository
5. Paste it into the SQL editor
6. Click **Run** to execute the SQL commands

This will create:
- `users` table - Stores user information from Whop
- `funnels` table - Stores all funnel data with pages, sections, theme, and analytics
- `feature_templates` table - Stores saved feature section templates
- Indexes for better query performance
- Row Level Security (RLS) policies
- Auto-update triggers for `updated_at` columns

## Step 3: Verify Environment Variables

The environment variables have already been added to `.env.development`:

```env
NEXT_PUBLIC_SUPABASE_URL=https://zkizdauajeccoduwpihv.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpraXpkYXVhamVjY29kdXdwaWh2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NjA2ODA5MzgsImV4cCI6MjA3NjI1NjkzOH0.ZOfumiZEG_aho5_o1aQuwwNFWtilBZ2wSCTUzMiDQuk
```

For production, make sure to add these to your production environment variables as well.

## Step 4: Understanding the Data Model

### Users Table
```sql
- id (UUID, Primary Key)
- whop_user_id (TEXT, Unique) - User ID from Whop
- whop_company_id (TEXT) - Company ID from Whop
- user_name (TEXT) - User's display name
- username (TEXT) - User's username
- company_title (TEXT) - Company name
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

### Funnels Table
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key to users)
- whop_company_id (TEXT) - For filtering by company
- title (TEXT) - Funnel title
- description (TEXT) - Funnel description
- status (TEXT) - 'draft' or 'published'
- pages (JSONB) - Array of funnel pages
- sections (JSONB) - Array of sections (backward compatibility)
- theme (JSONB) - Color scheme and styling
- layout (TEXT) - 'single-page' or 'multi-step'
- visits (INTEGER) - Analytics: number of visits
- conversions (INTEGER) - Analytics: number of conversions
- revenue (DECIMAL) - Analytics: total revenue
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
- published_at (TIMESTAMP) - When the funnel was published
```

### Feature Templates Table
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key to users)
- whop_company_id (TEXT) - For filtering by company
- name (TEXT) - Template name
- properties (JSONB) - Template properties
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

## Step 5: API Endpoints

The following API endpoints have been created:

### Funnels
- `GET /api/funnels?companyId={id}` - Get all funnels for a company
- `POST /api/funnels` - Create a new funnel
- `GET /api/funnels/[id]` - Get a specific funnel
- `PUT /api/funnels/[id]` - Update a funnel
- `DELETE /api/funnels/[id]` - Delete a funnel

### Templates
- `GET /api/templates?companyId={id}` - Get all templates for a company
- `POST /api/templates` - Create a new template
- `DELETE /api/templates/[id]` - Delete a template

## Step 6: Testing the Integration

1. **Start the development server:**
   ```bash
   pnpm dev
   ```

2. **Test creating a funnel:**
   - Open the app in your browser
   - Create a new funnel using the AI generator
   - The funnel should be automatically saved to Supabase

3. **Verify in Supabase:**
   - Go to your Supabase dashboard
   - Navigate to **Table Editor**
   - Check the `funnels` table - you should see your newly created funnel
   - Check the `users` table - your user should be automatically created

4. **Test funnel operations:**
   - Edit the funnel - changes should be saved to Supabase
   - Delete the funnel - it should be removed from Supabase
   - Refresh the page - funnels should load from Supabase

## Step 7: Migration from localStorage

All localStorage data has been migrated to Supabase:

✅ Funnel data (draft and published)
✅ Feature templates
✅ Auto-save functionality
✅ User information

**Note:** If you have existing data in localStorage, it will not be automatically migrated. Users will need to recreate their funnels, or you can write a migration script if needed.

## Troubleshooting

### Error: "Missing Supabase environment variables"
- Make sure `.env.development` contains the Supabase credentials
- Restart your development server after adding environment variables

### Error: "Failed to fetch funnels"
- Check that the SQL schema has been executed in Supabase
- Verify the environment variables are correct
- Check the browser console for detailed error messages

### Error: "Row Level Security policy violation"
- The RLS policies are set to allow public access for development
- For production, you should update the policies to be more restrictive

### Data not persisting
- Check the Network tab in browser DevTools to see if API calls are successful
- Verify the Supabase dashboard shows the data being inserted
- Check the server logs for any errors

## Security Considerations

### Current Setup (Development)
- RLS policies allow public read/write access
- This is suitable for development and testing

### Production Recommendations
1. **Implement proper authentication:**
   - Use Supabase Auth or integrate with Whop authentication
   - Update RLS policies to check authenticated user

2. **Update RLS policies:**
   ```sql
   -- Example: Only allow users to see their own company's funnels
   CREATE POLICY "Users can only see their company funnels" ON funnels
       FOR SELECT USING (auth.uid() = user_id);
   ```

3. **Add API authentication:**
   - Verify Whop tokens in API routes
   - Validate user permissions before allowing operations

4. **Environment variables:**
   - Use Vercel/deployment platform's environment variables
   - Never commit `.env` files to version control

## Next Steps

1. ✅ Database schema is set up
2. ✅ Environment variables are configured
3. ✅ API routes are created
4. ✅ Components are updated to use Supabase
5. 🔄 Test the integration thoroughly
6. 🔜 Deploy to production
7. 🔜 Implement proper authentication
8. 🔜 Update RLS policies for production

## Support

If you encounter any issues:
1. Check the browser console for errors
2. Check the Supabase logs in the dashboard
3. Review the API route logs
4. Verify the database schema matches the expected structure

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
