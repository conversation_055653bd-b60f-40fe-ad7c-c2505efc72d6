"use client";

import { Badge, Heading, Text } from "frosted-ui";
import React, { useState } from "react";
import { FormattingSidebar } from "./FormattingSidebar";

interface LeftSidebarProps {
	onAddSection?: (sectionType: string) => void;
	onAddElement?: (elementType: string) => void;
	onSelectElement?: (elementId: string) => void;
	onReorderElements?: (fromIndex: number, toIndex: number) => void;
	onUpdateElement?: (elementId: string, properties: any) => void;
	elements?: Array<{
		id: string;
		name: string;
		type: string;
		visible: boolean;
		properties?: any;
	}>;
	selectedElementId?: string;
}

type TabType = "sections" | "elements" | "layers" | "text-formatting";

interface SectionItem {
	id: string;
	name: string;
	description: string;
	icon: string;
	preview: string;
}

interface ElementItem {
	id: string;
	name: string;
	description: string;
	icon: string;
	category: string;
}

const sectionItems: SectionItem[] = [
	{
		id: "hero",
		name: "Hero",
		description: "Landing page header with CTA",
		icon: "hero",
		preview: "Hero with headline, subtext, and button",
	},
	{
		id: "problem",
		name: "Problem",
		description: "Address customer pain points",
		icon: "problem",
		preview: "Problem identification section",
	},
	{
		id: "solution",
		name: "Solution",
		description: "Present your solution",
		icon: "solution",
		preview: "Solution presentation",
	},
	{
		id: "features",
		name: "Features",
		description: "Product features showcase",
		icon: "features",
		preview: "3-column feature grid",
	},
	{
		id: "pricing",
		name: "Pricing",
		description: "Pricing table",
		icon: "pricing",
		preview: "3-tier pricing cards",
	},
	{
		id: "testimonials",
		name: "Testimonials",
		description: "Customer reviews",
		icon: "testimonials",
		preview: "Customer quote carousel",
	},
	{
		id: "social_proof",
		name: "Social Proof",
		description: "Trust indicators and logos",
		icon: "social_proof",
		preview: "Company logos and trust badges",
	},
	{
		id: "faq",
		name: "FAQ",
		description: "Frequently asked questions",
		icon: "faq",
		preview: "Expandable FAQ section",
	},
	{
		id: "form",
		name: "Form",
		description: "Lead capture form",
		icon: "form",
		preview: "Email signup form",
	},
	{
		id: "footer",
		name: "Footer",
		description: "Page footer with links",
		icon: "footer",
		preview: "Footer with social links",
	},
];

const elementItems: ElementItem[] = [
	// Content Elements
	{
		id: "text",
		name: "Text",
		description: "Headline, paragraph, or label",
		icon: "text",
		category: "Content",
	},
	{
		id: "heading",
		name: "Heading",
		description: "H1, H2, H3, etc.",
		icon: "heading",
		category: "Content",
	},
	{
		id: "list",
		name: "List",
		description: "Bulleted or numbered list",
		icon: "list",
		category: "Content",
	},
	{
		id: "quote",
		name: "Quote",
		description: "Blockquote or testimonial",
		icon: "quote",
		category: "Content",
	},
	{
		id: "code",
		name: "Code",
		description: "Code snippet or technical text",
		icon: "code",
		category: "Content",
	},
	// Media Elements
	{
		id: "image",
		name: "Image",
		description: "Photo or illustration",
		icon: "image",
		category: "Media",
	},
	{
		id: "video",
		name: "Video",
		description: "Embedded video player",
		icon: "video",
		category: "Media",
	},
	{
		id: "audio",
		name: "Audio",
		description: "Audio player",
		icon: "audio",
		category: "Media",
	},
	{
		id: "gallery",
		name: "Gallery",
		description: "Image gallery or carousel",
		icon: "gallery",
		category: "Media",
	},
	// Interactive Elements
	{
		id: "button",
		name: "Button",
		description: "Call-to-action button",
		icon: "button",
		category: "Interactive",
	},
	{
		id: "link",
		name: "Link",
		description: "Text or image link",
		icon: "link",
		category: "Interactive",
	},
	{
		id: "accordion",
		name: "Accordion",
		description: "Expandable content sections",
		icon: "accordion",
		category: "Interactive",
	},
	{
		id: "tabs",
		name: "Tabs",
		description: "Tabbed content interface",
		icon: "tabs",
		category: "Interactive",
	},
	{
		id: "modal",
		name: "Modal",
		description: "Popup or overlay content",
		icon: "modal",
		category: "Interactive",
	},
	// Form Elements
	{
		id: "input",
		name: "Input",
		description: "Text input field",
		icon: "input",
		category: "Forms",
	},
	{
		id: "textarea",
		name: "Textarea",
		description: "Multi-line text input",
		icon: "textarea",
		category: "Forms",
	},
	{
		id: "select",
		name: "Select",
		description: "Dropdown selection",
		icon: "select",
		category: "Forms",
	},
	{
		id: "checkbox",
		name: "Checkbox",
		description: "Checkbox input",
		icon: "checkbox",
		category: "Forms",
	},
	{
		id: "radio",
		name: "Radio",
		description: "Radio button input",
		icon: "radio",
		category: "Forms",
	},
	{
		id: "date",
		name: "Date",
		description: "Date picker input",
		icon: "date",
		category: "Forms",
	},
	{
		id: "file",
		name: "File Upload",
		description: "File upload input",
		icon: "file",
		category: "Forms",
	},
	// Layout Elements
	{
		id: "divider",
		name: "Divider",
		description: "Visual separator line",
		icon: "divider",
		category: "Layout",
	},
	{
		id: "spacer",
		name: "Spacer",
		description: "Vertical spacing",
		icon: "spacer",
		category: "Layout",
	},
	{
		id: "container",
		name: "Container",
		description: "Content wrapper",
		icon: "container",
		category: "Layout",
	},
	{
		id: "columns",
		name: "Columns",
		description: "Multi-column layout",
		icon: "columns",
		category: "Layout",
	},
	{
		id: "card",
		name: "Card",
		description: "Content card container",
		icon: "card",
		category: "Layout",
	},
	// Advanced Elements
	{
		id: "discount_banner",
		name: "Discount Banner",
		description: "Promotional banner with discount code",
		icon: "discount_banner",
		category: "Advanced",
	},
	{
		id: "countdown",
		name: "Countdown",
		description: "Timer or countdown display",
		icon: "countdown",
		category: "Advanced",
	},
	{
		id: "progress",
		name: "Progress Bar",
		description: "Progress indicator",
		icon: "progress",
		category: "Advanced",
	},
	{
		id: "chart",
		name: "Chart",
		description: "Data visualization",
		icon: "chart",
		category: "Advanced",
	},
	{
		id: "map",
		name: "Map",
		description: "Interactive map",
		icon: "map",
		category: "Advanced",
	},
	{
		id: "embed",
		name: "Embed",
		description: "External content embed",
		icon: "embed",
		category: "Advanced",
	},
];

export function LeftSidebar({ onAddSection, onAddElement, onSelectElement, onReorderElements, onUpdateElement, elements = [], selectedElementId }: LeftSidebarProps) {
	const [activeTab, setActiveTab] = useState<TabType>("sections");
	const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
	const [showFormattingPanel, setShowFormattingPanel] = useState(false);

	// Check if selected element is a text element
	const selectedElement = elements.find(el => el.id === selectedElementId);
	const isTextElement = selectedElement && [
		"text", "heading", "headline", "subheadline", "paragraph", "button", "hero",
		"features", "pricing", "testimonials", "form", "footer"
	].includes(selectedElement.type);

	// Auto-switch to text formatting for text elements, layers for others
	React.useEffect(() => {
		if (selectedElementId) {
			if (isTextElement) {
				setActiveTab("text-formatting");
			} else {
				setActiveTab("layers");
			}
		}
	}, [selectedElementId, isTextElement]);

	const getIcon = (iconName: string) => {
		const iconProps = {
			width: 20,
			height: 20,
			viewBox: "0 0 24 24",
			fill: "none",
			stroke: "currentColor",
			strokeWidth: 2,
			strokeLinecap: "round" as const,
			strokeLinejoin: "round" as const,
		};

		switch (iconName) {
			case "hero":
				// SparkleRectangle icon for hero - clean Frost UI style
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<rect x="8" y="8" width="8" height="8" rx="1" ry="1" />
						<circle cx="12" cy="12" r="1" fill="currentColor" />
					</svg>
				);
			case "features":
				// Clean features icon - grid layout
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="7" height="7" />
						<rect x="14" y="3" width="7" height="7" />
						<rect x="14" y="14" width="7" height="7" />
						<rect x="3" y="14" width="7" height="7" />
					</svg>
				);
			case "pricing":
				// DollarCircle icon for pricing
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="10" />
						<path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8" />
						<path d="M12 18V6" />
					</svg>
				);
			case "testimonials":
				// SealCheckmark icon for testimonials - clean Frost UI style
				return (
					<svg {...iconProps}>
						<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
						<path d="m9 12 2 2 4-4" />
					</svg>
				);
			case "form":
				// Clean form icon - document with lines
				return (
					<svg {...iconProps}>
						<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
						<path d="M14 2v6h6" />
						<path d="M16 13H8" />
						<path d="M16 17H8" />
						<path d="M10 9H8" />
					</svg>
				);
			case "footer":
				// LinkAdd icon for footer - clean Frost UI style
				return (
					<svg {...iconProps}>
						<path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
						<path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
						<circle cx="18" cy="6" r="3" />
						<path d="M18 5v2M17 6h2" />
					</svg>
				);
			case "text":
				// Clean text icon
				return (
					<svg {...iconProps}>
						<path d="M4 7V4h16v3" />
						<path d="M9 20h6" />
						<path d="M12 4v16" />
					</svg>
				);
			case "heading":
				// Clean heading icon
				return (
					<svg {...iconProps}>
						<path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z" />
						<path d="M6 12h9" />
					</svg>
				);
			case "image":
				// Clean image icon
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<circle cx="9" cy="9" r="2" />
						<path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
					</svg>
				);
			case "button":
				// Clean button icon
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<path d="M9 9h6v6H9z" />
					</svg>
				);
			case "input":
				// Clean input icon
				return (
					<svg {...iconProps}>
						<rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
						<line x1="7" y1="8" x2="17" y2="8" />
						<line x1="7" y1="12" x2="17" y2="12" />
					</svg>
				);
			case "video":
				// Video player icon with play button
				return (
					<svg {...iconProps} stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
						<rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
						<polygon points="10 8 16 12 10 16 10 8" fill="currentColor" />
					</svg>
				);
			case "divider":
				// Clean divider icon
				return (
					<svg {...iconProps}>
						<line x1="3" y1="12" x2="21" y2="12" />
					</svg>
				);
			case "spacer":
				// Clean spacer icon
				return (
					<svg {...iconProps}>
						<line x1="12" y1="3" x2="12" y2="21" />
						<path d="M8 6l4-4 4 4" />
						<path d="M8 18l4 4 4-4" />
					</svg>
				);
			// New section icons
			case "problem":
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="10" />
						<line x1="15" y1="9" x2="9" y2="15" />
						<line x1="9" y1="9" x2="15" y2="15" />
					</svg>
				);
			case "solution":
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="10" />
						<path d="M9 12l2 2 4-4" />
					</svg>
				);
			case "benefits":
				return (
					<svg {...iconProps}>
						<path d="M9 12l2 2 4-4" />
						<path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z" />
						<path d="M3 8h18" />
					</svg>
				);
			case "faq":
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="10" />
						<path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
						<path d="M12 17h.01" />
					</svg>
				);
			case "social_proof":
				return (
					<svg {...iconProps}>
						<path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
						<circle cx="9" cy="7" r="4" />
						<path d="M22 21v-2a4 4 0 0 0-3-3.87" />
						<path d="M16 3.13a4 4 0 0 1 0 7.75" />
					</svg>
				);
			case "checkout":
				return (
					<svg {...iconProps}>
						<rect x="1" y="3" width="15" height="13" />
						<path d="M16 8h2a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2h-2" />
						<path d="M5 8h2" />
						<path d="M5 12h2" />
					</svg>
				);
			case "thank_you":
				return (
					<svg {...iconProps}>
						<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
						<path d="M22 4L12 14.01l-3-3" />
					</svg>
				);
			// New element icons
			case "list":
				return (
					<svg {...iconProps}>
						<line x1="8" y1="6" x2="21" y2="6" />
						<line x1="8" y1="12" x2="21" y2="12" />
						<line x1="8" y1="18" x2="21" y2="18" />
						<line x1="3" y1="6" x2="3.01" y2="6" />
						<line x1="3" y1="12" x2="3.01" y2="12" />
						<line x1="3" y1="18" x2="3.01" y2="18" />
					</svg>
				);
			case "quote":
				return (
					<svg {...iconProps}>
						<path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z" />
						<path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z" />
					</svg>
				);
			case "code":
				return (
					<svg {...iconProps}>
						<polyline points="16,18 22,12 16,6" />
						<polyline points="8,6 2,12 8,18" />
					</svg>
				);
			case "audio":
				return (
					<svg {...iconProps}>
						<polygon points="11,5 6,9 2,9 2,15 6,15 11,19 11,5" />
						<path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07" />
					</svg>
				);
			case "gallery":
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<circle cx="8.5" cy="8.5" r="1.5" />
						<polyline points="21,15 16,10 5,21" />
					</svg>
				);
			case "link":
				return (
					<svg {...iconProps}>
						<path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
						<path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
					</svg>
				);
			case "accordion":
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<path d="M9 9h6" />
						<path d="M9 15h6" />
						<path d="M9 12h6" />
					</svg>
				);
			case "tabs":
				return (
					<svg {...iconProps}>
						<rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
						<line x1="8" y1="21" x2="16" y2="21" />
						<line x1="12" y1="17" x2="12" y2="21" />
					</svg>
				);
			case "modal":
				return (
					<svg {...iconProps}>
						<rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
						<line x1="8" y1="21" x2="16" y2="21" />
						<line x1="12" y1="17" x2="12" y2="21" />
					</svg>
				);
			case "textarea":
				return (
					<svg {...iconProps}>
						<rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
						<line x1="7" y1="8" x2="17" y2="8" />
						<line x1="7" y1="12" x2="17" y2="12" />
						<line x1="7" y1="16" x2="17" y2="16" />
					</svg>
				);
			case "select":
				return (
					<svg {...iconProps}>
						<rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
						<path d="M8 8h8" />
						<path d="M8 12h8" />
						<path d="M8 16h8" />
					</svg>
				);
			case "checkbox":
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<path d="M9 12l2 2 4-4" />
					</svg>
				);
			case "radio":
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="10" />
						<circle cx="12" cy="12" r="3" />
					</svg>
				);
			case "date":
				return (
					<svg {...iconProps}>
						<rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
						<line x1="16" y1="2" x2="16" y2="6" />
						<line x1="8" y1="2" x2="8" y2="6" />
						<line x1="3" y1="10" x2="21" y2="10" />
					</svg>
				);
			case "file":
				return (
					<svg {...iconProps}>
						<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
						<polyline points="14,2 14,8 20,8" />
						<line x1="16" y1="13" x2="8" y2="13" />
						<line x1="16" y1="17" x2="8" y2="17" />
						<polyline points="10,9 9,9 8,9" />
					</svg>
				);
			case "container":
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<rect x="7" y="7" width="10" height="10" rx="1" ry="1" />
					</svg>
				);
			case "columns":
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<line x1="9" y1="3" x2="9" y2="21" />
						<line x1="15" y1="3" x2="15" y2="21" />
					</svg>
				);
			case "card":
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<rect x="7" y="7" width="10" height="10" rx="1" ry="1" />
					</svg>
				);
			case "discount_banner":
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<path d="M9 9h6" />
						<path d="M9 12h6" />
						<path d="M9 15h6" />
						<circle cx="18" cy="6" r="2" />
					</svg>
				);
			case "countdown":
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="10" />
						<polyline points="12,6 12,12 16,14" />
					</svg>
				);
			case "progress":
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<rect x="3" y="3" width="12" height="18" rx="2" ry="2" />
					</svg>
				);
			case "chart":
				return (
					<svg {...iconProps}>
						<line x1="18" y1="20" x2="18" y2="10" />
						<line x1="12" y1="20" x2="12" y2="4" />
						<line x1="6" y1="20" x2="6" y2="14" />
					</svg>
				);
			case "map":
				return (
					<svg {...iconProps}>
						<polygon points="1,6 1,22 8,18 16,22 23,18 23,2 16,6 8,2 1,6" />
						<circle cx="12" cy="12" r="3" />
					</svg>
				);
			case "embed":
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
						<path d="M8 9h8" />
						<path d="M8 15h8" />
						<path d="M8 12h8" />
					</svg>
				);
			case "sections":
				// Clean sections icon
				return (
					<svg {...iconProps}>
						<rect x="3" y="3" width="7" height="7" />
						<rect x="14" y="3" width="7" height="7" />
						<rect x="14" y="14" width="7" height="7" />
						<rect x="3" y="14" width="7" height="7" />
					</svg>
				);
			case "elements":
				// Clean elements icon
				return (
					<svg {...iconProps}>
						<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
					</svg>
				);
			case "layers":
				// Clean layers icon
				return (
					<svg {...iconProps}>
						<polygon points="12 2 2 7 12 12 22 7 12 2" />
						<polyline points="2 17 12 22 22 17" />
						<polyline points="2 12 12 17 22 12" />
					</svg>
				);
			case "text":
				// Text formatting icon
				return (
					<svg {...iconProps}>
						<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
						<polyline points="14 2 14 8 20 8" />
						<line x1="16" y1="13" x2="8" y2="13" />
						<line x1="16" y1="17" x2="8" y2="17" />
						<polyline points="10 9 9 9 8 9" />
					</svg>
				);
			default:
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="3" />
					</svg>
				);
		}
	};

	// Conditionally show tabs based on selection state
	const tabs = isTextElement ? [
		{ id: "text-formatting" as TabType, label: "Text", icon: "text" },
	] : [
		{ id: "sections" as TabType, label: "Sections", icon: "sections" },
		{ id: "elements" as TabType, label: "Elements", icon: "elements" },
		{ id: "layers" as TabType, label: "Layers", icon: "layers" },
	];


	const renderSections = () => {
		// Group sections by category for better organization
		const sectionCategories = {
			"Landing": ["hero", "problem", "solution"],
			"Content": ["features", "testimonials", "social_proof", "faq"],
			"Conversion": ["form", "pricing"],
			"Structure": ["footer"]
		};

		return (
			<div className="flex flex-col gap-4 p-3">
				{Object.entries(sectionCategories).map(([category, sectionIds]) => (
					<div key={category}>
						<Text size="1" weight="bold" color="gray" style={{ marginBottom: "8px", paddingLeft: "4px" }}>
							{category.toUpperCase()}
						</Text>
						<div className="flex flex-col gap-2">
							{sectionItems
								.filter(section => sectionIds.includes(section.id))
								.map((section) => (
									<div
										key={section.id}
										draggable
										onDragStart={(e) => {
											e.dataTransfer.setData("sectionType", section.id);
										}}
										onClick={() => onAddSection?.(section.id)}
										className="whop-card"
										style={{
											padding: "12px",
											cursor: "grab",
											transition: "all 0.2s ease",
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.background = "var(--accent-2)";
											e.currentTarget.style.borderColor = "var(--accent-6)";
											e.currentTarget.style.transform = "translateY(-1px)";
											e.currentTarget.style.boxShadow = "0 4px 12px rgba(59, 130, 246, 0.15)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.background = "var(--color-panel-solid)";
											e.currentTarget.style.borderColor = "var(--gray-6)";
											e.currentTarget.style.transform = "translateY(0)";
											e.currentTarget.style.boxShadow = "none";
										}}
									>
										<div className="flex items-start gap-3">
											<div
												style={{
													width: "36px",
													height: "36px",
													background: "var(--accent-3)",
													borderRadius: "8px",
													display: "flex",
													alignItems: "center",
													justifyContent: "center",
													fontSize: "16px",
													flexShrink: 0,
													color: "var(--accent-11)",
												}}
											>
												{getIcon(section.icon)}
											</div>
											<div style={{ flex: 1, minWidth: 0 }}>
												<Text size="2" weight="medium" style={{ display: "block", marginBottom: "2px" }}>
													{section.name}
												</Text>
												<Text size="1" color="gray" style={{ display: "block", lineHeight: "1.3" }}>
													{section.description}
												</Text>
											</div>
										</div>
									</div>
								))}
						</div>
					</div>
				))}
			</div>
		);
	};

	const renderElements = () => {
		const categories = [...new Set(elementItems.map(item => item.category))];
		
		return (
			<div className="flex flex-col gap-4 p-3">
				{categories.map((category) => (
					<div key={category}>
						<Text size="1" weight="bold" color="gray" style={{ marginBottom: "8px", paddingLeft: "4px" }}>
							{category.toUpperCase()}
						</Text>
						<div className="flex flex-col gap-2">
							{elementItems
								.filter(item => item.category === category)
								.map((element, elementIndex) => (
									<div
										key={`${element.id}-${elementIndex}`}
										draggable
										onDragStart={(e) => {
											e.dataTransfer.setData("elementType", element.id);
										}}
										onClick={() => onAddElement?.(element.id)}
										style={{
											padding: "12px",
											background: "var(--gray-1)",
											border: "1px solid var(--gray-6)",
											borderRadius: "8px",
											cursor: "grab",
											transition: "all 0.2s ease",
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.background = "var(--accent-2)";
											e.currentTarget.style.borderColor = "var(--accent-6)";
											e.currentTarget.style.transform = "translateY(-1px)";
											e.currentTarget.style.boxShadow = "0 4px 12px rgba(59, 130, 246, 0.15)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.background = "var(--gray-1)";
											e.currentTarget.style.borderColor = "var(--gray-6)";
											e.currentTarget.style.transform = "translateY(0)";
											e.currentTarget.style.boxShadow = "none";
										}}
									>
										<div className="flex items-center gap-3">
											<div 
												style={{ 
													color: "var(--accent-11)",
													width: "20px",
													height: "20px",
													display: "flex",
													alignItems: "center",
													justifyContent: "center"
												}}
											>
												{getIcon(element.icon)}
											</div>
											<div style={{ flex: 1 }}>
												<Text size="2" weight="medium" style={{ display: "block", marginBottom: "2px" }}>
													{element.name}
												</Text>
												<Text size="1" color="gray" style={{ display: "block", lineHeight: "1.3" }}>
													{element.description}
												</Text>
											</div>
										</div>
									</div>
								))}
						</div>
					</div>
				))}
			</div>
		);
	};

	const handleDragStart = (index: number) => {
		setDraggedIndex(index);
	};

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
	};

	const handleDrop = (e: React.DragEvent, dropIndex: number) => {
		e.preventDefault();
		if (draggedIndex !== null && draggedIndex !== dropIndex && onReorderElements) {
			onReorderElements(draggedIndex, dropIndex);
		}
		setDraggedIndex(null);
	};

	const handleDragEnd = () => {
		setDraggedIndex(null);
	};

	const renderTextFormatting = () => {
		if (!selectedElement) return null;

		return (
			<div style={{ padding: "16px", height: "100%", background: "var(--gray-1)" }}>
				{/* Header */}
				<div style={{
					display: "flex",
					alignItems: "center",
					justifyContent: "space-between",
					marginBottom: "20px",
					paddingBottom: "12px",
					borderBottom: "1px solid var(--gray-5)"
				}}>
					<Text size="2" weight="bold" style={{ color: "var(--gray-12)" }}>
						Text Formatting
					</Text>
					<button
						onClick={() => {
							onSelectElement?.("");
							setActiveTab("elements");
						}}
						style={{
							background: "none",
							border: "none",
							color: "var(--gray-9)",
							fontSize: "18px",
							cursor: "pointer",
							padding: "4px",
							borderRadius: "4px",
							lineHeight: 1,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.background = "var(--gray-3)";
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.background = "none";
						}}
					>
						×
					</button>
				</div>

				{/* Inline FormattingSidebar content */}
				<div style={{ height: "calc(100% - 60px)", overflowY: "auto" }}>
					{(() => {
						// Helper function to get the correct property key based on element type and ID
						const getPropertyKey = (baseProperty: string) => {
							switch (selectedElement?.type) {
								case 'hero':
									if (selectedElement.id.includes('-headline')) return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
									if (selectedElement.id.includes('-subheadline')) return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
									return baseProperty;
								case 'pricing':
									if (selectedElement.id.includes('-pricing-title')) return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
									return baseProperty;
								case 'features':
									if (selectedElement.id.includes('-features-title')) return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
									if (selectedElement.id.includes('-features-description')) return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
									return baseProperty;
								case 'testimonials':
									if (selectedElement.id.includes('-testimonials-title')) return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
									return baseProperty;
								case 'social-proof':
									if (selectedElement.id.includes('-social-proof-title')) return `socialProofTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
									return baseProperty;
								case 'form':
									if (selectedElement.id.includes('-form-title')) return `formTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
									return baseProperty;
								case 'footer':
									if (selectedElement.id.includes('-footer-title')) return `footerTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
									return baseProperty;
								default:
									return baseProperty;
							}
						};

						// Get current property values
						const textColorKey = getPropertyKey('color');
						const fontSizeKey = getPropertyKey('fontSize');
						const fontFamilyKey = getPropertyKey('fontFamily');
						const fontWeightKey = getPropertyKey('fontWeight');
						const fontStyleKey = getPropertyKey('fontStyle');
						const textDecorationKey = getPropertyKey('textDecoration');
						const textAlignKey = getPropertyKey('textAlign');

						return (
							<FormattingSidebar
								visible={true}
								textContent={(selectedElement as any).content || selectedElement.properties?.content || selectedElement.properties?.text || ""}
								textColor={selectedElement.properties?.[textColorKey] || selectedElement.properties?.textColor || "#000000"}
								backgroundColor={selectedElement.properties?.backgroundColor || "#ffffff"}
								fontSize={String(selectedElement.properties?.[fontSizeKey] || selectedElement.properties?.fontSize || "Medium")}
								fontFamily={selectedElement.properties?.[fontFamilyKey] || selectedElement.properties?.fontFamily || "Inter"}
								textAlign={selectedElement.properties?.[textAlignKey] || selectedElement.properties?.textAlign || "left"}
								isBold={(selectedElement.properties?.[fontWeightKey] || selectedElement.properties?.fontWeight) === "bold"}
								isItalic={(selectedElement.properties?.[fontStyleKey] || selectedElement.properties?.fontStyle) === "italic"}
								isUnderline={(selectedElement.properties?.[textDecorationKey] || selectedElement.properties?.textDecoration) === "underline"}
						onTextChange={(text) => {
							// Update both content and properties.text to handle different element types
							onUpdateElement?.(selectedElement.id, {
								_content: text,
								content: text,
								text: text
							});
						}}
						onTextColor={(color) => {
							// Use the same property mapping logic as the tooltip
							const getPropertyKey = () => {
								switch (selectedElement?.type) {
									case 'hero':
										if (selectedElement.id.includes('-headline')) return `headlineColor`;
										if (selectedElement.id.includes('-subheadline')) return `subheadlineColor`;
										return 'textColor';
									case 'pricing':
										if (selectedElement.id.includes('-pricing-title')) return `pricingTitleColor`;
										return 'textColor';
									case 'features':
										if (selectedElement.id.includes('-features-title')) return `featuresTitleColor`;
										if (selectedElement.id.includes('-features-description')) return `featuresDescriptionColor`;
										return 'textColor';
									case 'testimonials':
										if (selectedElement.id.includes('-testimonials-title')) return `testimonialsTitleColor`;
										return 'textColor';
									case 'social-proof':
										if (selectedElement.id.includes('-social-proof-title')) return `socialProofTitleColor`;
										return 'textColor';
									case 'form':
										if (selectedElement.id.includes('-form-title')) return `formTitleColor`;
										return 'textColor';
									case 'footer':
										if (selectedElement.id.includes('-footer-title')) return `footerTitleColor`;
										return 'textColor';
									default:
										return 'textColor';
								}
							};
							const propertyKey = getPropertyKey();
							onUpdateElement?.(selectedElement.id, { [propertyKey]: color });
						}}
						onBackgroundColor={(color) => onUpdateElement?.(selectedElement.id, { backgroundColor: color })}
						onFontSize={(size) => onUpdateElement?.(selectedElement.id, { fontSize: size })}
						onFontFamily={(family) => onUpdateElement?.(selectedElement.id, { fontFamily: family })}
						onTextAlign={(align) => onUpdateElement?.(selectedElement.id, { textAlign: align })}
						onBold={() => onUpdateElement?.(selectedElement.id, {
							fontWeight: selectedElement.properties?.fontWeight === "bold" ? "normal" : "bold"
						})}
						onItalic={() => onUpdateElement?.(selectedElement.id, {
							fontStyle: selectedElement.properties?.fontStyle === "italic" ? "normal" : "italic"
						})}
						onUnderline={() => onUpdateElement?.(selectedElement.id, {
							textDecoration: selectedElement.properties?.textDecoration === "underline" ? "none" : "underline"
						})}
						onAIEdit={(action) => {
							console.log("AI Edit action:", action);
						}}
						onLink={() => {
							console.log("Link action");
						}}
						onMore={() => {
							console.log("More options");
						}}
					/>
						);
					})()}
				</div>
			</div>
		);
	};

	const renderLayers = () => {
		const selectedElement = elements.find(el => el.id === selectedElementId);
		const isTextElement = selectedElement && (selectedElement.type === "text" || selectedElement.type === "heading");

		return (
			<div className="flex flex-col gap-1 p-3">

				{elements.length === 0 ? (
					<div
						style={{
							padding: "24px",
							textAlign: "center",
							color: "var(--gray-9)",
						}}
					>
						<Text size="2" color="gray">
							No elements added yet
						</Text>
						<Text size="1" color="gray" style={{ marginTop: "4px" }}>
							Drag sections or elements to get started
						</Text>
					</div>
				) : (
					<>
						<div style={{ padding: "0 4px 8px 4px" }}>
							<Text size="1" color="gray">
								Drag layers to reorder
							</Text>
						</div>
						{elements.map((element, index) => (
							<div
								key={`${element.id}-${index}`}
								draggable
								onDragStart={() => handleDragStart(index)}
								onDragOver={handleDragOver}
								onDrop={(e) => handleDrop(e, index)}
								onDragEnd={handleDragEnd}
								onClick={() => onSelectElement?.(element.id)}
								style={{
									padding: "8px 12px",
									background: draggedIndex === index ? "var(--accent-3)" : selectedElementId === element.id ? "var(--accent-2)" : "var(--gray-1)",
									border: selectedElementId === element.id ? "1px solid var(--accent-6)" : "1px solid var(--gray-6)",
									borderRadius: "6px",
									cursor: draggedIndex === index ? "grabbing" : "grab",
									transition: "all 0.2s",
									display: "flex",
									alignItems: "center",
									gap: "8px",
									opacity: draggedIndex === index ? 0.5 : 1,
								}}
								onMouseEnter={(e) => {
									if (draggedIndex !== index) {
										e.currentTarget.style.background = "var(--accent-2)";
										e.currentTarget.style.borderColor = "var(--accent-6)";
									}
								}}
								onMouseLeave={(e) => {
									if (draggedIndex !== index) {
										e.currentTarget.style.background = selectedElementId === element.id ? "var(--accent-2)" : "var(--gray-1)";
										e.currentTarget.style.borderColor = selectedElementId === element.id ? "var(--accent-6)" : "var(--gray-6)";
									}
								}}
							>
								<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="var(--gray-9)" strokeWidth="2">
									<line x1="4" y1="8" x2="20" y2="8" />
									<line x1="4" y1="16" x2="20" y2="16" />
								</svg>
								<span style={{ fontSize: "12px", color: "var(--gray-9)", fontWeight: "bold" }}>
									{index + 1}
								</span>
								<Text size="2" weight="medium" style={{ flex: 1 }}>
									{element.name}
								</Text>
								<Badge size="1" color="gray">
									{element.type}
								</Badge>
								{!element.visible && (
									<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="var(--gray-9)" strokeWidth="2">
										<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
										<line x1="1" y1="1" x2="23" y2="23" />
									</svg>
								)}
							</div>
						))}
					</>
				)}
			</div>
		);
	};

	return (
		<div
			style={{
				width: "320px",
				background: "var(--gray-2)",
				display: "flex",
				flexDirection: "column",
				overflow: "hidden",
			}}
		>
			{/* Header */}
			<div
				style={{
					padding: "16px",
					borderBottom: "1px solid var(--gray-5)",
					background: "var(--gray-1)",
				}}
			>
				<Heading size="4" weight="bold">
					Elements & Structure
				</Heading>
			</div>

			{/* Tabs */}
			<div
				style={{
					display: "flex",
					borderBottom: "1px solid var(--gray-5)",
					background: "var(--gray-1)",
				}}
			>
				{tabs.map((tab) => (
					<button
						key={tab.id}
						type="button"
						onClick={() => setActiveTab(tab.id)}
						style={{
							flex: 1,
							padding: "10px 6px",
							background: activeTab === tab.id ? "var(--accent-3)" : "transparent",
							border: "none",
							borderBottom: activeTab === tab.id ? "2px solid var(--accent-9)" : "2px solid transparent",
							cursor: "pointer",
							transition: "all 0.15s ease",
							display: "flex",
							flexDirection: "column",
							alignItems: "center",
							gap: "3px",
							color: activeTab === tab.id ? "var(--accent-11)" : "var(--gray-10)",
						}}
						onMouseEnter={(e) => {
							if (activeTab !== tab.id) {
								e.currentTarget.style.background = "var(--gray-3)";
								e.currentTarget.style.color = "var(--gray-12)";
							}
						}}
						onMouseLeave={(e) => {
							if (activeTab !== tab.id) {
								e.currentTarget.style.background = "transparent";
								e.currentTarget.style.color = "var(--gray-10)";
							}
						}}
					>
						{getIcon(tab.icon)}
						<Text size="1" weight={activeTab === tab.id ? "bold" : "medium"}>
							{tab.label}
						</Text>
					</button>
				))}
			</div>

			{/* Content */}
			<div
				style={{
					flex: 1,
					overflowY: "auto",
					background: "var(--gray-1)",
				}}
			>
				{activeTab === "sections" && renderSections()}
				{activeTab === "elements" && renderElements()}
				{activeTab === "layers" && renderLayers()}
				{activeTab === "text-formatting" && renderTextFormatting()}
			</div>

			{/* Footer */}
			<div
				style={{
					padding: "12px 16px",
					borderTop: "1px solid var(--gray-5)",
					background: "var(--gray-1)",
				}}
			>
				<Text size="1" color="gray">
					{elements.length} element{elements.length !== 1 ? "s" : ""} on page
				</Text>
			</div>
		</div>
	);
}
