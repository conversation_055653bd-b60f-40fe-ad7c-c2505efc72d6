"use client";

import { But<PERSON> } from "frosted-ui";
import type { ReactNode } from "react";
import Image from "next/image";

interface NewLayoutProps {
	children: ReactNode;
	onSave?: () => void;
	onPublish?: () => void;
	onUndo?: () => void;
	onRedo?: () => void;
	canUndo?: boolean;
	canRedo?: boolean;
	hasUnsavedChanges?: boolean;
}

export function NewLayout({
	children,
	onSave,
	onPublish,
	onUndo,
	onRedo,
	canUndo = false,
	canRedo = false,
	hasUnsavedChanges = false,
}: NewLayoutProps) {

	return (
		<div
			style={{
				height: "100vh",
				display: "flex",
				flexDirection: "column",
				background: "var(--gray-1)",
			}}
		>
			{/* Fixed Top Bar */}
			<div
				style={{
					height: "56px",
					background: "var(--color-panel-solid)",
					borderBottom: "1px solid var(--gray-6)",
					display: "flex",
					alignItems: "center",
					justifyContent: "space-between",
					padding: "0 20px",
					position: "fixed",
					top: 0,
					left: 0,
					right: 0,
					zIndex: 50,
					backdropFilter: "blur(12px)",
				}}
			>
				{/* Left: Logo */}
				<div className="flex items-center">
					<div
						style={{
							width: "40px",
							height: "40px",
							borderRadius: "13px",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							overflow: "hidden",
							cursor: "pointer",
							transition: "transform 0.2s ease",
						}}
						onClick={() => window.location.reload()}
						onMouseEnter={(e) => {
							e.currentTarget.style.transform = "scale(1.1)";
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.transform = "scale(1)";
						}}
					>
						<Image
							src="/logo.svg"
							alt=" Logo"
							width={32}
							height={32}
							style={{
								objectFit: "contain",
							}}
						/>
					</div>
				</div>

				{/* Right: Actions */}
				<div className="flex items-center gap-2">
					{/* Undo/Redo */}
					<div className="flex items-center gap-1">
						<button
							type="button"
							onClick={onUndo}
							disabled={!canUndo}
							style={{
								width: "32px",
								height: "32px",
								borderRadius: "6px",
								background: "var(--gray-2)",
								border: "1px solid var(--gray-6)",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								cursor: canUndo ? "pointer" : "not-allowed",
								opacity: canUndo ? 1 : 0.5,
								transition: "all 0.2s",
							}}
							title="Undo"
						>
							<svg
								width="16"
								height="16"
								viewBox="0 0 24 24"
								fill="none"
								stroke="var(--gray-11)"
								strokeWidth="2"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
								/>
							</svg>
						</button>
						<button
							type="button"
							onClick={onRedo}
							disabled={!canRedo}
							style={{
								width: "32px",
								height: "32px",
								borderRadius: "6px",
								background: "var(--gray-2)",
								border: "1px solid var(--gray-6)",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								cursor: canRedo ? "pointer" : "not-allowed",
								opacity: canRedo ? 1 : 0.5,
								transition: "all 0.2s",
							}}
							title="Redo"
						>
							<svg
								width="16"
								height="16"
								viewBox="0 0 24 24"
								fill="none"
								stroke="var(--gray-11)"
								strokeWidth="2"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									d="M21 10H11a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6"
								/>
							</svg>
						</button>
					</div>

					{/* Save & Publish */}
					<div className="flex items-center gap-2">
						{onSave && (
							<Button
								size="2"
								variant="soft"
								onClick={onSave}
								style={{
									background: hasUnsavedChanges ? "var(--blue-3)" : "var(--gray-2)",
									border: hasUnsavedChanges ? "1px solid var(--blue-6)" : "1px solid var(--gray-6)",
									color: hasUnsavedChanges ? "var(--blue-11)" : "var(--gray-11)",
								}}
							>
								{hasUnsavedChanges ? "Save Changes" : "Save"}
							</Button>
						)}
						{onPublish && (
							<Button
								size="2"
								variant="solid"
								onClick={onPublish}
								style={{
									background: "var(--accent-9)",
									border: "none",
									boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
									position: "relative",
									overflow: "hidden",
								}}
							>
								<span style={{ position: "relative", zIndex: 1 }}>Publish</span>
								<div
									style={{
										position: "absolute",
										top: 0,
										left: 0,
										right: 0,
										bottom: 0,
										background: "linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0) 100%)",
										pointerEvents: "none",
									}}
								/>
							</Button>
						)}
					</div>
				</div>
			</div>

			{/* Main Workspace - 2-Pane Layout */}
			<div
				style={{
					flex: 1,
					display: "flex",
					marginTop: "56px", // Account for fixed top bar
					height: "calc(100vh - 56px)",
					overflow: "hidden",
				}}
			>
				{children}
			</div>
		</div>
	);
}
