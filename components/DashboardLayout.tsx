"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "frosted-ui";
import type { ReactNode } from "react";
import Image from "next/image";
import { OpenInNewTabButton } from "./OpenInNewTabButton";

interface DashboardLayoutProps {
	children: ReactNode;
	userName: string;
	username: string;
	companyTitle: string;
	onCreateFunnel?: () => void;
	activeSection?: string;
	onNavigate?: (section: string) => void;
}

type NavItem = {
	id: string;
	label: string;
	icon: string;
	href: string;
	badge?: string;
};

const navigationItems: NavItem[] = [
	{ id: "funnels", label: "Funnels", icon: "funnel", href: "/funnels" },
	{ id: "templates", label: "Templates", icon: "template", href: "/templates" },
	{ id: "products", label: "Products", icon: "product", href: "/products" },
	{ id: "leads", label: "Leads", icon: "leads", href: "/leads" },
	{ id: "analytics", label: "Analytics", icon: "analytics", href: "/analytics" },
	{ id: "settings", label: "Settings", icon: "settings", href: "/settings" },
];

export function DashboardLayout({
	children,
	userName,
	onCreateFunnel,
	activeSection = "funnels",
	onNavigate,
}: DashboardLayoutProps) {
	
	const isActive = (item: NavItem) => {
		return activeSection === item.id;
	};

	const getIcon = (iconName: string) => {
		const iconProps = {
			width: 24,
			height: 24,
			viewBox: "0 0 24 24",
			fill: "none",
			stroke: "currentColor",
			strokeWidth: 2,
			strokeLinecap: "round" as const,
			strokeLinejoin: "round" as const,
		};

		switch (iconName) {
			case "funnel":
				// Rocket icon for funnel
				return (
					<svg {...iconProps}>
						<path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z" />
						<path d="M12 15l-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z" />
						<path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0" />
						<path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5" />
					</svg>
				);
			case "template":
				// Document template icon
				return (
					<svg {...iconProps}>
						<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
						<polyline points="14,2 14,8 20,8" />
						<line x1="16" y1="13" x2="8" y2="13" />
						<line x1="16" y1="17" x2="8" y2="17" />
						<polyline points="10,9 9,9 8,9" />
					</svg>
				);
			case "automation":
				// Paper airplane icon for automation
				return (
					<svg {...iconProps}>
						<path d="M11.25 2.75H4.45C2.95883 2.75 1.75 3.95883 1.75 5.45V18.55C1.75 20.0412 2.95883 21.25 4.45 21.25H17.55C19.0412 21.25 20.25 20.0412 20.25 18.55V13.75" />
						<path d="M17.5 11.25C17.5 9.45507 14.5449 6.5 12.75 6.5C14.5449 6.5 17.5 4.04493 17.5 1.75C17.5 4.04493 19.9551 6.5 22.25 6.5C19.9551 6.5 17.5 9.45507 17.5 11.25Z" />
					</svg>
				);
			case "product":
				// Cube/package icon for products
				return (
					<svg {...iconProps}>
						<path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z" />
						<line x1="3" y1="6" x2="21" y2="6" />
						<path d="M16 10a4 4 0 0 1-8 0" />
					</svg>
				);
			case "leads":
				// Contact/lead icon
				return (
					<svg {...iconProps}>
						<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
						<circle cx="12" cy="7" r="4" />
					</svg>
				);
			case "analytics":
				// Bar chart icon
				return (
					<svg {...iconProps}>
						<path d="M18 20V10" />
						<path d="M12 20V4" />
						<path d="M6 20v-6" />
					</svg>
				);
			case "settings":
				// Gear icon
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="3" />
						<path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
					</svg>
				);
			default:
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="3" />
					</svg>
				);
		}
	};

	return (
		<div
			style={{
				minHeight: "100vh",
				background: "var(--gray-2)",
				display: "flex",
			}}
		>
			{/* Left Sidebar */}
			<div
				style={{
					width: "80px",
					background: "var(--gray-2)",
					display: "flex",
					flexDirection: "column",
					position: "fixed",
					height: "100vh",
					left: 0,
					top: 0,
					zIndex: 10,
				}}
			>
				{/* White background for entire sidebar */}
				<div
					style={{
						position: "absolute",
						left: "8px",
						right: "8px",
						top: "8px",
						bottom: "8px",
						background: "white",
						borderRadius: "20px",
						zIndex: 0,
					}}
				/>

				{/* Logo */}
				<div
					style={{
						padding: "20px 0 12px 0",
						display: "flex",
						justifyContent: "center",
						position: "relative",
						zIndex: 1,
					}}
				>
					<div
						style={{
							width: "32px",
							height: "32px",
							borderRadius: "8px",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							overflow: "visible",
							cursor: "pointer",
							transition: "transform 0.2s ease",
						}}
						onClick={() => window.location.reload()}
						onMouseEnter={(e) => {
							e.currentTarget.style.transform = "scale(1.1)";
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.transform = "scale(1)";
						}}
					>
						<Image
							src="/sidebar-logo.png"
							alt="FunnelFlow Logo"
							width={32}
							height={32}
							style={{
								objectFit: "contain",
							}}
						/>
					</div>
				</div>

				{/* Navigation */}
				<nav
					style={{
						flex: 1,
						padding: "20px 0",
						overflowY: "auto",
						display: "flex",
						flexDirection: "column",
						alignItems: "center",
						gap: "8px",
						position: "relative",
					}}
				>
					{navigationItems.map((item) => (
						<Tooltip 
							key={item.id} 
							content={item.label} 
							delayDuration={0}
							side="right"
							align="center"
							sideOffset={12}
						>
							<button
								type="button"
								className="whop-sidebar-button"
								onClick={() => onNavigate?.(item.id)}
								style={{
									width: "44px",
									height: "44px",
									background: isActive(item) ? "var(--gray-4)" : "transparent",
									border: "none",
									cursor: "pointer",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									zIndex: 1,
									position: "relative",
									color: isActive(item) ? "var(--gray-12)" : "var(--gray-10)",
									borderRadius: "12px",
									transition: "all 0.15s ease",
								}}
								onMouseEnter={(e) => {
									if (!isActive(item)) {
										e.currentTarget.style.background = "var(--gray-3)";
										e.currentTarget.style.color = "var(--gray-12)";
									}
								}}
								onMouseLeave={(e) => {
									if (!isActive(item)) {
										e.currentTarget.style.background = "transparent";
										e.currentTarget.style.color = "var(--gray-10)";
									} else {
										e.currentTarget.style.background = "var(--gray-4)";
										e.currentTarget.style.color = "var(--gray-12)";
									}
								}}
							>
								{getIcon(item.icon)}
								{item.badge && (
									<Badge 
										size="1" 
										color="gray"
										style={{
											position: "absolute",
											top: "-4px",
											right: "-4px",
											fontSize: "10px",
										}}
									>
										{item.badge}
									</Badge>
								)}
							</button>
						</Tooltip>
					))}
				</nav>

				{/* User Info */}
				<div
					style={{
						padding: "16px 0",
						display: "flex",
						justifyContent: "center",
						position: "relative",
						zIndex: 1,
					}}
				>
					<div
						style={{
							width: "40px",
							height: "40px",
							borderRadius: "50%",
							background: "var(--accent-4)",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							fontSize: "16px",
							fontWeight: "bold",
							color: "var(--accent-11)",
						}}
					>
						{userName.charAt(0).toUpperCase()}
					</div>
				</div>
			</div>

			{/* Main Content Area */}
			<div
				style={{
					flex: 1,
					marginLeft: "80px",
					padding: "16px",
					display: "flex",
					flexDirection: "column",
				}}
			>
				{/* Floating Page Container */}
				<div
					style={{
						background: "var(--color-panel-solid)",
						borderRadius: "12px",
						boxShadow: "0 4px 24px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04)",
						overflow: "hidden",
						display: "flex",
						flexDirection: "column",
						height: "calc(100vh - 32px)",
					}}
				>
					{/* Top Header */}
					<div
						style={{
							background: "var(--color-panel-solid)",
							borderBottom: "1px solid var(--gray-4)",
							position: "sticky",
							top: 0,
							zIndex: 5,
						}}
					>
					<div
						style={{
							padding: "16px 32px",
							display: "flex",
							alignItems: "center",
							justifyContent: "space-between",
						}}
					>
						{/* Breadcrumbs */}
						<nav
							style={{
								display: "flex",
								alignItems: "center",
								gap: "8px",
								fontSize: "14px",
								color: "var(--gray-9)",
							}}
						>
							<span style={{ color: "var(--gray-9)" }}>Dashboard</span>
							<span style={{ color: "var(--gray-8)" }}>/</span>
							<span style={{ color: "var(--gray-10)", fontWeight: "500" }}>
								{activeSection === "funnels" && "Funnels"}
								{activeSection === "templates" && "Templates"}
								{activeSection === "analytics" && "Analytics"}
								{activeSection === "products" && "Products"}
								{activeSection === "leads" && "Leads"}
								{activeSection === "settings" && "Settings"}
							</span>
						</nav>
						<div className="flex items-center gap-3">
							{/* Create Funnel Button */}
							{onCreateFunnel && (
								<Button size="2" onClick={onCreateFunnel}>
									<span className="flex items-center gap-2">
										<svg
											width="16"
											height="16"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
											role="img"
											aria-label="AI lightning bolt"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												d="M13 10V3L4 14h7v7l9-11h-7z"
											/>
										</svg>
										Create Funnel
									</span>
								</Button>
							)}

							{/* Edit in Tab Button */}
							<div style={{ minWidth: "140px" }}>
								<OpenInNewTabButton />
							</div>
						</div>
					</div>
				</div>

				{/* Page Content */}
				<div style={{ flex: 1, overflow: "auto" }}>{children}</div>
			</div>
		</div>
	</div>
	);
}