"use client";

import { useState } from "react";
import { Text, Button } from "frosted-ui";

interface GlobalAIChatProps {
	onAIRequest: (prompt: string) => Promise<void>;
	isProcessing: boolean;
}

export default function GlobalAIChat({ onAIRequest, isProcessing }: GlobalAIChatProps) {
	const [prompt, setPrompt] = useState("");
	const [isOpen, setIsOpen] = useState(false);
	const [status, setStatus] = useState("");

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!prompt.trim() || isProcessing) return;
		
		setStatus("Processing your request...");
		
		await onAIRequest(prompt);
		setPrompt("");
		setStatus("Done! Your funnel has been updated.");
		
		// Clear status after 3 seconds
		setTimeout(() => setStatus(""), 3000);
	};

	// Always show the AI button, but with different styles when open
	return (
		<>
			{/* AI Button - Always visible */}
			<div
				style={{
					position: "fixed",
					bottom: "20px",
					right: "20px",
					zIndex: 1001,
				}}
			>
				<button
					onClick={() => setIsOpen(!isOpen)}
					style={{
						display: "flex",
						alignItems: "center",
						gap: "8px",
						padding: "12px 20px",
						background: isOpen ? "#1976d2" : "white",
						border: isOpen ? "1px solid #1976d2" : "1px solid #e3f2fd",
						borderRadius: "24px",
						color: isOpen ? "white" : "#1976d2",
						fontSize: "14px",
						fontWeight: "600",
						cursor: "pointer",
						boxShadow: isOpen ? "0 6px 20px rgba(25, 118, 210, 0.5)" : "inset 0 2px 4px rgba(25, 118, 210, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1)",
						transition: "all 0.3s ease",
						position: "relative",
						overflow: "hidden",
						transform: isOpen ? "translateY(-2px)" : "translateY(0)",
					}}
					onMouseEnter={(e) => {
						e.currentTarget.style.transform = "translateY(-2px)";
						e.currentTarget.style.boxShadow = "0 6px 20px rgba(25, 118, 210, 0.4)";
						e.currentTarget.style.background = isOpen ? "#1565c0" : "#f8f9fa";
					}}
					onMouseLeave={(e) => {
						e.currentTarget.style.transform = isOpen ? "translateY(-2px)" : "translateY(0)";
						e.currentTarget.style.boxShadow = isOpen ? "0 6px 20px rgba(25, 118, 210, 0.5)" : "inset 0 2px 4px rgba(25, 118, 210, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1)";
						e.currentTarget.style.background = isOpen ? "#1976d2" : "white";
					}}
				>
					{/* Sparkles Icon */}
					<svg
						width="16"
						height="16"
						viewBox="0 0 24 24"
						fill="none"
						stroke={isOpen ? "white" : "url(#iconGradient)"}
						strokeWidth="2"
						strokeLinecap="round"
						strokeLinejoin="round"
					>
						{!isOpen && (
							<defs>
								<linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
									<stop offset="0%" style={{ stopColor: "#1976d2", stopOpacity: 1 }} />
									<stop offset="100%" style={{ stopColor: "#42a5f5", stopOpacity: 1 }} />
								</linearGradient>
							</defs>
						)}
						<path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
						<path d="M20 3v4" />
						<path d="M22 5h-4" />
						<path d="M4 17v2" />
						<path d="M5 18H3" />
					</svg>
					<span style={{ 
						background: isOpen ? "none" : "linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)", 
						WebkitBackgroundClip: isOpen ? "initial" : "text", 
						WebkitTextFillColor: isOpen ? "inherit" : "transparent", 
						backgroundClip: isOpen ? "initial" : "text" 
					}}>AI Mode</span>
					
					{/* Wave shine animation */}
					<div
						style={{
							position: "absolute",
							top: 0,
							left: "-100%",
							width: "100%",
							height: "100%",
							background: "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent)",
							animation: "waveShine 2s infinite",
						}}
					/>
					
					{/* Wave shine animation */}
					<style jsx>{`
						@keyframes waveShine {
							0% { left: -100%; }
							100% { left: 100%; }
						}
					`}</style>
				</button>
			</div>

			{/* Chat Bar - Only show when open */}
			{isOpen && (
				<div
					style={{
						position: "fixed",
						bottom: "20px",
						left: "calc(50% + 100px)",
						transform: "translateX(-50%)",
						width: "500px",
						background: "rgba(255, 255, 255, 0.1)",
						backdropFilter: "blur(20px)",
						WebkitBackdropFilter: "blur(20px)",
						borderRadius: "16px",
						boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.08)",
						zIndex: 1000,
						overflow: "hidden",
						border: "1px solid rgba(255, 255, 255, 0.2)",
					}}
				>
			{/* Status Bar */}
			{status && (
				<div
					style={{
						padding: "12px 20px",
						background: "rgba(248, 249, 250, 0.8)",
						backdropFilter: "blur(10px)",
						borderBottom: "1px solid rgba(233, 236, 239, 0.5)",
						textAlign: "center",
						fontSize: "14px",
						color: "var(--gray-11)",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						gap: "8px",
					}}
				>
					{status === "Processing your request..." && (
						<div style={{ display: "flex", gap: "4px" }}>
							<div style={{ width: "4px", height: "4px", borderRadius: "50%", background: "var(--gray-8)", animation: "bounce 1.4s infinite ease-in-out both" }}></div>
							<div style={{ width: "4px", height: "4px", borderRadius: "50%", background: "var(--gray-8)", animation: "bounce 1.4s infinite ease-in-out both", animationDelay: "0.16s" }}></div>
							<div style={{ width: "4px", height: "4px", borderRadius: "50%", background: "var(--gray-8)", animation: "bounce 1.4s infinite ease-in-out both", animationDelay: "0.32s" }}></div>
						</div>
					)}
					<span>{status}</span>
				</div>
			)}

			{/* Input Bar */}
			<div
				style={{
					padding: "8px 12px",
					background: "transparent",
					display: "flex",
					alignItems: "center",
					gap: "8px",
				}}
			>
				<form onSubmit={handleSubmit} style={{ display: "flex", alignItems: "center", flex: 1, background: "rgba(248, 249, 250, 0.8)", borderRadius: "12px", padding: "6px 12px", border: "1px solid rgba(233, 236, 239, 0.5)", backdropFilter: "blur(10px)" }}>
					<input
						type="text"
						value={prompt}
						onChange={(e) => setPrompt(e.target.value)}
						placeholder="Ask me to modify your funnel..."
						disabled={isProcessing}
						style={{
							flex: 1,
							padding: "6px 8px",
							border: "none",
							background: "transparent",
							fontSize: "14px",
							outline: "none",
							fontFamily: "inherit",
							color: "#333",
						}}
						onFocus={(e) => {
							e.currentTarget.parentElement!.style.borderColor = "rgba(233, 236, 239, 0.5)";
							e.currentTarget.parentElement!.style.boxShadow = "none";
						}}
						onBlur={(e) => {
							e.currentTarget.parentElement!.style.borderColor = "rgba(233, 236, 239, 0.5)";
							e.currentTarget.parentElement!.style.boxShadow = "none";
						}}
					/>
					<button
						type="submit"
						disabled={!prompt.trim() || isProcessing}
						style={{
							width: "28px",
							height: "28px",
							background: prompt.trim() && !isProcessing ? "#1976d2" : "#ccc",
							border: "none",
							borderRadius: "50%",
							color: "white",
							cursor: prompt.trim() && !isProcessing ? "pointer" : "not-allowed",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							transition: "all 0.2s ease",
							flexShrink: 0,
						}}
					>
						<svg
							width="12"
							height="12"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
							strokeLinecap="round"
							strokeLinejoin="round"
						>
							<path d="M22 2L11 13" />
							<path d="M22 2L15 22L11 13L2 9L22 2Z" />
						</svg>
					</button>
				</form>
			</div>


					{/* Loading Animation CSS */}
					<style jsx>{`
						@keyframes bounce {
							0%, 80%, 100% { 
								transform: scale(0);
							} 40% { 
								transform: scale(1);
							}
						}
					`}</style>
				</div>
			)}
		</>
	);
}
