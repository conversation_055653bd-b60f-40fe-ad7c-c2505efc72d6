"use client";

import type {
	FunnelPage,
	FunnelSchema,
	FunnelSection,
} from "@/lib/types/funnel";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Heading, Text } from "frosted-ui";
import Image from "next/image";
import React from "react";
import { FunnelEditor } from "./editor/FunnelEditor";
import { PagePreview } from "./PagePreview";

interface FunnelBuilderProps {
	funnel: FunnelSchema | null;
	onSaveDraft?: () => void;
	onPublish?: () => void;
	onUpdateFunnel?: (funnel: FunnelSchema) => void;
}

export function FunnelBuilder({
	funnel,
	onSaveDraft,
	onPublish,
	onUpdateFunnel,
}: FunnelBuilderProps) {
	const [editingPlanIds, setEditingPlanIds] = React.useState<{
		[pageId: string]: string;
	}>({});
	const [isEditingPlans, setIsEditingPlans] = React.useState<{
		[pageId: string]: boolean;
	}>({});
	const [showEditor, setShowEditor] = React.useState(false);
	const [editingPageIndex, setEditingPageIndex] = React.useState<number | null>(null);
	const [previewingPage, setPreviewingPage] = React.useState<FunnelPage | null>(null);

	const handleSavePlanIds = (pageId: string) => {
		if (!funnel || !onUpdateFunnel) return;

		const planIds =
			editingPlanIds[pageId]
				?.split(",")
				.map((id) => id.trim())
				.filter(Boolean) || [];

		const updatedPages = funnel.pages.map((page) =>
			page.id === pageId ? { ...page, whopPlanIds: planIds } : page,
		);

		onUpdateFunnel({
			...funnel,
			pages: updatedPages,
		});

		setIsEditingPlans({ ...isEditingPlans, [pageId]: false });
	};

	const handleEditorSave = (updatedFunnel: FunnelSchema) => {
		if (onUpdateFunnel) {
			onUpdateFunnel(updatedFunnel);
		}
		setShowEditor(false);
		setEditingPageIndex(null);
	};

	const handleEditPage = (pageIndex: number) => {
		setEditingPageIndex(pageIndex);
		setShowEditor(true);
	};

	const handlePreviewPage = (page: FunnelPage) => {
		setPreviewingPage(page);
	};

	// Show editor if requested
	if (showEditor && funnel) {
		return (
			<FunnelEditor
				funnel={funnel}
				onSave={handleEditorSave}
				onClose={() => {
					setShowEditor(false);
					setEditingPageIndex(null);
				}}
				initialPageIndex={editingPageIndex ?? 0}
			/>
		);
	}

	if (!funnel) {
		return (
			<Card
				style={{
					minHeight: "384px",
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
					border: "1px solid var(--gray-4)",
					background: "var(--gray-2)",
				}}
			>
				<div className="flex flex-col items-center gap-4">
					<div
						style={{
							width: "80px",
							height: "80px",
							borderRadius: "20px",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							overflow: "hidden",
						}}
					>
						<Image
							src="/logo.svg"
							alt="FunnelFow AI Logo"
							width={64}
							height={64}
							style={{
								objectFit: "contain",
							}}
						/>
					</div>
					<Heading size="4">No funnel created</Heading>
					<Text color="gray" size="2">
						Get started by creating a funnel with AI
					</Text>
				</div>
			</Card>
		);
	}

	const renderSection = (section: FunnelSection) => {
		switch (section.type) {
			case "hero":
				return (
					<div
						key={section.id}
						style={{
							background: "rgba(255, 255, 255, 0.05)",
							backdropFilter: "blur(12px)",
							WebkitBackdropFilter: "blur(12px)",
							border: "1px solid rgba(255, 255, 255, 0.1)",
							borderRadius: "var(--radius-3)",
							padding: "24px",
							boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
						}}
					>
						<div
							className="flex flex-col items-center gap-3"
							style={{ maxWidth: "36rem", margin: "0 auto" }}
						>
							<Heading size="6" align="center">
								{section.heading}
							</Heading>
							<Text size="2" align="center" color="gray">
								{section.content}
							</Text>
							{section.cta && (
								<Button
									size="2"
									variant={section.cta.style === "primary" ? "solid" : "soft"}
								>
									{section.cta.text}
								</Button>
							)}
						</div>
					</div>
				);

			case "features":
				return (
					<div key={section.id} className="flex flex-col gap-2">
						<Heading size="4" align="center">
							{section.heading}
						</Heading>
						<Text size="1" color="gray" align="center">
							{section.content}
						</Text>
						{section.items && (
							<div className="grid grid-cols-3 gap-2 mt-2">
								{section.items.slice(0, 3).map((item) => (
									<div
										key={`${section.id}-${item.title}`}
										className="flex flex-col items-center gap-1 p-2"
									>
										<div
											className="flex items-center justify-center"
											style={{
												width: "32px",
												height: "32px",
												borderRadius: "50%",
												background: "var(--accent-3)",
											}}
										>
											<svg
												width="16"
												height="16"
												viewBox="0 0 24 24"
												fill="none"
												stroke="var(--accent-9)"
												strokeWidth="2"
												role="img"
												aria-label="Feature icon"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													d="M5 13l4 4L19 7"
												/>
											</svg>
										</div>
										<Text size="1" weight="bold" align="center">
											{item.title}
										</Text>
									</div>
								))}
							</div>
						)}
					</div>
				);

			case "pricing":
				return (
					<div key={section.id} className="flex flex-col gap-4">
						{/* Header Section */}
						<div className="text-center mb-6">
							<Heading size="5" weight="bold" className="mb-2">
								{section.heading || "Pricing Plans"}
							</Heading>
							<Text size="2" color="gray">
								{section.content || "Choose the plan that's right for you"}
							</Text>
						</div>

						{/* Pricing Cards Grid */}
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
							{/* Basic Plan */}
							<div className="bg-white rounded-2xl border border-gray-200 p-8 relative hover:shadow-lg transition-all duration-300">
								<div className="text-center mb-6">
									<h3 className="text-xl font-bold text-gray-900 mb-2">Basic</h3>
									<div className="mb-4">
										<span className="text-4xl font-bold text-blue-600">$29</span>
										<span className="text-gray-500 ml-1">/month</span>
									</div>
								</div>
								<ul className="space-y-3 mb-8">
									<li className="flex items-center text-gray-700">
										<svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
										</svg>
										Basic Features
									</li>
									<li className="flex items-center text-gray-700">
										<svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
										</svg>
										Email Support
									</li>
									<li className="flex items-center text-gray-700">
										<svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
										</svg>
										5GB Storage
									</li>
								</ul>
								<button
									className="w-full py-3 px-6 bg-gray-100 text-gray-800 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-200"
									style={{
										boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)'
									}}
								>
									Get Started
								</button>
							</div>

							{/* Pro Plan - Popular */}
							<div className="bg-white rounded-2xl border-2 border-blue-500 p-8 relative hover:shadow-xl transition-all duration-300 scale-105">
								<div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
									<span className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-bold">
										MOST POPULARRR
									</span>
								</div>
								<div className="text-center mb-6">
									<h3 className="text-xl font-bold text-gray-900 mb-2">Pro</h3>
									<div className="mb-4">
										<span className="text-4xl font-bold text-blue-600">$79</span>
										<span className="text-gray-500 ml-1">/month</span>
									</div>
								</div>
								<ul className="space-y-3 mb-8">
									<li className="flex items-center text-gray-700">
										<svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
										</svg>
										All Features
									</li>
									<li className="flex items-center text-gray-700">
										<svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
										</svg>
										Priority Support
									</li>
									<li className="flex items-center text-gray-700">
										<svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
										</svg>
										50GB Storage
									</li>
								</ul>
								<button
									className="w-full py-3 px-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200"
									style={{
										boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.2)'
									}}
								>
									Get Started
								</button>
							</div>

							{/* Enterprise Plan */}
							<div className="bg-white rounded-2xl border border-gray-200 p-8 relative hover:shadow-lg transition-all duration-300">
								<div className="text-center mb-6">
									<h3 className="text-xl font-bold text-gray-900 mb-2">Enterprise</h3>
									<div className="mb-4">
										<span className="text-4xl font-bold text-blue-600">$199</span>
										<span className="text-gray-500 ml-1">/month</span>
									</div>
								</div>
								<ul className="space-y-3 mb-8">
									<li className="flex items-center text-gray-700">
										<svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
										</svg>
										Everything
									</li>
									<li className="flex items-center text-gray-700">
										<svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
										</svg>
										24/7 Support
									</li>
									<li className="flex items-center text-gray-700">
										<svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
										</svg>
										Unlimited Storage
									</li>
								</ul>
								<button
									className="w-full py-3 px-6 bg-gray-100 text-gray-800 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-200"
									style={{
										boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)'
									}}
								>
									Get Started
								</button>
							</div>
						</div>
					</div>
				);

			case "problem":
				return (
					<div key={section.id} className="flex flex-col gap-2">
						<Heading size="4" align="center">
							{section.heading}
						</Heading>
						<Text size="1" color="gray" align="center">
							{section.content}
						</Text>
						<div className="p-3 bg-red-50 border border-red-200 rounded-lg mt-2">
							<Text size="1" color="red">
								Common pain points and challenges
							</Text>
						</div>
					</div>
				);

			case "solution":
				return (
					<div key={section.id} className="flex flex-col gap-2">
						<Heading size="4" align="center">
							{section.heading}
						</Heading>
						<Text size="1" color="gray" align="center">
							{section.content}
						</Text>
						<div className="p-3 bg-blue-50 border border-blue-200 rounded-lg mt-2">
							<Text size="1" color="blue">
								The perfect solution to your problems
							</Text>
						</div>
					</div>
				);

			case "testimonials":
				return (
					<div key={section.id} className="flex flex-col gap-2">
						<Heading size="4" align="center">
							{section.heading}
						</Heading>
						{section.items && section.items.length > 0 && (
							<div className="p-2 bg-[var(--gray-3)] rounded-lg">
								<Text size="1" color="gray">
									&ldquo;{section.items[0].description}&rdquo;
								</Text>
								<Text size="1" weight="bold" style={{ marginTop: "4px" }}>
									{section.items[0].title}
								</Text>
							</div>
						)}
					</div>
				);

			case "faq":
				return (
					<div key={section.id} className="flex flex-col gap-2">
						<Heading size="4" align="center">
							{section.heading}
						</Heading>
						<Text size="1" color="gray" align="center">
							{section.content}
						</Text>
						<div className="space-y-2 mt-2">
							<div className="p-2 bg-[var(--gray-3)] rounded-lg">
								<Text size="1" weight="bold">
									How does this work?
								</Text>
								<Text size="1" color="gray">
									Simple and effective solution...
								</Text>
							</div>
							<div className="p-2 bg-[var(--gray-3)] rounded-lg">
								<Text size="1" weight="bold">
									Is there a guarantee?
								</Text>
								<Text size="1" color="gray">
									30-day money-back guarantee...
								</Text>
							</div>
						</div>
					</div>
				);

			case "cta":
				return (
					<div
						key={section.id}
						style={{
							background: "rgba(255, 255, 255, 0.08)",
							backdropFilter: "blur(16px)",
							WebkitBackdropFilter: "blur(16px)",
							border: "1px solid rgba(255, 255, 255, 0.15)",
							borderRadius: "var(--radius-3)",
							padding: "16px",
							boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
						}}
					>
						<div className="flex flex-col items-center gap-2">
							<Heading size="4" align="center" style={{ color: "white" }}>
								{section.heading}
							</Heading>
							<Text
								size="1"
								align="center"
								style={{ color: "var(--accent-2)" }}
							>
								{section.content}
							</Text>
							{section.cta && (
								<Button size="2" variant="surface" highContrast>
									{section.cta.text}
								</Button>
							)}
						</div>
					</div>
				);

			case "faq":
				return (
					<div key={section.id} className="flex flex-col gap-2">
						<Heading size="4" align="center">
							{section.heading}
						</Heading>
						{section.items && section.items.length > 0 && (
							<div className="p-2 bg-[var(--gray-3)] rounded-lg">
								<Text size="1" weight="bold">
									{section.items[0].title}
								</Text>
								<Text size="1" color="gray" style={{ marginTop: "4px" }}>
									{section.items[0].description}
								</Text>
							</div>
						)}
					</div>
				);

			default:
				return (
					<div key={section.id} className="flex flex-col gap-1">
						<Text size="2" weight="bold">
							{section.heading}
						</Text>
						<Text size="1" color="gray">
							{section.content}
						</Text>
					</div>
				);
		}
	};

	// Convert sections to pages if needed for backward compatibility
	const pages: FunnelPage[] = funnel.pages || [
		{
			id: "page-1",
			name: "Landing Page",
			type: "landing",
			sections: funnel.sections,
			order: 1,
		},
	];

	const getPageTypeColor = (type: string) => {
		switch (type) {
			case "landing":
				return "blue";
			case "upsell":
				return "green";
			case "downsell":
				return "amber";
			case "thank-you":
				return "purple";
			case "checkout":
				return "cyan";
			default:
				return "gray";
		}
	};

	const getPageTypeIcon = (type: string) => {
		// Returns SVG icon based on page type
		const iconPath = (() => {
			switch (type) {
				case "landing":
					return "M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6";
				case "upsell":
					return "M13 7h8m0 0v8m0-8l-8 8-4-4-6 6";
				case "downsell":
					return "M13 17h8m0 0V9m0 8l-8-8-4 4-6-6";
				case "thank-you":
					return "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z";
				case "checkout":
					return "M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3v-8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z";
				default:
					return "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z";
			}
		})();

		return (
			<svg
				width="20"
				height="20"
				viewBox="0 0 24 24"
				fill="none"
				stroke="currentColor"
				strokeWidth="2"
				role="img"
				aria-label={`${type} page icon`}
			>
				<path strokeLinecap="round" strokeLinejoin="round" d={iconPath} />
			</svg>
		);
	};

	return (
		<>
			{/* Page Preview Modal */}
			<PagePreview
				page={previewingPage}
				isOpen={previewingPage !== null}
				onClose={() => setPreviewingPage(null)}
				theme={funnel?.theme}
			/>

			<div className="flex flex-col gap-6">
				{/* Funnel Header with Frosted Glass Effect */}
			<div
				style={{
					background: "rgba(255, 255, 255, 0.05)",
					backdropFilter: "blur(12px)",
					WebkitBackdropFilter: "blur(12px)",
					border: "1px solid rgba(255, 255, 255, 0.1)",
					borderRadius: "16px",
					padding: "24px",
					boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
				}}
			>
				<div className="flex justify-between items-start">
					<div>
						<div className="flex items-center gap-3 mb-2">
							<Heading size="8">{funnel.title}</Heading>
							<Badge size="2" color="indigo">
								{pages.length} {pages.length === 1 ? "Page" : "Pages"}
							</Badge>
						</div>
						<Text color="gray" size="3">
							{funnel.description}
						</Text>
					</div>
					<Button
						size="3"
						variant="solid"
						onClick={() => setShowEditor(true)}
						style={{
							background: "linear-gradient(135deg, var(--accent-9) 0%, var(--accent-10) 100%)",
						}}
					>
						<span className="flex items-center gap-2">
							<svg
								width="20"
								height="20"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
								/>
							</svg>
							Edit with Drag & Drop
						</span>
					</Button>
				</div>
			</div>

			{/* Funnel Flow Visualization */}
			<div className="flex flex-col gap-4">
				<div className="flex items-center gap-2 mb-2">
					<svg
						width="20"
						height="20"
						viewBox="0 0 24 24"
						fill="none"
						stroke="var(--accent-9)"
						strokeWidth="2"
						role="img"
						aria-label="Funnel flow chart"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
						/>
					</svg>
					<Heading size="5">Funel Flow</Heading>
					<Text size="2" color="gray">
						({pages.length} step{pages.length !== 1 ? "s" : ""} to conversion)
					</Text>
				</div>

				{/* Pages Chain with Connectors */}
				<div className="relative">
					{pages.map((page, index) => (
						<div key={page.id} className="relative">
							{/* Page Card with Frosted Glass */}
							<div
								style={{
									background: "rgba(255, 255, 255, 0.03)",
									backdropFilter: "blur(20px)",
									WebkitBackdropFilter: "blur(20px)",
									border: "1px solid rgba(255, 255, 255, 0.08)",
									borderRadius: "20px",
									padding: "28px",
									boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
									marginBottom: index < pages.length - 1 ? "32px" : "0",
									position: "relative",
									overflow: "hidden",
								}}
							>
								{/* Subtle Color Accent */}
								<div
									style={{
										position: "absolute",
										top: 0,
										left: 0,
										right: 0,
										height: "100%",
										background: `var(--${getPageTypeColor(page.type)}-2)`,
										opacity: 0.1,
										pointerEvents: "none",
									}}
								/>

								{/* Content */}
								<div style={{ position: "relative", zIndex: 1 }}>
									{/* Page Header */}
									<div className="flex items-center justify-between mb-4">
										<div className="flex items-center gap-3">
											<div
												className="flex items-center justify-center"
												style={{
													width: "48px",
													height: "48px",
													borderRadius: "12px",
													background: `var(--${getPageTypeColor(page.type)}-4)`,
													border: `2px solid var(--${getPageTypeColor(page.type)}-7)`,
													fontSize: "24px",
												}}
											>
												{getPageTypeIcon(page.type)}
											</div>
											<div>
												<div className="flex items-center gap-2">
													<Heading size="6">{page.name}</Heading>
													<Badge
														color={
															getPageTypeColor(page.type) as
																| "blue"
																| "green"
																| "amber"
																| "purple"
																| "cyan"
																| "gray"
														}
														size="1"
													>
														{page.type}
													</Badge>
												</div>
												<Text size="1" color="gray">
													Step {index + 1} of {pages.length}
												</Text>
											</div>
										</div>
										<div
											className="flex items-center justify-center"
											style={{
												width: "32px",
												height: "32px",
												borderRadius: "50%",
												background: "var(--gray-4)",
												fontWeight: "bold",
												fontSize: "14px",
											}}
										>
											{index + 1}
										</div>
									</div>

									{/* Page Sections Preview */}
									<div className="flex flex-col gap-3">
										{page.sections.map((section) => renderSection(section))}
									</div>

									{/* Page Stats */}
									<div
										className="flex items-center gap-4 mt-4 pt-4"
										style={{
											borderTop: "1px solid var(--gray-6)",
										}}
									>
										<div className="flex items-center gap-2">
											<svg
												width="16"
												height="16"
												viewBox="0 0 24 24"
												fill="none"
												stroke="var(--gray-10)"
												strokeWidth="2"
												role="img"
												aria-label="Sections list"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													d="M4 6h16M4 10h16M4 14h16M4 18h16"
												/>
											</svg>
											<Text size="1" color="gray">
												{page.sections.length} section
												{page.sections.length !== 1 ? "s" : ""}
											</Text>
										</div>
										<Button
											size="1"
											variant="soft"
											onClick={() => handleEditPage(index)}
										>
											Edit Page
										</Button>
										<Button
											size="1"
											variant="ghost"
											onClick={() => handlePreviewPage(page)}
										>
											Preview
										</Button>
									</div>

									{/* Whop Plan IDs for Checkout Pages */}
									{page.type === "checkout" && (
										<div
											className="mt-4 pt-4"
											style={{
												borderTop: "1px solid var(--gray-6)",
											}}
										>
											<div className="flex items-start gap-3">
												<div className="flex-1">
													<div className="flex items-center gap-2 mb-2">
														<svg
															width="16"
															height="16"
															viewBox="0 0 24 24"
															fill="none"
															stroke="var(--cyan-9)"
															strokeWidth="2"
															role="img"
															aria-label="Whop logo"
														>
															<path
																strokeLinecap="round"
																strokeLinejoin="round"
																d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3v-8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
															/>
														</svg>
														<Text size="2" weight="bold">
															Whop Plan IDs
														</Text>
														<Badge color="cyan" size="1">
															Checkout
														</Badge>
													</div>
													{isEditingPlans[page.id] ? (
														<div className="flex flex-col gap-2">
															<input
																type="text"
																placeholder="plan_123, plan_456, plan_789"
																value={editingPlanIds[page.id] || ""}
																onChange={(e) =>
																	setEditingPlanIds({
																		...editingPlanIds,
																		[page.id]: e.target.value,
																	})
																}
																style={{
																	width: "100%",
																	padding: "8px 12px",
																	borderRadius: "var(--radius-2)",
																	border: "1px solid var(--gray-7)",
																	background: "var(--gray-2)",
																	color: "var(--gray-12)",
																	fontSize: "14px",
																}}
															/>
															<Text size="1" color="gray">
																Enter Whop plan IDs separated by commas
															</Text>
															<div className="flex items-center gap-2 mt-1">
																<Button
																	size="1"
																	variant="solid"
																	onClick={() => handleSavePlanIds(page.id)}
																>
																	Save Plan IDs
																</Button>
																<Button
																	size="1"
																	variant="soft"
																	onClick={() =>
																		setIsEditingPlans({
																			...isEditingPlans,
																			[page.id]: false,
																		})
																	}
																>
																	Cancel
																</Button>
															</div>
														</div>
													) : (
														<div className="flex items-center gap-2">
															{page.whopPlanIds &&
															page.whopPlanIds.length > 0 ? (
																<div className="flex items-center gap-2 flex-wrap">
																	{page.whopPlanIds.map((planId) => (
																		<Badge key={planId} color="cyan" size="1">
																			{planId}
																		</Badge>
																	))}
																	<Button
																		size="1"
																		variant="soft"
																		onClick={() => {
																			setEditingPlanIds({
																				...editingPlanIds,
																				[page.id]:
																					page.whopPlanIds?.join(", ") || "",
																			});
																			setIsEditingPlans({
																				...isEditingPlans,
																				[page.id]: true,
																			});
																		}}
																	>
																		Edit
																	</Button>
																</div>
															) : (
																<div className="flex items-center gap-2">
																	<Text size="1" color="gray">
																		No plan IDs configured
																	</Text>
																	<Button
																		size="1"
																		variant="soft"
																		onClick={() => {
																			setEditingPlanIds({
																				...editingPlanIds,
																				[page.id]: "",
																			});
																			setIsEditingPlans({
																				...isEditingPlans,
																				[page.id]: true,
																			});
																		}}
																	>
																		Add Plan IDs
																	</Button>
																</div>
															)}
														</div>
													)}
												</div>
											</div>
										</div>
									)}
								</div>
							</div>

							{/* Flow Connector Arrow */}
							{index < pages.length - 1 && (
								<div
									className="flex items-center justify-center"
									style={{
										height: "32px",
										position: "relative",
									}}
								>
									<div
										style={{
											position: "absolute",
											width: "2px",
											height: "100%",
											background: `var(--${getPageTypeColor(page.type)}-6)`,
										}}
									/>
									<div
										className="flex items-center justify-center"
										style={{
											width: "40px",
											height: "40px",
											borderRadius: "50%",
											background: "var(--gray-3)",
											border: "2px solid var(--gray-6)",
											position: "relative",
											zIndex: 2,
										}}
									>
										<svg
											width="20"
											height="20"
											viewBox="0 0 24 24"
											fill="none"
											stroke="var(--accent-9)"
											strokeWidth="2"
											role="img"
											aria-label="Flow connector arrow"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												d="M19 14l-7 7m0 0l-7-7m7 7V3"
											/>
										</svg>
									</div>
								</div>
							)}
						</div>
					))}
				</div>
			</div>

			{/* Funnel Actions */}
			<div
				style={{
					background: "rgba(255, 255, 255, 0.03)",
					backdropFilter: "blur(12px)",
					WebkitBackdropFilter: "blur(12px)",
					border: "1px solid rgba(255, 255, 255, 0.08)",
					borderRadius: "16px",
					padding: "20px",
				}}
			>
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<Text size="2" weight="bold">
							Ready to publish?
						</Text>
						<Badge color="green" size="1">
							All pages configured
						</Badge>
					</div>
					<div className="flex items-center gap-2">
						<Button
							size="2"
							variant="soft"
							onClick={onSaveDraft}
							disabled={!onSaveDraft}
						>
							Save Draft
						</Button>
						<Button
							size="2"
							variant="solid"
							onClick={onPublish}
							disabled={!onPublish}
						>
							Publish Funnel
						</Button>
					</div>
				</div>
			</div>
			</div>
		</>
	);
}
