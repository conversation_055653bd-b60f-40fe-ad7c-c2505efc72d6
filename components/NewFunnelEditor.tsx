"use client";

import { useState, useCallback, useEffect } from "react";
import type { FunnelSchema } from "@/lib/types/funnel";
import { NewLayout } from "./NewLayout";
import { LeftSidebar } from "./LeftSidebar";
import { CenterCanvas } from "./CenterCanvas";
import { FormattingSidebar } from "./FormattingSidebar";
import { Callout } from "frosted-ui";
// import { RightSidebar } from "./RightSidebar"; // Temporarily commented out
import GlobalAIChat from "./GlobalAIChat";
import { InlineElementTooltip } from "./InlineElementTooltip";

interface NewFunnelEditorProps {
	funnel: FunnelSchema;
	onSave: (funnel: FunnelSchema) => void;
	onClose: () => void;
}

interface ElementProperties {
	padding?: { top: number; right: number; bottom: number; left: number };
	textColor?: string;
	textAlign?: string;
	visible?: boolean;
	animation?: string;
	fontSize?: number | string;
	backgroundColor?: string;
	imageUrl?: string;
	altText?: string;
	linkUrl?: string;
	actionType?: string;
	inputType?: string;
	placeholder?: string;
	content?: string;
	[key: string]: unknown;
}

interface EditorElement {
	id: string;
	type: string;
	name: string;
	content: string;
	properties: ElementProperties;
}

export function NewFunnelEditor({
	funnel,
	onSave,
}: NewFunnelEditorProps) {
	// Convert funnel sections to editor elements on initialization
	const initialElements: EditorElement[] = funnel.pages.flatMap(page => 
		page.sections?.map(section => ({
			id: section.id,
			type: section.type,
			name: getElementName(section.type),
			content: section.content || getDefaultContent(section.type),
			// Merge saved properties with defaults
			properties: { ...getDefaultProperties(section.type), ...section.properties },
		})) || []
	);

	const [elements, setElements] = useState<EditorElement[]>(initialElements);
	const [selectedElementId, setSelectedElementId] = useState<string | null>(null);
	const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
	const [canUndo, setCanUndo] = useState(false);
	const [canRedo, setCanRedo] = useState(false);
	const [history, setHistory] = useState<EditorElement[][]>([initialElements]);
	const [historyIndex, setHistoryIndex] = useState(0);
	const [isProcessingAI, setIsProcessingAI] = useState(false);
	const [tooltipState, setTooltipState] = useState<{
		element: EditorElement;
		position: { x: number; y: number };
		context?: { heroPart?: string };
	} | null>(null);
	const [showToast, setShowToast] = useState(false);
	const [toastMessage, setToastMessage] = useState("");


	// Toast notification function
	const showToastNotification = useCallback((message: string) => {
		setToastMessage(message);
		setShowToast(true);
		setTimeout(() => setShowToast(false), 3000);
	}, []);

	const addToHistory = useCallback((newElements: EditorElement[]) => {
		const newHistory = history.slice(0, historyIndex + 1);
		newHistory.push([...newElements]);
		setHistory(newHistory);
		setHistoryIndex(newHistory.length - 1);
		setCanUndo(newHistory.length > 1);
		setCanRedo(false);
	}, [history, historyIndex]);

	const handleUndo = useCallback(() => {
		if (canUndo && historyIndex > 0) {
			const newIndex = historyIndex - 1;
			setHistoryIndex(newIndex);
			setElements([...history[newIndex]]);
			setCanUndo(newIndex > 0);
			setCanRedo(true);
			setHasUnsavedChanges(true);
		}
	}, [canUndo, historyIndex, history]);

	const handleRedo = useCallback(() => {
		if (canRedo && historyIndex < history.length - 1) {
			const newIndex = historyIndex + 1;
			setHistoryIndex(newIndex);
			setElements([...history[newIndex]]);
			setCanUndo(true);
			setCanRedo(newIndex < history.length - 1);
			setHasUnsavedChanges(true);
		}
	}, [canRedo, historyIndex, history]);

	const handleSave = useCallback(() => {
		// Convert editor elements back to funnel schema
		const updatedFunnel: FunnelSchema = {
			...funnel,
			pages: funnel.pages.map((page, index) => ({
				...page,
				// Store elements as sections for the first page (main landing page)
				sections: index === 0 ? elements.map(el => ({
					id: el.id,
					type: el.type as "hero" | "features" | "pricing" | "testimonials" | "cta" | "faq",
					heading: el.name,
					content: el.content,
					// Include all styling properties
					properties: el.properties,
				})) : page.sections,
			})),
		};

		onSave(updatedFunnel);
		setHasUnsavedChanges(false);
		
		// Show success toast notification
		showToastNotification("Funnel saved successfully! All styling and content has been saved.");
	}, [funnel, elements, onSave, showToastNotification]);

	// Auto-save functionality (defined after handleSave)
	const autoSave = useCallback(() => {
		if (hasUnsavedChanges) {
			// Auto-save to Supabase
			handleSave();
			// Note: Auto-save toast is intentionally not shown to avoid spam
			// Users will see the main save toast when they manually save
		}
	}, [hasUnsavedChanges, handleSave]);

	// Set up auto-save interval
	useEffect(() => {
		const interval = setInterval(autoSave, 5000); // Auto-save every 5 seconds
		return () => clearInterval(interval);
	}, [autoSave]);

	const handlePublish = useCallback(() => {
		handleSave();
		// Here you would typically publish to a live URL
		showToastNotification("Funnel published successfully! Your funnel is now live.");
	}, [handleSave, showToastNotification]);

	const handleAddSection = useCallback((sectionType: string) => {
		const newElement: EditorElement = {
			id: `element-${Date.now()}`,
			type: sectionType,
			name: sectionType.charAt(0).toUpperCase() + sectionType.slice(1),
			content: getDefaultContent(sectionType),
			properties: getDefaultProperties(sectionType),
		};

		const newElements = [...elements, newElement];
		setElements(newElements);
		addToHistory(newElements);
		setSelectedElementId(newElement.id);
		setHasUnsavedChanges(true);
		showToastNotification(`${sectionType.charAt(0).toUpperCase() + sectionType.slice(1)} section added successfully!`);
	}, [elements, addToHistory, showToastNotification]);

	const handleAddElement = useCallback((elementType: string) => {
		const newElement: EditorElement = {
			id: `element-${Date.now()}`,
			type: elementType,
			name: elementType.charAt(0).toUpperCase() + elementType.slice(1),
			content: getDefaultContent(elementType),
			properties: getDefaultProperties(elementType),
		};

		const newElements = [...elements, newElement];
		setElements(newElements);
		addToHistory(newElements);
		setSelectedElementId(newElement.id);
		setHasUnsavedChanges(true);
	}, [elements, addToHistory]);

	const handleSelectElement = useCallback((elementId: string) => {
		setSelectedElementId(elementId);
	}, []);

	const handleUpdateElement = useCallback((elementId: string, properties: ElementProperties) => {
		console.log('🚀 Update element:', elementId, properties);
		const newElements = elements.map(el => {
			if (el.id === elementId) {
				// Handle content updates separately
				const { _content, ...otherProperties } = properties as ElementProperties & { _content?: string };
				const updatedElement = {
					...el,
					content: _content !== undefined ? _content : el.content,
					properties: { ...el.properties, ...otherProperties }
				};
				console.log('✅ Updated element:', updatedElement);
				return updatedElement;
			}
			return el;
		});
		setElements(newElements);
		addToHistory(newElements);
		
		// Update tooltip state if it's showing the updated element
		if (tooltipState && tooltipState.element.id === elementId) {
			const updatedElement = newElements.find(el => el.id === elementId);
			if (updatedElement) {
				console.log('🔄 Updating tooltip state with:', updatedElement);
				setTooltipState({ ...tooltipState, element: updatedElement });
			}
		}
		
		setHasUnsavedChanges(true);
	}, [elements, tooltipState, addToHistory]);

	const handleDeleteElement = useCallback((elementId: string) => {
		const elementToDelete = elements.find(el => el.id === elementId);
		const newElements = elements.filter(el => el.id !== elementId);
		setElements(newElements);
		addToHistory(newElements);
		if (selectedElementId === elementId) {
			setSelectedElementId(null);
		}
		setTooltipState(null); // Close tooltip on delete
		setHasUnsavedChanges(true);
		showToastNotification(`${elementToDelete?.name || 'Element'} deleted successfully!`);
	}, [elements, addToHistory, selectedElementId, showToastNotification]);

	const handleEditElement = useCallback((elementId: string, event: React.MouseEvent, context?: { heroPart?: string }) => {
		const element = elements.find(el => el.id === elementId);
		if (element) {
			// Minimal throttling for better responsiveness
			const position = { x: event.clientX, y: event.clientY };
			setTooltipState(prev => {
				// Only update if position changed significantly (more than 5px)
				if (prev && Math.abs(prev.position.x - position.x) < 5 && Math.abs(prev.position.y - position.y) < 5) {
					return prev;
				}
				return { element, position, context };
			});
			setSelectedElementId(elementId);
		}
	}, [elements]);

	const handleDuplicateElement = useCallback((elementId: string) => {
		const element = elements.find(el => el.id === elementId);
		if (!element) return;

		const newElement: EditorElement = {
			...element,
			id: `element-${Date.now()}`,
			name: `${element.name} Copy`,
		};

		const newElements = [...elements, newElement];
		setElements(newElements);
		addToHistory(newElements);
		setSelectedElementId(newElement.id);
		setHasUnsavedChanges(true);
	}, [elements, addToHistory]);

	const handleDrop = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		const sectionType = e.dataTransfer.getData("sectionType");
		const elementType = e.dataTransfer.getData("elementType");
		
		if (sectionType) {
			handleAddSection(sectionType);
		} else if (elementType) {
			handleAddElement(elementType);
		}
	}, [handleAddSection, handleAddElement]);

	const handleDropIntoSection = useCallback((elementType: string, targetSectionId: string, dropPosition?: string) => {
		// Check if this is a section type (should create new page) or element type (should add inside)
		const isSectionType = ["hero", "features", "pricing", "testimonials", "form", "footer", "problem", "solution", "social_proof", "faq"].includes(elementType);
		
		if (isSectionType) {
			// Create a new section page at the bottom (like before)
			handleAddSection(elementType);
			return; // Exit early for sections
		}
		
		// For elements, create a new element and add it to the canvas (not inside sections)
		const newElement: EditorElement = {
			id: `element-${Date.now()}`,
			type: elementType,
			name: elementType.charAt(0).toUpperCase() + elementType.slice(1),
			content: elementType === "button" ? "Click me" : elementType === "text" ? "Edit this text" : "New element",
			properties: {
				// Add default properties based on element type
				...(elementType === "button" && { 
					backgroundColor: "#3b82f6", 
					textColor: "#ffffff",
					fontSize: "16px",
					fontWeight: "600",
					borderRadius: "8px"
				}),
				...(elementType === "text" && { 
					fontSize: "16px",
					color: "#374151",
					fontWeight: "400"
				}),
				...(elementType === "heading" && { 
					fontSize: "24px",
					color: "#111827",
					fontWeight: "600"
				}),
				...(elementType === "image" && { 
					width: "100%",
					height: "200px",
					backgroundColor: "#f3f4f6"
				})
			}
		};

		// Add the element to the main elements array (as a new page/section)
		const updatedElements = [...elements, newElement];
		setElements(updatedElements);
		addToHistory(updatedElements);
		setSelectedElementId(newElement.id);
		setHasUnsavedChanges(true);
	}, [elements, addToHistory, handleAddSection]);

	const handleDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
	}, []);

	const handleReorderElements = useCallback((fromIndex: number, toIndex: number) => {
		const newElements = [...elements];
		const [movedElement] = newElements.splice(fromIndex, 1);
		newElements.splice(toIndex, 0, movedElement);
		setElements(newElements);
		addToHistory(newElements);
		setHasUnsavedChanges(true);
	}, [elements, addToHistory]);

	// Global AI handler
	const handleGlobalAIRequest = useCallback(async (prompt: string) => {
		if (isProcessingAI) return;
		
		setIsProcessingAI(true);
		try {
			const response = await fetch('/api/global-ai', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					prompt,
					elements: elements.map(el => ({
						id: el.id,
						type: el.type,
						content: el.content,
						properties: el.properties,
					})),
				}),
			});

			const result = await response.json();

			if (!result.success) {
				throw new Error(result.error || 'Failed to process AI request');
			}

			// Debug logging
			console.log("AI Response:", result);
			console.log("Current elements:", elements.map(el => ({ id: el.id, type: el.type, content: el.content })));

			// Apply the changes from AI
			if (result.changes && result.changes.length > 0) {
				console.log("Applying changes:", result.changes);
				const newElements = [...elements];
				
				result.changes.forEach((change: { elementId: string; changes: Record<string, unknown> }) => {
					const elementIndex = newElements.findIndex(el => el.id === change.elementId);
					console.log(`Processing change for element ${change.elementId}, found at index: ${elementIndex}`);
					
					if (elementIndex !== -1) {
						const oldElement = newElements[elementIndex];
						console.log(`Old element:`, oldElement);
						
						// Handle content changes separately from property changes
						const { content, ...propertyChanges } = change.changes;
						
						// Update element content if specified
						if (content !== undefined && content !== null && typeof content === 'string') {
							console.log(`Updating content from "${oldElement.content}" to "${content}"`);
							newElements[elementIndex] = {
								...newElements[elementIndex],
								content: content,
							};
						}
						
						// Update element properties
						if (Object.keys(propertyChanges).length > 0) {
							console.log(`Updating properties:`, propertyChanges);
							newElements[elementIndex] = {
								...newElements[elementIndex],
								properties: {
									...newElements[elementIndex].properties,
									...propertyChanges,
								},
							};
						}
						
						console.log(`New element:`, newElements[elementIndex]);
					} else {
						console.warn(`Element with ID ${change.elementId} not found`);
					}
				});

				console.log("Final newElements array:", newElements);
				setElements(newElements);
				addToHistory(newElements);
				setHasUnsavedChanges(true);
				console.log("State updated successfully");
			} else {
				console.log("No changes returned from AI");
				// Try a simple fallback for common requests
				const lowerPrompt = prompt.toLowerCase();
				if (lowerPrompt.includes("hero") || lowerPrompt.includes("header") || lowerPrompt.includes("title")) {
					// Try to find the first hero element
					const heroElement = elements.find(el => el.type === "hero");
					if (heroElement && lowerPrompt.includes("say")) {
						// Extract the new text from the prompt
						const sayMatch = prompt.match(/say\s+['"]([^'"]+)['"]/i) || prompt.match(/say\s+(.+)/i);
						if (sayMatch) {
							const newText = sayMatch[1].trim();
							console.log("Fallback: Updating hero element with text:", newText);
							const newElements = [...elements];
							const heroIndex = newElements.findIndex(el => el.id === heroElement.id);
							if (heroIndex !== -1) {
								newElements[heroIndex] = {
									...newElements[heroIndex],
									content: newText,
								};
								setElements(newElements);
								addToHistory(newElements);
								setHasUnsavedChanges(true);
								return; // Success, exit early
							}
						}
					}
				}
				
				// If fallback didn't work, show helpful message
				alert("AI couldn't find the element you mentioned. Try being more specific, like 'Change the first heading to...' or 'Update the main title to...'");
			}
		} catch (error) {
			console.error("Error processing global AI request:", error);
			const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
			
			// Show user-friendly error messages
			if (errorMessage.includes("503") || errorMessage.includes("overloaded") || errorMessage.includes("UNAVAILABLE")) {
				alert("AI service is temporarily overloaded. Please try again in a few moments.");
			} else if (errorMessage.includes("API key") || errorMessage.includes("authentication") || errorMessage.includes("401")) {
				alert("AI service configuration error. Please check your OpenAI API key.");
			} else if (errorMessage.includes("Invalid JSON") || errorMessage.includes("parse")) {
				alert("AI returned an invalid response. Please try rephrasing your request.");
			} else if (errorMessage.includes("rate limit") || errorMessage.includes("429")) {
				alert("AI service rate limit exceeded. Please wait a moment and try again.");
			} else {
				alert(`Error processing your request: ${errorMessage}. Please try again.`);
			}
		} finally {
			setIsProcessingAI(false);
		}
	}, [elements, isProcessingAI, addToHistory]);

	return (
		<>
			<NewLayout
				onSave={handleSave}
				onPublish={handlePublish}
				onUndo={handleUndo}
				onRedo={handleRedo}
				canUndo={canUndo}
				canRedo={canRedo}
				hasUnsavedChanges={hasUnsavedChanges}
			>
				<LeftSidebar
					onAddSection={handleAddSection}
					onAddElement={handleAddElement}
					onSelectElement={handleSelectElement}
					onReorderElements={handleReorderElements}
					onUpdateElement={handleUpdateElement}
					elements={elements.map(el => ({
						id: el.id,
						name: el.name,
						type: el.type,
						visible: el.properties.visible !== false,
						properties: el.properties,
					}))}
					selectedElementId={selectedElementId || undefined}
				/>
				<CenterCanvas
					elements={elements}
					onSelectElement={handleSelectElement}
					onUpdateElement={handleUpdateElement}
					onDeleteElement={handleDeleteElement}
					onDuplicateElement={handleDuplicateElement}
					selectedElementId={selectedElementId ?? undefined}
					onDrop={handleDrop}
					onDragOver={handleDragOver}
					// Removed onEditElement - using inline editing instead
					onDropIntoSection={handleDropIntoSection}
				/>
				{/* RightSidebar - Temporarily hidden, using inline tooltip instead */}
				{/* <RightSidebar
					selectedElement={selectedElement}
					onUpdateElement={(properties) => selectedElement && handleUpdateElement(selectedElement.id, properties)}
					onCloseEditor={onClose}
				/> */}
			</NewLayout>
			<GlobalAIChat
				onAIRequest={handleGlobalAIRequest}
				isProcessing={isProcessingAI}
			/>
			{/* Inline Tooltip */}
			{tooltipState && (() => {
				const currentElement = elements.find(el => el.id === tooltipState.element.id);
				return currentElement ? (
					<InlineElementTooltip
						key={`${currentElement.id}-${JSON.stringify(currentElement.properties)}`}
						element={currentElement}
						onUpdate={(properties) => handleUpdateElement(tooltipState.element.id, properties)}
						onDelete={() => handleDeleteElement(tooltipState.element.id)}
						position={{ x: window.innerWidth - 400, y: 80 }} // Fixed top right position
						onClose={() => setTooltipState(null)}
						context={tooltipState.context}
					/>
				) : null;
			})()}
			
			{/* Success Toast Notification */}
			{showToast && (
				<div
					className="whop-toast"
					style={{
						position: "fixed",
						top: "24px",
						right: "24px",
						zIndex: 50,
						minWidth: "320px",
						maxWidth: "480px",
					}}
				>
					<Callout.Root
						color="green"
						size="2"
						style={{
							padding: "16px 20px",
							borderRadius: "10px",
							boxShadow: "0 8px 24px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08)",
							border: "1px solid var(--green-6)",
							background: "var(--green-2)",
						}}
					>
						<Callout.Icon>
							<svg
								width="24"
								height="24"
								viewBox="0 0 20 20"
								fill="currentColor"
								role="img"
								aria-label="Success checkmark icon"
							>
								<path
									fillRule="evenodd"
									d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
									clipRule="evenodd"
								/>
							</svg>
						</Callout.Icon>
						<Callout.Text style={{ fontSize: "14px", fontWeight: "500" }}>
							{toastMessage}
						</Callout.Text>
					</Callout.Root>
				</div>
			)}
		</>
	);
}

function getElementName(type: string): string {
	switch (type) {
		case "heading":
			return "Heading";
		case "text":
			return "Text";
		case "button":
			return "Button";
		case "image":
			return "Image";
		case "input":
			return "Input";
		case "hero":
			return "Hero Section";
		case "features":
			return "Features Section";
		case "pricing":
			return "Pricing Section";
		case "testimonials":
			return "Testimonials Section";
		case "form":
			return "Lead Capture Form";
		case "footer":
			return "Footer Section";
		default:
			return type.charAt(0).toUpperCase() + type.slice(1);
	}
}

function getDefaultContent(type: string): string {
	switch (type) {
		case "heading":
			return "Your Amazing Headline";
		case "text":
			return "Add your compelling text here. This is where you explain your value proposition and engage your visitors.";
		case "button":
			return "Get Started";
		case "hero":
			return "Hero Section";
		case "features":
			return "Features Section";
		case "pricing":
			return "Pricing Section";
		case "testimonials":
			return "Testimonials Section";
		case "form":
			return "Lead Capture Form";
		case "footer":
			return "Footer Section";
		case "video":
			return "Video Player";
		default:
			return "New Element";
	}
}

function getDefaultProperties(type: string): ElementProperties {
	const baseProperties = {
		padding: { top: 16, right: 16, bottom: 16, left: 16 },
		textColor: "#000000",
		textAlign: "left",
		visible: true,
		animation: "none",
	};

	switch (type) {
		case "heading":
			return {
				...baseProperties,
				fontSize: 32,
				textAlign: "center",
			};
		case "text":
			return {
				...baseProperties,
				fontSize: 16,
			};
		case "button":
			return {
				...baseProperties,
				backgroundColor: "#3b82f6",
				textColor: "#ffffff",
				textAlign: "center",
				fontSize: 16,
			};
		case "image":
			return {
				...baseProperties,
				imageUrl: "",
				altText: "",
			};
		case "input":
			return {
				...baseProperties,
				inputType: "text",
				placeholder: "Enter your information",
			};
		case "features":
			return {
				...baseProperties,
				subheading: "Everything you need to succeed",
				features: [
					{
						title: "Feature 1",
						description: "Powerful tools to help you succeed",
						icon: "⚡",
						iconUrl: "",
						ctaText: "",
						ctaUrl: "",
					},
					{
						title: "Feature 2",
						description: "Advanced analytics and insights",
						icon: "📊",
						iconUrl: "",
						ctaText: "",
						ctaUrl: "",
					},
					{
						title: "Feature 3",
						description: "24/7 customer support",
						icon: "💬",
						iconUrl: "",
						ctaText: "",
						ctaUrl: "",
					},
				],
			};
		case "pricing":
			return {
				...baseProperties,
				subtitle: "Choose the plan that's right for you",
				buttonText: "Get Started",
				priceColor: "#667eea",
				popularPlanColor: "#667eea",
				planBackground: "#ffffff",
				planBorderColor: "#e5e7eb",
				planBorderRadius: 16,
				featureColor: "#374151",
				buttonBackground: "#f3f4f6",
				buttonTextColor: "#374151",
				columns: 3,
				plans: [
					{
						name: "Basic Plan",
						price: "$29",
						period: "/month",
						features: ["Basic Features", "Email Support", "5GB Storage"],
						popular: false,
					},
					{
						name: "Pro Plan",
						price: "$79",
						period: "/month",
						features: ["All Features", "Priority Support", "50GB Storage"],
						popular: true,
					},
					{
						name: "Enterprise",
						price: "$199",
						period: "/month",
						features: ["Everything", "24/7 Support", "Unlimited Storage"],
						popular: false,
					},
				],
			};
		case "testimonials":
			return {
				...baseProperties,
				subtitle: "See what our users have to say",
				testimonialBackground: "#ffffff",
				testimonialBorderColor: "#e5e7eb",
				testimonialBorderRadius: 16,
				quoteColor: "#374151",
				authorColor: "#111827",
				roleColor: "#6b7280",
				starColor: "#fbbf24",
				avatarBackground: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
				columns: 3,
				testimonials: [
					{
						quote: "This product has completely transformed how we work. Absolutely amazing!",
						author: "Sarah Johnson",
						role: "CEO, TechCorp",
						avatar: "SJ",
						rating: 5,
					},
					{
						quote: "The best investment we've made this year. Highly recommend to everyone.",
						author: "Michael Chen",
						role: "Founder, StartupXYZ",
						avatar: "MC",
						rating: 5,
					},
					{
						quote: "Outstanding support and incredible features. Worth every penny!",
						author: "Emma Williams",
						role: "Product Manager",
						avatar: "EW",
						rating: 5,
					},
				],
			};

		case "video":
			return {
				...baseProperties,
				videoUrl: "",
				autoplay: false,
				controls: true,
				loop: false,
				muted: false,
				width: "100%",
				height: "auto",
			};
		case "hero":
			return {
				...baseProperties,
				// Hero-specific properties
				title: "Build Funnels That Convert",
				subtitle: "Create high-converting sales funnels with AI-powered tools and proven templates.",
				ctaText: "Start Free Trial",
				backgroundImage: "",
				backgroundColor: "#ffffff",
				useGradient: true,
				gradientStart: "#f8fafc",
				gradientEnd: "#f1f5f9",
				overlay: false,
				overlayOpacity: 0.4,
				height: "100vh",
				videoUrl: "",
				socialProof: "Trusted by 50,000+ happy customers",
				trustText: "",
				headingVisible: true,
				subheadingVisible: true,
				buttonVisible: true,
				headingTextColor: "#1f2937",
				// Promotional banner properties
				showPromoBanner: true,
				promoBannerText: "🎉 Limited Time: Get 20% off using code \"FUNNEL\"",
			};
		default:
			return baseProperties;
	}
}
