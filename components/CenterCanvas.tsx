"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Text } from "frosted-ui";
import { useState, useRef } from "react";
import { DESIGN_SYSTEM } from "@/lib/design-system";
import { AiFillStar } from "react-icons/ai";
import { HiPlay } from "react-icons/hi2";
import { FiCheck } from "react-icons/fi";
import { SmallElementTooltip } from "./SmallElementTooltip";

// Gradient utilities for AI
const GRADIENT_PRESETS = {
	// Light mode gradients
	lightBlue: "linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%)",
	lightPurple: "linear-gradient(135deg, #faf5ff 0%, #f3e8ff 50%, #e9d5ff 100%)",
	lightGreen: "linear-gradient(135deg, #f0fdf4 0%, #dcfce7 50%, #bbf7d0 100%)",
	lightOrange: "linear-gradient(135deg, #fff7ed 0%, #fed7aa 50%, #fdba74 100%)",
	lightPink: "linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #fbcfe8 100%)",
	
	// Dark mode gradients
	darkBlue: "linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)",
	darkPurple: "linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #4c1d95 100%)",
	darkGreen: "linear-gradient(135deg, #064e3b 0%, #065f46 50%, #047857 100%)",
	darkOrange: "linear-gradient(135deg, #7c2d12 0%, #ea580c 50%, #f97316 100%)",
	darkPink: "linear-gradient(135deg, #831843 0%, #be185d 50%, #ec4899 100%)",
	
	// Subtle depth gradients
	subtleBlue: "linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)",
	subtlePurple: "linear-gradient(135deg, #fafafa 0%, #f4f4f5 50%, #e4e4e7 100%)",
	subtleGreen: "linear-gradient(135deg, #f9fafb 0%, #f3f4f6 50%, #e5e7eb 100%)",
	
	// Professional gradients
	corporateBlue: "linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%)",
	corporatePurple: "linear-gradient(135deg, #7c3aed 0%, #8b5cf6 50%, #a78bfa 100%)",
	corporateGreen: "linear-gradient(135deg, #059669 0%, #10b981 50%, #34d399 100%)",
	
	// Depth gradients
	depthBlue: "linear-gradient(135deg, #1e3a8a 0%, #1e40af 25%, #3b82f6 75%, #60a5fa 100%)",
	depthPurple: "linear-gradient(135deg, #581c87 0%, #7c3aed 25%, #8b5cf6 75%, #a78bfa 100%)",
	depthGreen: "linear-gradient(135deg, #064e3b 0%, #059669 25%, #10b981 75%, #34d399 100%)",
};

interface ElementProperties {
	padding?: { top: number; right: number; bottom: number; left: number };
	textColor?: string;
	textAlign?: string;
	visible?: boolean;
	animation?: string;
	fontSize?: number | string;
	backgroundColor?: string;
	imageUrl?: string;
	altText?: string;
	linkUrl?: string;
	actionType?: string;
	inputType?: string;
	placeholder?: string;
	content?: string;
	overlay?: boolean;
	overlayOpacity?: number;
	[key: string]: unknown;
}

interface CenterCanvasProps {
	elements: Array<{
		id: string;
		type: string;
		name: string;
		content: string;
		properties: ElementProperties;
	}>;
	onSelectElement?: (elementId: string) => void;
	onUpdateElement?: (elementId: string, properties: ElementProperties) => void;
	onDeleteElement?: (elementId: string) => void;
	onDuplicateElement?: (elementId: string) => void;
	selectedElementId?: string;
	onDrop?: (e: React.DragEvent) => void;
	onDragOver?: (e: React.DragEvent) => void;
	// Removed onEditElement - using inline editing instead
	onDropIntoSection?: (elementType: string, targetSectionId: string, dropPosition?: string) => void;
}

// HoverToolbar component removed per user request

// Helper function to handle font size values (both numeric and string with px)
const getFontSizeValue = (fontSize: unknown): string | undefined => {
	if (typeof fontSize === 'number') {
		return `${fontSize}px`;
	}
	if (typeof fontSize === 'string') {
		// If it already has px, return as is; if not, add px
		return fontSize.endsWith('px') ? fontSize : `${fontSize}px`;
	}
	return undefined;
};

interface ElementWrapperProps {
	element: {
		id: string;
		type: string;
		name: string;
		content: string;
		properties: ElementProperties;
	};
	isSelected: boolean;
	onSelect: () => void;
	onUpdate: (properties: ElementProperties) => void;
	onDelete: () => void;
	onDuplicate: () => void;
	// Removed onEditElement - using inline editing instead
	onDropIntoSection?: (elementType: string, targetSectionId: string, dropPosition?: string) => void;
	onElementClick?: (element: any, position: { x: number; y: number }) => void;
}

function ElementWrapper({ element, isSelected, onSelect, onUpdate, onDropIntoSection, onElementClick }: ElementWrapperProps) {
	// Hover state removed since hover toolbar is disabled
	const [isEditing, setIsEditing] = useState(false);
	const [editValue, setEditValue] = useState(element.content);
	const elementRef = useRef<HTMLDivElement>(null);

	const handleDoubleClick = () => {
		setIsEditing(true);
	};

	const handleBlur = () => {
		setIsEditing(false);
		if (editValue !== element.content) {
			onUpdate({ content: editValue });
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			setIsEditing(false);
			if (editValue !== element.content) {
				onUpdate({ content: editValue });
			}
		}
		if (e.key === "Escape") {
			setIsEditing(false);
			setEditValue(element.content);
		}
	};

	const handleClick = (e: React.MouseEvent) => {
		e.stopPropagation();
		onSelect();

		// Notify parent about element click for tooltip
		const rect = elementRef.current?.getBoundingClientRect();
		if (rect && onElementClick) {
			const position = {
				x: rect.left + rect.width / 2,
				y: rect.top
			};
			console.log("Element clicked, position:", position);
			onElementClick(element, position);
		}
	};



	// Remove drag and drop handlers for individual elements - let sections work normally


	const renderElement = (): React.ReactNode => {
		// Handle section types (hero, features, etc.) with professional styling
		if (element.type === "hero") {
			const props = element.properties;

			return (
				<div
						data-element-content
						style={{
							position: "relative",
							backgroundColor: props.useGradient ? undefined : (props.backgroundColor || "#ffffff"),
							backgroundImage: props.backgroundImage
								? `url(${props.backgroundImage})`
								: props.useGradient
									? `linear-gradient(135deg, ${props.gradientStart || "#f8fafc"} 0%, ${props.gradientEnd || "#f1f5f9"} 100%)`
									: undefined,
							backgroundSize: "cover",
							backgroundPosition: "center",
							backgroundRepeat: "no-repeat",
							overflow: "hidden",
							minHeight: (props.height as string) || "100vh",
							width: "100%",
							display: props.visible === false ? "none" : "flex",
							alignItems: "center",
							justifyContent: "center",
							padding: "80px 40px",
						}}
					>
					{(props.overlay as boolean) && (
						<div
							style={{
								position: "absolute",
								top: 0,
								left: 0,
								right: 0,
								bottom: 0,
								background: `rgba(0, 0, 0, ${(props.overlayOpacity as number) || 0.4})`,
								zIndex: 0,
							}}
						/>
					)}

					{/* Main Content Container */}
					<div style={{
						position: "relative",
						zIndex: 1,
						textAlign: "center",
						maxWidth: "800px",
						margin: "0 auto"
					}}>
						{/* Trust Bar - Bigger and closer */}
						<div
							style={{
								display: "inline-flex",
								alignItems: "center",
								fontSize: "16px",
								fontWeight: 600,
								marginBottom: 20,
								color: "#374151",
								cursor: "pointer",
								padding: 8,
								borderRadius: 6,
								transition: "background-color 0.2s ease"
							}}
							onClick={e => {
								e.stopPropagation();
								// Trigger tooltip for this element
								const rect = e.currentTarget.getBoundingClientRect();
								const position = { x: rect.left + rect.width / 2, y: rect.top };
								if (onElementClick) {
									onElementClick(element, position);
								}
							}}
							onMouseEnter={e => {
								(e.currentTarget as HTMLDivElement).style.backgroundColor = "rgba(59, 130, 246, 0.05)";
							}}
							onMouseLeave={e => {
								(e.currentTarget as HTMLDivElement).style.backgroundColor = "transparent";
							}}
							contentEditable
							suppressContentEditableWarning
							onBlur={e => {
								const newText = e.currentTarget.textContent || "";
								if (newText !== String(props.socialProof ?? "")) {
									onUpdate({ socialProof: newText });
								}
							}}
							onKeyDown={e => {
								if (e.key === "Enter") {
									e.preventDefault();
									(e.currentTarget as HTMLDivElement).blur();
								} else if (e.key === "Escape") {
									(e.currentTarget as HTMLDivElement).textContent =
										String(props.socialProof ?? "Trusted by 50,000+ happy customers");
									(e.currentTarget as HTMLDivElement).blur();
								}
							}}
						>
							<div style={{ display: "flex", alignItems: "center", marginRight: 12, gap: 3 }}>
								{Array.from({ length: 5 }).map((_, i) => (
									<AiFillStar key={i} style={{ color: "#fbbf24", fontSize: 18 }} />
								))}
							</div>
							{String(props.socialProof ?? "Trusted by 50,000+ happy customers")}
						</div>

						{/* Headline (H1) - STRICT: Max 7 words only */}
						<h1
							data-element-content
							onClick={e => {
								e.stopPropagation();
								// Trigger tooltip for this element
								const rect = e.currentTarget.getBoundingClientRect();
								const position = {
									x: rect.left + rect.width / 2,
									y: rect.top
								};
								if (onElementClick) {
									// Create unique element identifier for headline
									const uniqueElement = {
										...element,
										id: `${element.id}-headline`,
										elementType: 'headline',
										textPath: 'headline'
									};
									onElementClick(uniqueElement, position);
								}
							}}
							style={{
								fontSize: (element.properties.headlineFontSize as string) || (element.properties.fontSize as string) || DESIGN_SYSTEM.typography.hero.fontSize,
								fontWeight: (element.properties.headlineFontWeight as string) || (element.properties.fontWeight as string) || DESIGN_SYSTEM.typography.hero.fontWeight,
								fontStyle: (element.properties.headlineFontStyle as string) || (element.properties.fontStyle as string) || "normal",
								textDecoration: (element.properties.headlineTextDecoration as string) || (element.properties.textDecoration as string) || "none",
								fontFamily: (element.properties.headlineFontFamily as string) || (element.properties.fontFamily as string) || (element.properties.headingFontFamily as string) || "Inter",
								color: (element.properties.headlineColor as string) || (element.properties.color as string) || (element.properties.textColor as string) || (element.properties.headingTextColor as string) || DESIGN_SYSTEM.colors.text.primary,
								textAlign: (element.properties.headlineTextAlign as "left" | "center" | "right") || (element.properties.textAlign as "left" | "center" | "right") || "center",
								lineHeight: DESIGN_SYSTEM.typography.hero.lineHeight,
								letterSpacing: DESIGN_SYSTEM.typography.hero.letterSpacing,
								maxWidth: 800,
								margin: "0 auto 24px auto",
								cursor: "pointer",
								display: element.properties.headingVisible === false ? "none" : "block",
								padding: 8,
								borderRadius: 6,
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={e => {
								(e.currentTarget as HTMLHeadingElement).style.backgroundColor = "rgba(59, 130, 246, 0.05)";
							}}
							onMouseLeave={e => {
								(e.currentTarget as HTMLHeadingElement).style.backgroundColor = "transparent";
							}}
							contentEditable
							suppressContentEditableWarning
							onBlur={e => {
								const newText = e.currentTarget.textContent || "";
								if (newText !== String(element.content ?? "")) {
									onUpdate({ content: newText });
								}
							}}
							onKeyDown={e => {
								if (e.key === "Enter") {
									e.preventDefault();
									(e.currentTarget as HTMLHeadingElement).blur();
								} else if (e.key === "Escape") {
									(e.currentTarget as HTMLHeadingElement).textContent = String(
										element.content ?? props.title ?? "Build Funnels That Convert"
									);
									(e.currentTarget as HTMLHeadingElement).blur();
								}
							}}
						>
							{(() => {
								const headline = String(
									element.content ??
										props.title ??
										props.content ??
										"Build Funnels That Convert"
								);
								const words = headline.trim().split(/\s+/);
								return words.length > DESIGN_SYSTEM.components.hero.maxHeadlineWords
									? words.slice(0, DESIGN_SYSTEM.components.hero.maxHeadlineWords).join(" ")
									: headline;
							})()}
						</h1>

						{/* Subheadline - Clear value proposition */}
						<p
							data-element-content
							onClick={e => {
								e.stopPropagation();
								// Trigger tooltip for this element
								const rect = e.currentTarget.getBoundingClientRect();
								const position = {
									x: rect.left + rect.width / 2,
									y: rect.top
								};
								if (onElementClick) {
									// Create unique element identifier for subheadline
									const uniqueElement = {
										...element,
										id: `${element.id}-subheadline`,
										elementType: 'subheadline',
										textPath: 'subheadline'
									};
									onElementClick(uniqueElement, position);
								}
							}}
							style={{
								fontSize: (element.properties.subheadlineFontSize as string) || (element.properties.fontSize as string) || DESIGN_SYSTEM.typography.bodyLarge.fontSize,
								fontWeight: (element.properties.subheadlineFontWeight as string) || (element.properties.fontWeight as string) || "400",
								fontStyle: (element.properties.subheadlineFontStyle as string) || (element.properties.fontStyle as string) || "normal",
								textDecoration: (element.properties.subheadlineTextDecoration as string) || (element.properties.textDecoration as string) || "none",
								fontFamily: (element.properties.subheadlineFontFamily as string) || (element.properties.fontFamily as string) || (element.properties.subheadingFontFamily as string) || "Inter",
								color: (element.properties.subheadlineColor as string) || (element.properties.color as string) || DESIGN_SYSTEM.colors.text.secondary,
								textAlign: (element.properties.subheadlineTextAlign as "left" | "center" | "right") || (element.properties.textAlign as "left" | "center" | "right") || "center",
								lineHeight: DESIGN_SYSTEM.typography.bodyLarge.lineHeight,
								maxWidth: 600,
								margin: `0 auto ${DESIGN_SYSTEM.spacing.xl} auto`,
								cursor: "pointer",
								display: element.properties.subheadingVisible === false ? "none" : "block",
								padding: 8,
								borderRadius: 6,
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={e => {
								(e.currentTarget as HTMLParagraphElement).style.backgroundColor = "rgba(59, 130, 246, 0.05)";
							}}
							onMouseLeave={e => {
								(e.currentTarget as HTMLParagraphElement).style.backgroundColor = "transparent";
							}}
							contentEditable
							suppressContentEditableWarning
							onBlur={e => {
								const newText = e.currentTarget.textContent || "";
								if (
									newText !== String(props.subtitle ?? "") &&
									newText !== String(element.properties.subtitle ?? "")
								) {
									onUpdate({ subtitle: newText });
								}
							}}
							onKeyDown={e => {
								if (e.key === "Enter") {
									e.preventDefault();
									(e.currentTarget as HTMLParagraphElement).blur();
								} else if (e.key === "Escape") {
									(e.currentTarget as HTMLParagraphElement).textContent = String(
										props.subtitle ??
											element.properties.subtitle ??
											"Create high-converting sales funnels with AI-powered tools and proven templates."
									);
									(e.currentTarget as HTMLParagraphElement).blur();
								}
							}}
						>
							{String(
								props.subtitle ??
									element.properties.subtitle ??
									"Create high-converting sales funnels with AI-powered tools and proven templates."
							)}
						</p>

						{/* CTA Buttons */}
						<div
							style={{
								display: "flex",
								gap: DESIGN_SYSTEM.spacing.md,
								justifyContent: "center",
								alignItems: "center",
								marginBottom: DESIGN_SYSTEM.spacing.xl
							}}
						>
							<button
								type="button"
								data-element-content
								onClick={e => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = {
										x: rect.left + rect.width / 2,
										y: rect.top
									};
									if (onElementClick) {
										onElementClick(element, position);
									}
								}}
								style={{
									padding: "16px 48px",
									background: "linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)",
									color: "#ffffff",
									fontSize: 16,
									fontFamily:
										typeof element.properties.buttonFontFamily === "string" && element.properties.buttonFontFamily
											? element.properties.buttonFontFamily
											: "Inter",
									fontWeight: 600,
									borderRadius: 12,
									border: "none",
									cursor: "pointer",
									boxShadow: "0 4px 14px rgba(59, 130, 246, 0.25), inset 0 2px 4px rgba(255, 255, 255, 0.3)",
									transition: "all 0.2s ease",
									display: element.properties.buttonVisible === false ? "none" : "inline-block",
									minWidth: 200,
									position: "relative"
								}}
								onMouseEnter={e => {
									(e.currentTarget as HTMLButtonElement).style.transform = "translateY(-1px)";
									(e.currentTarget as HTMLButtonElement).style.boxShadow =
										"0 6px 20px rgba(59, 130, 246, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.2)";
								}}
								onMouseLeave={e => {
									(e.currentTarget as HTMLButtonElement).style.transform = "translateY(0)";
									(e.currentTarget as HTMLButtonElement).style.boxShadow =
										"0 4px 14px rgba(59, 130, 246, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2)";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={e => {
									const newText = e.currentTarget.textContent || "";
									if (
										newText !== String(props.ctaText ?? "") &&
										newText !== String(element.properties.ctaText ?? "")
									) {
										onUpdate({ ctaText: newText } as ElementProperties);
									}
								}}
								onKeyDown={e => {
									if (e.key === "Enter") {
										e.preventDefault();
										(e.currentTarget as HTMLButtonElement).blur();
									} else if (e.key === "Escape") {
										(e.currentTarget as HTMLButtonElement).textContent = String(
											props.ctaText ?? element.properties.ctaText ?? "Start Free Trial"
										);
										(e.currentTarget as HTMLButtonElement).blur();
									}
								}}
							>
								{String(props.ctaText ?? element.properties.ctaText ?? "Start Free Trial")}
							</button>

							{/* Optional Secondary CTA */}
							{typeof props.secondaryCtaText === "string" && props.secondaryCtaText.length > 0 && (
								<button
									type="button"
									onClick={e => {
										e.stopPropagation();
										// Trigger tooltip for this element
										const rect = e.currentTarget.getBoundingClientRect();
										const position = { x: rect.left + rect.width / 2, y: rect.top };
										if (onElementClick) {
											onElementClick(element, position);
										}
									}}
									style={{
										...(DESIGN_SYSTEM.buttons.secondary as React.CSSProperties),
										cursor: "pointer",
										position: "relative"
									}}
									onMouseEnter={e => {
										(e.currentTarget as HTMLButtonElement).style.background =
											DESIGN_SYSTEM.buttons.secondary.hover.background;
										(e.currentTarget as HTMLButtonElement).style.color =
											DESIGN_SYSTEM.buttons.secondary.hover.color;
									}}
									onMouseLeave={e => {
										(e.currentTarget as HTMLButtonElement).style.background =
											DESIGN_SYSTEM.buttons.secondary.background;
										(e.currentTarget as HTMLButtonElement).style.color =
											DESIGN_SYSTEM.buttons.secondary.color;
									}}
									contentEditable
									suppressContentEditableWarning
									onBlur={e => {
										const newText = e.currentTarget.textContent || "";
										if (newText !== String(props.secondaryCtaText ?? "")) {
											onUpdate({ secondaryCtaText: newText } as ElementProperties);
										}
									}}
									onKeyDown={e => {
										if (e.key === "Enter") {
											e.preventDefault();
											(e.currentTarget as HTMLButtonElement).blur();
										} else if (e.key === "Escape") {
											(e.currentTarget as HTMLButtonElement).textContent = String(
												props.secondaryCtaText ?? "Learn More"
											);
											(e.currentTarget as HTMLButtonElement).blur();
										}
									}}
								>
									{String(props.secondaryCtaText)}
								</button>
							)}
						</div>

						{/* Social Proof Text */}
						{typeof props.trustText === "string" && props.trustText.length > 0 && (
							<p
								data-element-content
								onClick={e => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = { x: rect.left + rect.width / 2, y: rect.top };
									if (onElementClick) {
										onElementClick(element, position);
									}
								}}
								style={{
									fontSize: (element.properties.fontSize as string) || "14px",
									fontWeight: (element.properties.fontWeight as string) || "500",
									fontStyle: (element.properties.fontStyle as string) || "normal",
									textDecoration: (element.properties.textDecoration as string) || "none",
									fontFamily: (element.properties.fontFamily as string) || "Inter",
									color: (element.properties.color as string) || (props.textColor ? `${props.textColor}99` : "#9ca3af"),
									textAlign: (element.properties.textAlign as "left" | "center" | "right") || "center",
									marginBottom: 40,
									cursor: "pointer",
									padding: 8,
									borderRadius: 6,
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={e => {
									(e.currentTarget as HTMLParagraphElement).style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={e => {
									(e.currentTarget as HTMLParagraphElement).style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={e => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== String(props.trustText ?? "")) {
										onUpdate({ trustText: newText } as ElementProperties);
									}
								}}
								onKeyDown={e => {
									if (e.key === "Enter") {
										e.preventDefault();
										(e.currentTarget as HTMLParagraphElement).blur();
									} else if (e.key === "Escape") {
										(e.currentTarget as HTMLParagraphElement).textContent = String(
											props.trustText ?? "Join thousands of satisfied customers"
										);
										(e.currentTarget as HTMLParagraphElement).blur();
									}
								}}
							>
								{String(props.trustText)}
							</p>
						)}

						{/* Video Section - Always show */}
						<div
							onClick={e => {
								e.stopPropagation();
								// Trigger tooltip for this element
								const rect = e.currentTarget.getBoundingClientRect();
								const position = { x: rect.left + rect.width / 2, y: rect.top };
								if (onElementClick) {
									onElementClick(element, position);
								}
							}}
							style={{
								maxWidth: 800,
								margin: "40px auto 0 auto",
								cursor: "pointer",
								padding: 8,
								borderRadius: 8,
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={e => {
								(e.currentTarget as HTMLDivElement).style.backgroundColor = "rgba(59, 130, 246, 0.05)";
							}}
							onMouseLeave={e => {
								(e.currentTarget as HTMLDivElement).style.backgroundColor = "transparent";
							}}
						>
							{typeof props.videoUrl === "string" && props.videoUrl.length > 0 ? (
								<div
									style={{
										borderRadius: 12,
										overflow: "hidden",
										boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)",
										background: "white",
										padding: 8
									}}
								>
									<div
										style={{
											position: "relative",
											paddingBottom: "56.25%",
											height: 0,
											borderRadius: 8,
											overflow: "hidden"
										}}
									>
										<iframe
											src={String(props.videoUrl)}
											style={{
												position: "absolute",
												top: 0,
												left: 0,
												width: "100%",
												height: "100%",
												border: "none"
											}}
											allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
											allowFullScreen
										/>
									</div>
								</div>
							) : (
								<div
									style={{
										width: "100%",
										height: 300,
										background: "#f3f4f6",
										border: "1px solid #e5e7eb",
										borderRadius: 12,
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										flexDirection: "column",
										gap: 12,
										cursor: "pointer"
									}}
									onClick={e => {
										e.stopPropagation();
									// Inline editing - no popup needed
									}}
								>
									<HiPlay style={{ fontSize: 48, color: "#374151" }} />
									<div style={{ textAlign: "center" }}>
										<div style={{ fontSize: 16, fontWeight: 500, color: "#374151", marginBottom: 4 }}>
											Add Video
										</div>
										<div style={{ fontSize: 14, color: "#6b7280" }}>
											Click to upload or add a video URL
										</div>
									</div>
								</div>
							)}
						</div>
					</div>
					
					{/* Render children elements */}
					{element.properties.children && Array.isArray(element.properties.children) && element.properties.children.length > 0 && (
						<div style={{ position: "relative", zIndex: 10 }}>
							{(element.properties.children as any[]).map((child: any, index: number) => (
								<ElementWrapper
									key={child.id || index}
									element={child}
									isSelected={false}
									onSelect={() => {}}
									onUpdate={(properties) => onUpdate({ ...element.properties, children: (element.properties.children as any[]).map((c: any) => c.id === child.id ? { ...c, properties: { ...c.properties, ...properties } } : c) } as ElementProperties)}
									onDelete={() => onUpdate({ ...element.properties, children: (element.properties.children as any[]).filter((c: any) => c.id !== child.id) } as ElementProperties)}
									onDuplicate={() => {}}
									// Removed onEditElement - using inline editing instead
									onDropIntoSection={onDropIntoSection}
								/>
							))}
						</div>
					)}
				</div>
			);
		}

		if (element.type === "social_proof") {
			const props = element.properties;

			return (
				<div
					style={{
						padding: "60px 40px",
						background: props.backgroundColor || "#f8fafc",
						width: "100%",
					}}
				>
					<div style={{ maxWidth: "1200px", margin: "0 auto" }}>
						{/* Section Header */}
						<div style={{ textAlign: "center", marginBottom: "48px" }}>
							<h2
								data-element-content
								onClick={(e) => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = {
										x: rect.left + rect.width / 2,
										y: rect.top
									};
									if (onElementClick) {
										// Create unique element identifier for social proof title
										const uniqueElement = {
											...element,
											id: `${element.id}-social-proof-title`,
											elementType: 'social-proof-title',
											textPath: 'social-proof-title'
										};
										onElementClick(uniqueElement, position);
									}
								}}
								style={{
									fontSize: (element.properties.socialProofTitleFontSize as string) || (element.properties.fontSize as string) || "32px",
									fontWeight: (element.properties.socialProofTitleFontWeight as string) || (element.properties.fontWeight as string) || "700",
									fontStyle: (element.properties.socialProofTitleFontStyle as string) || (element.properties.fontStyle as string) || "normal",
									textDecoration: (element.properties.socialProofTitleTextDecoration as string) || (element.properties.textDecoration as string) || "none",
									fontFamily: (element.properties.socialProofTitleFontFamily as string) || (element.properties.fontFamily as string) || "Inter",
									color: (element.properties.socialProofTitleColor as string) || (element.properties.color as string) || props.textColor || "#1f2937",
									textAlign: (element.properties.socialProofTitleTextAlign as "left" | "center" | "right") || (element.properties.textAlign as "left" | "center" | "right") || "center",
									marginBottom: "16px",
									letterSpacing: "-0.02em",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== element.content) {
										onUpdate({ content: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = element.content || "Trusted by Industry Leaders";
										e.currentTarget.blur();
									}
								}}
							>
								{typeof element.content === 'string' ? element.content : (element.content as any)?.title || "Trusted by Industry Leaders"}
							</h2>
							<p 
								onClick={(e) => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = {
										x: rect.left + rect.width / 2,
										y: rect.top
									};
									if (onElementClick) {
										onElementClick(element, position);
									}
								}}
								style={{ 
									fontSize: "18px", 
									fontFamily: (element.properties.socialProofSubtitleFontFamily as string) || "Inter",
									color: props.textColor ? `${props.textColor}cc` : "#6b7280", 
									lineHeight: "1.6",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== (props.subtitle as string)) {
										onUpdate({ subtitle: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = (props.subtitle as string) || "Join thousands of satisfied customers";
										e.currentTarget.blur();
									}
								}}
							>
								{(props.subtitle as string) || "Join thousands of satisfied customers"}
							</p>
						</div>

						{/* Trust Indicators */}
						<div style={{ display: "flex", flexDirection: "column", gap: "32px", alignItems: "center" }}>
							{/* Stats Row */}
							<div 
								onClick={(e) => {
									e.stopPropagation();
									// Inline editing - no popup needed
								}}
								style={{ 
									display: "flex", 
									gap: "48px", 
									alignItems: "center", 
									flexWrap: "wrap", 
									justifyContent: "center",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
							>
								{(() => {
									const stats = props.stats as Array<{ number: string; label: string }> || [
										{ number: "50,000+", label: "Happy Customers" },
										{ number: "99.9%", label: "Uptime" },
										{ number: "24/7", label: "Support" },
										{ number: "4.9/5", label: "Rating" }
									];
									return stats.map((stat, idx) => (
									<div 
										key={idx} 
										onClick={(e) => {
											e.stopPropagation();
											// Inline editing - no popup needed
										}}
										style={{ 
											textAlign: "center",
											cursor: "pointer",
											padding: "8px",
											borderRadius: "8px",
											transition: "background-color 0.2s ease"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "transparent";
										}}
									>
										<div 
											style={{ fontSize: "32px", fontFamily: (element.properties.socialProofStatFontFamily as string) || "Inter", fontWeight: "700", color: props.textColor || "#1f2937", marginBottom: "4px", cursor: "pointer", padding: "4px", borderRadius: "4px", transition: "background-color 0.2s ease" }}
											onMouseEnter={(e) => {
												e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
											}}
											onMouseLeave={(e) => {
												e.currentTarget.style.backgroundColor = "transparent";
											}}
											contentEditable
											suppressContentEditableWarning
											onBlur={(e) => {
												const newText = e.currentTarget.textContent || "";
												if (newText !== stat.number) {
													// Update the stat number in the stats array
													const updatedStats = [...(props.stats as Array<any>)];
													updatedStats[idx] = { ...updatedStats[idx], number: newText };
													onUpdate({ stats: updatedStats });
												}
											}}
											onKeyDown={(e) => {
												if (e.key === "Enter") {
													e.preventDefault();
													e.currentTarget.blur();
												}
												if (e.key === "Escape") {
													e.currentTarget.textContent = stat.number;
													e.currentTarget.blur();
												}
											}}
										>
											{stat.number}
										</div>
										<div 
											style={{ fontSize: "14px", fontFamily: (element.properties.socialProofStatFontFamily as string) || "Inter", color: props.textColor ? `${props.textColor}cc` : "#6b7280", cursor: "pointer", padding: "4px", borderRadius: "4px", transition: "background-color 0.2s ease" }}
											onMouseEnter={(e) => {
												e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
											}}
											onMouseLeave={(e) => {
												e.currentTarget.style.backgroundColor = "transparent";
											}}
											contentEditable
											suppressContentEditableWarning
											onBlur={(e) => {
												const newText = e.currentTarget.textContent || "";
												if (newText !== stat.label) {
													// Update the stat label in the stats array
													const updatedStats = [...(props.stats as Array<any>)];
													updatedStats[idx] = { ...updatedStats[idx], label: newText };
													onUpdate({ stats: updatedStats });
												}
											}}
											onKeyDown={(e) => {
												if (e.key === "Enter") {
													e.preventDefault();
													e.currentTarget.blur();
												}
												if (e.key === "Escape") {
													e.currentTarget.textContent = stat.label;
													e.currentTarget.blur();
												}
											}}
										>
											{stat.label}
										</div>
									</div>
									));
								})()}
							</div>

							{/* Company Logos */}
							<div style={{ 
								display: "flex", 
								gap: "32px", 
								alignItems: "center", 
								flexWrap: "wrap", 
								justifyContent: "center"
							}}>
								{(() => {
									const logos = props.companyLogos as string[] || ["Microsoft", "Google", "Amazon", "Apple", "Meta"];
									return logos.map((company, idx) => (
									<div 
										key={idx} 
										onClick={(e) => {
											e.stopPropagation();
											// Inline editing - no popup needed
										}}
										style={{ 
											opacity: 0.6, 
											filter: "grayscale(100%)",
											transition: "all 0.3s ease",
											padding: "12px 24px",
											background: "white",
											borderRadius: "8px",
											boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
											cursor: "pointer"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "white";
										}}
									>
										<span 
											style={{ fontSize: "16px", fontFamily: (element.properties.socialProofLogoFontFamily as string) || "Inter", fontWeight: "600", color: "#374151", cursor: "pointer", padding: "4px", borderRadius: "4px", transition: "background-color 0.2s ease" }}
											onMouseEnter={(e) => {
												e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
											}}
											onMouseLeave={(e) => {
												e.currentTarget.style.backgroundColor = "transparent";
											}}
											contentEditable
											suppressContentEditableWarning
											onBlur={(e) => {
												const newText = e.currentTarget.textContent || "";
												if (newText !== company) {
													// Update the company name in the companyLogos array
													const updatedLogos = [...(props.companyLogos as string[])];
													updatedLogos[idx] = newText;
													onUpdate({ companyLogos: updatedLogos });
												}
											}}
											onKeyDown={(e) => {
												if (e.key === "Enter") {
													e.preventDefault();
													e.currentTarget.blur();
												}
												if (e.key === "Escape") {
													e.currentTarget.textContent = company;
													e.currentTarget.blur();
												}
											}}
										>
											{company}
										</span>
									</div>
									));
								})()}
							</div>

							{/* Trust Badges */}
							<div style={{ 
								display: "flex", 
								gap: "24px", 
								alignItems: "center", 
								flexWrap: "wrap", 
								justifyContent: "center"
							}}>
								{(() => {
									const badges = props.trustBadges as Array<{ icon: string; text: string }> || [
										{ icon: "lock", text: "SSL Secured" },
										{ icon: "check", text: "Money Back Guarantee" },
										{ icon: "shield", text: "Privacy Protected" },
										{ icon: "star", text: "5-Star Rated" }
									];
									return badges.map((badge, idx) => {
									const getIcon = (iconName: string) => {
										switch (iconName) {
											case "lock": return <FiCheck style={{ fontSize: "16px", color: "#10b981" }} />;
											case "check": return <FiCheck style={{ fontSize: "16px", color: "#10b981" }} />;
											case "shield": return <FiCheck style={{ fontSize: "16px", color: "#10b981" }} />;
											case "star": return <FiCheck style={{ fontSize: "16px", color: "#fbbf24" }} />;
											default: return <FiCheck style={{ fontSize: "16px", color: "#10b981" }} />;
										}
									};
									
									return (
										<div 
											key={idx} 
											onClick={(e) => {
												e.stopPropagation();
												// Inline editing - no popup needed
											}}
											style={{ 
												display: "flex", 
												alignItems: "center", 
												gap: "8px",
												padding: "8px 16px",
												background: "white",
												borderRadius: "20px",
												boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
												cursor: "pointer",
												transition: "all 0.2s ease"
											}}
											onMouseEnter={(e) => {
												e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
											}}
											onMouseLeave={(e) => {
												e.currentTarget.style.backgroundColor = "white";
											}}
										>
											{getIcon(badge.icon)}
											<span 
												style={{ fontSize: "14px", fontFamily: (element.properties.socialProofBadgeFontFamily as string) || "Inter", fontWeight: "500", color: "#374151", cursor: "pointer", padding: "4px", borderRadius: "4px", transition: "background-color 0.2s ease" }}
												onMouseEnter={(e) => {
													e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
												}}
												onMouseLeave={(e) => {
													e.currentTarget.style.backgroundColor = "transparent";
												}}
												contentEditable
												suppressContentEditableWarning
												onBlur={(e) => {
													const newText = e.currentTarget.textContent || "";
													if (newText !== badge.text) {
														// Update the badge text in the trustBadges array
														const updatedBadges = [...(props.trustBadges as Array<any>)];
														updatedBadges[idx] = { ...updatedBadges[idx], text: newText };
														onUpdate({ trustBadges: updatedBadges });
													}
												}}
												onKeyDown={(e) => {
													if (e.key === "Enter") {
														e.preventDefault();
														e.currentTarget.blur();
													}
													if (e.key === "Escape") {
														e.currentTarget.textContent = badge.text;
														e.currentTarget.blur();
													}
												}}
											>
												{badge.text}
											</span>
										</div>
									);
									});
								})()}
							</div>
						</div>
					</div>
				</div>
			);
		}

		if (element.type === "problem") {
			const props = element.properties;

			return (
				<div
					style={{
						padding: "80px 40px",
						background: props.useGradient && props.gradientPreset 
							? GRADIENT_PRESETS[props.gradientPreset as keyof typeof GRADIENT_PRESETS] || "#ffffff"
							: props.backgroundColor || "#ffffff",
						width: "100%",
						borderRadius: "12px",
						margin: "20px 0"
					}}
				>
					<div style={{ maxWidth: "1200px", margin: "0 auto" }}>
						<div style={{ textAlign: "center", marginBottom: "64px" }}>
							<h2
								onClick={(e) => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = {
										x: rect.left + rect.width / 2,
										y: rect.top
									};
									if (onElementClick) {
										// Create unique element identifier for problem title
										const uniqueElement = {
											...element,
											id: `${element.id}-problem-title`,
											elementType: 'problem-title',
											textPath: 'problem-title'
										};
										onElementClick(uniqueElement, position);
									}
								}}
								style={{
									fontSize: "36px",
									fontWeight: "700",
									color: "#1f2937",
									marginBottom: "16px",
									letterSpacing: "-0.02em",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== element.content) {
										onUpdate({ content: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = element.content || "The Problem You're Facing";
										e.currentTarget.blur();
									}
								}}
							>
								{typeof element.content === 'string' ? element.content : (element.content as any)?.title || (props.content as string) || "The Problem You're Facing"}
							</h2>
							<p 
								onClick={(e) => {
									e.stopPropagation();
									// Inline editing - no popup needed
								}}
								style={{ 
									fontSize: "18px", 
									color: "#6b7280", 
									maxWidth: "600px", 
									margin: "0 auto", 
									lineHeight: "1.6",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== (props.subtitle as string)) {
										onUpdate({ subtitle: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = (props.subtitle as string) || "You're not alone in facing this challenge";
										e.currentTarget.blur();
									}
								}}
							>
								{(props.subtitle as string) || "You're not alone in facing this challenge"}
							</p>
						</div>

						<div style={{ maxWidth: "800px", margin: "0 auto", textAlign: "center" }}>
							<p 
								onClick={(e) => {
									e.stopPropagation();
									// Inline editing - no popup needed
								}}
								style={{ 
									fontSize: "20px", 
									color: "#374151", 
									lineHeight: "1.7", 
									marginBottom: "32px",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== (props.description as string)) {
										onUpdate({ description: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = (props.description as string) || "Many businesses struggle with the same issues you're experiencing right now. The frustration, the wasted time, the missed opportunities - we understand because we've been there too.";
										e.currentTarget.blur();
									}
								}}
							>
								{(props.description as string) || "Many businesses struggle with the same issues you're experiencing right now. The frustration, the wasted time, the missed opportunities - we understand because we've been there too."}
							</p>

							{props.painPoints && Array.isArray(props.painPoints) ? (
								<div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))", gap: "24px", marginTop: "48px" }}>
									{(props.painPoints as string[]).map((point, idx) => (
										<div
											key={idx}
											style={{
												padding: "24px",
												background: "#f9fafb",
												borderRadius: "8px",
												border: "1px solid #e5e7eb",
											}}
										>
											<p style={{ fontSize: "16px", color: "#374151", margin: 0 }}>
												{point}
											</p>
										</div>
									))}
								</div>
							) : null}
						</div>
					</div>
				</div>
			);
		}

		if (element.type === "solution") {
			const props = element.properties;

			return (
				<div
					style={{
						padding: "80px 40px",
						background: props.useGradient && props.gradientPreset 
							? GRADIENT_PRESETS[props.gradientPreset as keyof typeof GRADIENT_PRESETS] || "#f8fafc"
							: props.backgroundColor || "#f8fafc",
						width: "100%",
						borderRadius: "12px",
						margin: "20px 0"
					}}
				>
					<div style={{ maxWidth: "1200px", margin: "0 auto" }}>
						<div style={{ textAlign: "center", marginBottom: "64px" }}>
							<h2
								data-element-content
								onClick={(e) => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = { x: rect.left + rect.width / 2, y: rect.top };
									if (onElementClick) {
										// Create unique element identifier for solution title
										const uniqueElement = {
											...element,
											id: `${element.id}-solution-title`,
											elementType: 'solution-title',
											textPath: 'solution-title'
										};
										onElementClick(uniqueElement, position);
									}
								}}
								style={{
									fontSize: (element.properties.featuresTitleFontSize as string) || (element.properties.fontSize as string) || "36px",
									fontWeight: (element.properties.featuresTitleFontWeight as string) || (element.properties.fontWeight as string) || "700",
									fontStyle: (element.properties.featuresTitleFontStyle as string) || (element.properties.fontStyle as string) || "normal",
									textDecoration: (element.properties.featuresTitleTextDecoration as string) || (element.properties.textDecoration as string) || "none",
									fontFamily: (element.properties.featuresTitleFontFamily as string) || (element.properties.fontFamily as string) || "Inter",
									color: (element.properties.featuresTitleColor as string) || (element.properties.color as string) || "#1f2937",
									textAlign: (element.properties.featuresTitleTextAlign as "left" | "center" | "right") || (element.properties.textAlign as "left" | "center" | "right") || "center",
									marginBottom: "16px",
									letterSpacing: "-0.02em",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== element.content) {
										onUpdate({ content: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = element.content || "The Perfect Solution";
										e.currentTarget.blur();
									}
								}}
							>
								{typeof element.content === 'string' ? element.content : (element.content as any)?.title || (props.content as string) || "The Perfect Solution"}
							</h2>
							<p
								data-element-content
								onClick={(e) => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = { x: rect.left + rect.width / 2, y: rect.top };
									if (onElementClick) {
										// Create unique element identifier for features description
										const uniqueElement = {
											...element,
											id: `${element.id}-features-description`,
											elementType: 'features-description',
											textPath: 'features-description'
										};
										onElementClick(uniqueElement, position);
									}
								}}
								style={{
									fontSize: (element.properties.featuresDescriptionFontSize as string) || (element.properties.fontSize as string) || "18px",
									fontWeight: (element.properties.featuresDescriptionFontWeight as string) || (element.properties.fontWeight as string) || "400",
									fontStyle: (element.properties.featuresDescriptionFontStyle as string) || (element.properties.fontStyle as string) || "normal",
									textDecoration: (element.properties.featuresDescriptionTextDecoration as string) || (element.properties.textDecoration as string) || "none",
									fontFamily: (element.properties.featuresDescriptionFontFamily as string) || (element.properties.fontFamily as string) || "Inter",
									color: (element.properties.featuresDescriptionColor as string) || (element.properties.color as string) || "#6b7280",
									textAlign: (element.properties.featuresDescriptionTextAlign as "left" | "center" | "right") || (element.properties.textAlign as "left" | "center" | "right") || "center",
									maxWidth: "600px",
									margin: "0 auto",
									lineHeight: "1.6",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== (props.subtitle as string)) {
										onUpdate({ subtitle: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = (props.subtitle as string) || "Everything you need to solve this problem";
										e.currentTarget.blur();
									}
								}}
							>
								{(props.subtitle as string) || "Everything you need to solve this problem"}
							</p>
						</div>

						<div style={{ maxWidth: "800px", margin: "0 auto", textAlign: "center" }}>
							<p 
								onClick={(e) => {
									e.stopPropagation();
									// Inline editing - no popup needed
								}}
								style={{ 
									fontSize: "20px", 
									color: "#374151", 
									lineHeight: "1.7", 
									marginBottom: "32px",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== (props.description as string)) {
										onUpdate({ description: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = (props.description as string) || "Our solution addresses every pain point you've been experiencing. Here's exactly what you get and how it transforms your business.";
										e.currentTarget.blur();
									}
								}}
							>
								{(props.description as string) || "Our solution addresses every pain point you've been experiencing. Here's exactly what you get and how it transforms your business."}
							</p>

							<div
								style={{
									padding: "32px",
									background: "white",
									borderRadius: "12px",
									border: "1px solid #e5e7eb",
									boxShadow: "0 4px 6px rgba(0, 0, 0, 0.05)",
									marginTop: "48px",
								}}
							>
								<h3 
									onClick={(e) => {
										e.stopPropagation();
										// Inline editing - no popup needed
									}}
									style={{ 
										fontSize: "24px", 
										fontWeight: "600", 
										color: "#1f2937", 
										marginBottom: "16px",
										cursor: "pointer",
										padding: "8px",
										borderRadius: "8px",
										transition: "background-color 0.2s ease"
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = "transparent";
									}}
									contentEditable
									suppressContentEditableWarning
									onBlur={(e) => {
										const newText = e.currentTarget.textContent || "";
										if (newText !== (props.solutionTitle as string)) {
											onUpdate({ solutionTitle: newText });
										}
									}}
									onKeyDown={(e) => {
										if (e.key === "Enter") {
											e.preventDefault();
											e.currentTarget.blur();
										}
										if (e.key === "Escape") {
											e.currentTarget.textContent = (props.solutionTitle as string) || "What's Inside";
											e.currentTarget.blur();
										}
									}}
								>
									{(props.solutionTitle as string) || "What's Inside"}
								</h3>
								<p 
									onClick={(e) => {
										e.stopPropagation();
										// Inline editing - no popup needed
									}}
									style={{ 
										fontSize: "16px", 
										color: "#6b7280", 
										lineHeight: "1.6",
										cursor: "pointer",
										padding: "8px",
										borderRadius: "8px",
										transition: "background-color 0.2s ease"
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = "transparent";
									}}
									contentEditable
									suppressContentEditableWarning
									onBlur={(e) => {
										const newText = e.currentTarget.textContent || "";
										if (newText !== (props.solutionDescription as string)) {
											onUpdate({ solutionDescription: newText });
										}
									}}
									onKeyDown={(e) => {
										if (e.key === "Enter") {
											e.preventDefault();
											e.currentTarget.blur();
										}
										if (e.key === "Escape") {
											e.currentTarget.textContent = (props.solutionDescription as string) || "A comprehensive solution designed specifically to address your challenges and deliver real results.";
											e.currentTarget.blur();
										}
									}}
								>
									{(props.solutionDescription as string) || "A comprehensive solution designed specifically to address your challenges and deliver real results."}
								</p>
							</div>
						</div>
					</div>
				</div>
			);
		}

		if (element.type === "features") {
			const props = element.properties;

			return (
				<div
					style={{
						padding: "80px 40px",
						background: props.useGradient && props.gradientPreset 
							? GRADIENT_PRESETS[props.gradientPreset as keyof typeof GRADIENT_PRESETS] || "#ffffff"
							: props.backgroundColor || "#ffffff",
						width: "100%",
						borderRadius: "12px",
						margin: "20px 0"
					}}
				>
					<div style={{ maxWidth: "1200px", margin: "0 auto" }}>
						{/* Section Header */}
						<div style={{ textAlign: "center", marginBottom: "64px" }}>
							<Heading 
								onClick={(e) => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = { x: rect.left + rect.width / 2, y: rect.top };
									if (onElementClick) {
										// Create unique element identifier for features title
										const uniqueElement = {
											...element,
											id: `${element.id}-features-title`,
											elementType: 'features-title',
											textPath: 'features-title'
										};
										onElementClick(uniqueElement, position);
									}
								}}
								size="8" 
								style={{ 
									marginBottom: "16px", 
									color: "#111827",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== element.content) {
										onUpdate({ content: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = element.content || "What You Get Inside";
										e.currentTarget.blur();
									}
								}}
							>
								{typeof element.content === 'string' ? element.content : (element.content as any)?.title || "What You Get Inside"}
							</Heading>
							<Text 
								onClick={(e) => {
									e.stopPropagation();
									// Inline editing - no popup needed
								}}
								size="5" 
								style={{ 
									color: "#6b7280", 
									lineHeight: "1.6", 
									maxWidth: "600px", 
									margin: "0 auto", 
									display: "block",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== (props.subheading as string)) {
										onUpdate({ subheading: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = (props.subheading as string) || "Everything you need to transform your results";
										e.currentTarget.blur();
									}
								}}
							>
								{(props.subheading as string) || "Everything you need to transform your results"}
							</Text>
						</div>

						{/* Features List */}
						<div
							style={{
								display: "flex",
								flexDirection: "column",
								gap: "24px",
								maxWidth: "800px",
								margin: "0 auto",
							}}
						>
						{((props.features as Array<{ title: string; description: string; icon?: string; iconUrl?: string; ctaText?: string; ctaUrl?: string }>) || [
							{ title: "Complete Step-by-Step System", description: "Get the exact blueprint that's generated over $10M in sales", icon: "✓" },
							{ title: "Proven Templates & Scripts", description: "Copy-paste templates that convert at 15%+ conversion rates", icon: "✓" },
							{ title: "Live Implementation Training", description: "Watch over-the-shoulder as we build a funnel from scratch", icon: "✓" },
							{ title: "Private Community Access", description: "Join 5,000+ entrepreneurs sharing wins and strategies", icon: "✓" },
							{ title: "30-Day Money-Back Guarantee", description: "Try it risk-free - if it doesn't work, get every penny back", icon: "✓" }
						]).map((feature, idx) => (
							<div
								key={idx}
								style={{
									display: "flex",
									alignItems: "flex-start",
									gap: "16px",
									padding: "20px 0",
									borderBottom: idx < ((props.features as Array<{ title: string; description: string }>) || []).length - 1 ? "1px solid #f3f4f6" : "none",
								}}
							>
								{/* Checkmark Icon */}
								<div
									style={{
										width: "32px",
										height: "32px",
										background: "#10b981",
										borderRadius: "50%",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										flexShrink: 0,
										marginTop: "4px",
									}}
								>
									<FiCheck style={{ fontSize: "18px", color: "white", fontWeight: "bold" }} />
								</div>

								{/* Feature Content */}
								<div style={{ flex: 1 }}>
									<Heading 
										size="4" 
										style={{ 
											marginBottom: "8px", 
											color: "#111827", 
											fontWeight: "600",
											cursor: "pointer",
											padding: "4px",
											borderRadius: "4px",
											transition: "background-color 0.2s ease"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "transparent";
										}}
										contentEditable
										suppressContentEditableWarning
										onBlur={(e) => {
											const newText = e.currentTarget.textContent || "";
											if (newText !== feature.title) {
												// Update the feature title in the features array
												const updatedFeatures = [...(props.features as Array<any>)];
												updatedFeatures[idx] = { ...updatedFeatures[idx], title: newText };
												onUpdate({ features: updatedFeatures });
											}
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												e.preventDefault();
												e.currentTarget.blur();
											}
											if (e.key === "Escape") {
												e.currentTarget.textContent = feature.title;
												e.currentTarget.blur();
											}
										}}
									>
										{feature.title}
									</Heading>
									<Text 
										size="3" 
										style={{ 
											color: "#6b7280", 
											lineHeight: "1.6",
											cursor: "pointer",
											padding: "4px",
											borderRadius: "4px",
											transition: "background-color 0.2s ease"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "transparent";
										}}
										contentEditable
										suppressContentEditableWarning
										onBlur={(e) => {
											const newText = e.currentTarget.textContent || "";
											if (newText !== feature.description) {
												// Update the feature description in the features array
												const updatedFeatures = [...(props.features as Array<any>)];
												updatedFeatures[idx] = { ...updatedFeatures[idx], description: newText };
												onUpdate({ features: updatedFeatures });
											}
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												e.preventDefault();
												e.currentTarget.blur();
											}
											if (e.key === "Escape") {
												e.currentTarget.textContent = feature.description;
												e.currentTarget.blur();
											}
										}}
									>
										{feature.description}
									</Text>
								</div>
							</div>
						))}
						</div>
					</div>
				</div>
			);
		}

		if (element.type === "pricing") {
			const props = element.properties;
			const bgStyle = props.useGradient
				? `linear-gradient(135deg, ${props.gradientStart || "#f9fafb"} 0%, ${props.gradientEnd || "#f3f4f6"} 100%)`
				: props.backgroundColor || "#f9fafb";
			
			return (
				<div
					style={{
						padding: "80px 40px",
						background: props.useGradient && props.gradientPreset 
							? GRADIENT_PRESETS[props.gradientPreset as keyof typeof GRADIENT_PRESETS] || bgStyle
							: bgStyle,
						width: "100%",
						minHeight: (props.height as string) || "auto",
						borderRadius: "12px",
						margin: "20px 0"
					}}
				>
					<div style={{ textAlign: "center", marginBottom: "60px", maxWidth: "800px", margin: "0 auto 60px auto" }}>
						<h2
							data-element-content
							onClick={(e) => {
								e.stopPropagation();
								// Trigger tooltip for this element
								const rect = e.currentTarget.getBoundingClientRect();
								const position = { x: rect.left + rect.width / 2, y: rect.top };
								if (onElementClick) {
									// Create unique element identifier for pricing title
									const uniqueElement = {
										...element,
										id: `${element.id}-pricing-title`,
										elementType: 'pricing-title',
										textPath: 'pricing-title'
									};
									onElementClick(uniqueElement, position);
								}
							}}
							style={{
								fontSize: (element.properties.pricingTitleFontSize as string) || (element.properties.fontSize as string) || "42px",
								fontWeight: (element.properties.pricingTitleFontWeight as string) || (element.properties.fontWeight as string) || "700",
								fontStyle: (element.properties.pricingTitleFontStyle as string) || (element.properties.fontStyle as string) || "normal",
								textDecoration: (element.properties.pricingTitleTextDecoration as string) || (element.properties.textDecoration as string) || "none",
								fontFamily: (element.properties.pricingTitleFontFamily as string) || (element.properties.fontFamily as string) || "Inter",
								color: (element.properties.pricingTitleColor as string) || (element.properties.color as string) || props.textColor || "#111827",
								textAlign: (element.properties.pricingTitleTextAlign as "left" | "center" | "right") || (element.properties.textAlign as "left" | "center" | "right") || "center",
								marginBottom: "16px",
								letterSpacing: "-0.02em",
								cursor: "pointer",
								padding: "8px",
								borderRadius: "8px",
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor = "transparent";
							}}
							contentEditable
							suppressContentEditableWarning
							onBlur={(e) => {
								const newText = e.currentTarget.textContent || "";
								if (newText !== element.content && newText !== (props.content as string)) {
									onUpdate({ content: newText });
								}
							}}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									e.preventDefault();
									e.currentTarget.blur();
								}
								if (e.key === "Escape") {
									e.currentTarget.textContent = element.content || (props.content as string) || "Pricing Plans";
									e.currentTarget.blur();
								}
							}}
						>
							{typeof element.content === 'string' ? element.content : (element.content as any)?.title || (props.content as string) || "Pricing Plans"}
						</h2>
						<p 
							onClick={(e) => {
								e.stopPropagation();
									// Inline editing - no popup needed
							}}
							style={{ 
								fontSize: "18px", 
								color: props.textColor ? `${props.textColor}cc` : "#6b7280", 
								lineHeight: "1.6",
								cursor: "pointer",
								padding: "8px",
								borderRadius: "8px",
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor = "transparent";
							}}
							contentEditable
							suppressContentEditableWarning
							onBlur={(e) => {
								const newText = e.currentTarget.textContent || "";
								if (newText !== (props.subtitle as string)) {
									onUpdate({ subtitle: newText });
								}
							}}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									e.preventDefault();
									e.currentTarget.blur();
								}
								if (e.key === "Escape") {
									e.currentTarget.textContent = (props.subtitle as string) || "Choose the plan that's right for you";
									e.currentTarget.blur();
								}
							}}
						>
							{(props.subtitle as string) || "Choose the plan that's right for you"}
						</p>
					</div>
					<div
						style={{
							display: "grid",
							gridTemplateColumns: `repeat(${props.columns || 3}, 1fr)`,
							gap: `${props.planGap || 24}px`,
							maxWidth: "1000px",
							margin: "0 auto",
							padding: "0 20px",
						}}
					>
						{((props.plans as Array<{
							name: string;
							price: string;
							period: string;
							features: string[];
							popular: boolean;
						}>) || [
							{
								name: "Basic",
								price: "$29",
								period: "/month",
								features: ["Basic Features", "Email Support", "5GB Storage"],
								popular: false,
							},
							{
								name: "Pro",
								price: "$79",
								period: "/month",
								features: ["All Features", "Priority Support", "50GB Storage"],
								popular: true,
							},
							{
								name: "Enterprise",
								price: "$199",
								period: "/month",
								features: ["Everything", "24/7 Support", "Unlimited Storage"],
								popular: false,
							},
						]).map((plan, idx) => (
							<div
								key={idx}
								style={{
									padding: "24px",
									background: (props.planBackground as string) || "white",
									borderRadius: "12px", // Lighter border radius
									border: plan.popular
										? "1px solid #3b82f6" // Thinner border for popular plan
										: "1px solid #e5e7eb", // Subtle gray border
									position: "relative",
									boxShadow: plan.popular
										? "0 4px 12px rgba(59, 130, 246, 0.08)" // Much lighter shadow
										: "0 2px 8px rgba(0, 0, 0, 0.04)", // Very subtle shadow
									// No transform scaling - keep it flat and clean
									transition: "all 0.2s ease",
								}}
							>
								{plan.popular && (
									<div
										style={{
											position: "absolute",
											top: "-8px",
											left: "50%",
											transform: "translateX(-50%)",
											background: "#3b82f6", // Simple solid blue
											color: "white",
											padding: "4px 16px",
											borderRadius: "16px",
											fontSize: "11px",
											fontWeight: "600",
											letterSpacing: "0.3px",
											// No shadow - keep it clean
										}}
									>
										{(props.popularBadgeText as string) || "POPULAR"}
									</div>
								)}
								<div style={{ marginBottom: "20px", textAlign: "center" }}>
									<h3
										onClick={(e) => {
											e.stopPropagation();
											// Trigger tooltip for this element
											const rect = e.currentTarget.getBoundingClientRect();
											const position = { x: rect.left + rect.width / 2, y: rect.top };
											if (onElementClick) {
												// Create unique element identifier for plan title
												const uniqueElement = {
													...element,
													id: `${element.id}-plan-${idx}-title`,
													elementType: 'plan-title',
													textPath: `plan-${idx}-title`,
													content: plan.name
												};
												onElementClick(uniqueElement, position);
											}
										}}
										style={{
											fontSize: "20px",
											fontWeight: "600",
											color: "#1f2937",
											marginBottom: "8px",
											cursor: "pointer",
											padding: "4px",
											borderRadius: "4px",
											transition: "background-color 0.2s ease"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "transparent";
										}}
										contentEditable
										suppressContentEditableWarning
										onBlur={(e) => {
											const newText = e.currentTarget.textContent || "";
											if (newText !== plan.name) {
												// Update the plan name in the plans array
												const updatedPlans = [...(props.plans as Array<any>)];
												updatedPlans[idx] = { ...updatedPlans[idx], name: newText };
												onUpdate({ plans: updatedPlans });
											}
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												e.preventDefault();
												e.currentTarget.blur();
											}
											if (e.key === "Escape") {
												e.currentTarget.textContent = plan.name;
												e.currentTarget.blur();
											}
										}}
									>
										{plan.name}
									</h3>
									<div style={{ marginBottom: "12px" }}>
										<span
											onClick={(e) => {
												e.stopPropagation();
												// Trigger tooltip for this element
												const rect = e.currentTarget.getBoundingClientRect();
												const position = { x: rect.left + rect.width / 2, y: rect.top };
												if (onElementClick) {
													// Create unique element identifier for plan price
													const uniqueElement = {
														...element,
														id: `${element.id}-plan-${idx}-price`,
														elementType: 'plan-price',
														textPath: `plan-${idx}-price`,
														content: plan.price
													};
													onElementClick(uniqueElement, position);
												}
											}}
											style={{
												fontSize: "36px",
												fontWeight: "700",
												color: "#1f2937",
												cursor: "pointer",
												padding: "4px",
												borderRadius: "4px",
												transition: "background-color 0.2s ease"
											}}
											onMouseEnter={(e) => {
												e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
											}}
											onMouseLeave={(e) => {
												e.currentTarget.style.backgroundColor = "transparent";
											}}
											contentEditable
											suppressContentEditableWarning
											onBlur={(e) => {
												const newText = e.currentTarget.textContent || "";
												if (newText !== plan.price) {
													// Update the plan price in the plans array
													const updatedPlans = [...(props.plans as Array<any>)];
													updatedPlans[idx] = { ...updatedPlans[idx], price: newText };
													onUpdate({ plans: updatedPlans });
												}
											}}
											onKeyDown={(e) => {
												if (e.key === "Enter") {
													e.preventDefault();
													e.currentTarget.blur();
												}
												if (e.key === "Escape") {
													e.currentTarget.textContent = plan.price;
													e.currentTarget.blur();
												}
											}}
										>
											{plan.price}
										</span>
										<span 
											style={{ 
												fontSize: "14px", 
												color: "#6b7280", 
												marginLeft: "2px",
												cursor: "pointer",
												padding: "4px",
												borderRadius: "4px",
												transition: "background-color 0.2s ease"
											}}
											onMouseEnter={(e) => {
												e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
											}}
											onMouseLeave={(e) => {
												e.currentTarget.style.backgroundColor = "transparent";
											}}
											contentEditable
											suppressContentEditableWarning
											onBlur={(e) => {
												const newText = e.currentTarget.textContent || "";
												if (newText !== plan.period) {
													// Update the plan period in the plans array
													const updatedPlans = [...(props.plans as Array<any>)];
													updatedPlans[idx] = { ...updatedPlans[idx], period: newText };
													onUpdate({ plans: updatedPlans });
												}
											}}
											onKeyDown={(e) => {
												if (e.key === "Enter") {
													e.preventDefault();
													e.currentTarget.blur();
												}
												if (e.key === "Escape") {
													e.currentTarget.textContent = plan.period;
													e.currentTarget.blur();
												}
											}}
										>
											{plan.period}
										</span>
									</div>
								</div>
								<ul style={{ listStyle: "none", padding: 0, marginBottom: "24px" }}>
									{plan.features.map((feature, featureIdx) => (
										<li
											key={featureIdx}
											style={{
												display: "flex",
												alignItems: "center",
												gap: "12px",
												padding: "12px 0",
												fontSize: "15px",
												color: (props.featureColor as string) || "#374151",
											}}
										>
											<svg
												width="20"
												height="20"
												viewBox="0 0 24 24"
												fill="none"
												stroke="#667eea"
												strokeWidth="3"
											>
												<path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
											</svg>
											<span
												onClick={(e) => {
													e.stopPropagation();
													// Trigger tooltip for this element
													const rect = e.currentTarget.getBoundingClientRect();
													const position = { x: rect.left + rect.width / 2, y: rect.top };
													if (onElementClick) {
														// Create unique element identifier for plan feature
														const uniqueElement = {
															...element,
															id: `${element.id}-plan-${idx}-feature-${featureIdx}`,
															elementType: 'plan-feature',
															textPath: `plan-${idx}-feature-${featureIdx}`,
															content: feature
														};
														onElementClick(uniqueElement, position);
													}
												}}
												style={{
													cursor: "pointer",
													padding: "2px 4px",
													borderRadius: "4px",
													transition: "background-color 0.2s ease",
													flex: 1
												}}
												onMouseEnter={(e) => {
													e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
												}}
												onMouseLeave={(e) => {
													e.currentTarget.style.backgroundColor = "transparent";
												}}
												contentEditable
												suppressContentEditableWarning
												onBlur={(e) => {
													const newText = e.currentTarget.textContent || "";
													if (newText !== feature) {
														// Update the feature in the plans array
														const updatedPlans = [...(props.plans as Array<any>)];
														const updatedFeatures = [...updatedPlans[idx].features];
														updatedFeatures[featureIdx] = newText;
														updatedPlans[idx] = { ...updatedPlans[idx], features: updatedFeatures };
														onUpdate({ plans: updatedPlans });
													}
												}}
												onKeyDown={(e) => {
													if (e.key === "Enter") {
														e.preventDefault();
														e.currentTarget.blur();
													}
													if (e.key === "Escape") {
														e.currentTarget.textContent = feature;
														e.currentTarget.blur();
													}
												}}
											>
												{feature}
											</span>
										</li>
									))}
								</ul>
								<button
									type="button"
									style={{
										width: "100%",
										padding: "12px 20px",
										background: plan.popular
											? "#3b82f6" // Simple solid blue
											: "#f8fafc", // Light gray background for non-popular
										color: plan.popular
											? "white"
											: "#374151", // Softer text color
										fontSize: "14px",
										fontWeight: "500",
										borderRadius: "8px",
										border: plan.popular ? "none" : "1px solid #e5e7eb",
										cursor: "pointer",
										boxShadow: "inset 0 1px 2px rgba(0, 0, 0, 0.05)", // Very subtle inner shadow
										transition: "all 0.2s ease",
									}}
									onMouseEnter={(e) => {
										if (plan.popular) {
											e.currentTarget.style.background = "#2563eb";
										} else {
											e.currentTarget.style.background = "#f1f5f9";
										}
									}}
									onMouseLeave={(e) => {
										if (plan.popular) {
											e.currentTarget.style.background = "#3b82f6";
										} else {
											e.currentTarget.style.background = "#f8fafc";
										}
									}}
									contentEditable
									suppressContentEditableWarning
									onBlur={(e) => {
										const newText = e.currentTarget.textContent || "";
										if (newText !== (props.buttonText as string)) {
											onUpdate({ buttonText: newText });
										}
									}}
									onKeyDown={(e) => {
										if (e.key === "Enter") {
											e.preventDefault();
											e.currentTarget.blur();
										}
										if (e.key === "Escape") {
											e.currentTarget.textContent = (props.buttonText as string) || "Get Started";
											e.currentTarget.blur();
										}
									}}
								>
									{(props.buttonText as string) || "Get Started"}
								</button>
							</div>
						))}
					</div>
				</div>
			);
		}

		if (element.type === "testimonials") {
			const props = element.properties;

			return (
				<div
					style={{
						padding: "80px 40px",
						background: props.useGradient && props.gradientPreset 
							? GRADIENT_PRESETS[props.gradientPreset as keyof typeof GRADIENT_PRESETS] || "#f8fafc"
							: props.backgroundColor || "#f8fafc",
						width: "100%",
						borderRadius: "12px",
						maxWidth: (props.maxWidth as string) || "100%",
						minHeight: (props.height as string) || "auto",
						margin: "20px 0"
					}}
				>
					<div style={{ textAlign: "center", marginBottom: "60px", maxWidth: "800px", margin: "0 auto 60px auto" }}>
						<h2
							data-element-content
							onClick={(e) => {
								e.stopPropagation();
								// Trigger tooltip for this element
								const rect = e.currentTarget.getBoundingClientRect();
								const position = { x: rect.left + rect.width / 2, y: rect.top };
								if (onElementClick) {
									// Create unique element identifier for testimonials title
									const uniqueElement = {
										...element,
										id: `${element.id}-testimonials-title`,
										elementType: 'testimonials-title',
										textPath: 'testimonials-title'
									};
									onElementClick(uniqueElement, position);
								}
							}}
							style={{
								fontSize: (element.properties.testimonialsTitleFontSize as string) || (element.properties.fontSize as string) || "42px",
								fontWeight: (element.properties.testimonialsTitleFontWeight as string) || (element.properties.fontWeight as string) || "700",
								fontStyle: (element.properties.testimonialsTitleFontStyle as string) || (element.properties.fontStyle as string) || "normal",
								textDecoration: (element.properties.testimonialsTitleTextDecoration as string) || (element.properties.textDecoration as string) || "none",
								fontFamily: (element.properties.testimonialsTitleFontFamily as string) || (element.properties.fontFamily as string) || "Inter",
								color: (element.properties.testimonialsTitleColor as string) || (element.properties.color as string) || props.textColor || "#1f2937",
								textAlign: (element.properties.testimonialsTitleTextAlign as "left" | "center" | "right") || (element.properties.textAlign as "left" | "center" | "right") || "center",
								marginBottom: "16px",
								letterSpacing: "-0.02em",
								cursor: "pointer",
								padding: "8px",
								borderRadius: "8px",
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor = "transparent";
							}}
						>
							{typeof element.content === 'string' ? element.content : (element.content as any)?.title || (props.content as string) || "Customer Reviews"}
						</h2>
						<p style={{ fontSize: "18px", color: props.textColor ? `${props.textColor}cc` : "#6b7280", lineHeight: "1.6" }}>
							{(props.subtitle as string) || "See what our users have to say"}
						</p>
					</div>
					<div
						style={{
							display: "grid",
							gridTemplateColumns: `repeat(${props.columns || 3}, 1fr)`,
							gap: `${props.testimonialGap || 32}px`,
						}}
					>
						{((props.testimonials as Array<{
							quote: string;
							author: string;
							role: string;
							avatar: string;
							rating: number;
						}>) || [
							{
								quote: "This product has completely transformed how we work. Absolutely amazing!",
								author: "Sarah Johnson",
								role: "CEO, TechCorp",
								avatar: "SJ",
								rating: 5,
							},
							{
								quote: "The best investment we've made this year. Highly recommend to everyone.",
								author: "Michael Chen",
								role: "Founder, StartupXYZ",
								avatar: "MC",
								rating: 5,
							},
							{
								quote: "Outstanding support and incredible features. Worth every penny!",
								author: "Emma Williams",
								role: "Product Manager",
								avatar: "EW",
								rating: 5,
							},
						]).map((testimonial, idx) => (
							<div
								key={idx}
								style={{
									padding: "32px",
									background: (props.testimonialBackground as string) || "white",
									borderRadius: `${props.testimonialBorderRadius || 16}px`,
									border: `1px solid ${(props.testimonialBorderColor as string) || "#e5e7eb"}`,
									boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
								}}
							>
								<div style={{ display: "flex", gap: "6px", marginBottom: "16px" }}>
									{[...Array(testimonial.rating)].map((_, starIdx) => (
										<svg
											key={starIdx}
											width="24"
											height="24"
											viewBox="0 0 24 24"
											fill={(props.starColor as string) || "#fbbf24"}
										>
											<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
										</svg>
									))}
								</div>
								<p
									onClick={(e) => {
										e.stopPropagation();
										// Trigger tooltip for this element
										const rect = e.currentTarget.getBoundingClientRect();
										const position = { x: rect.left + rect.width / 2, y: rect.top };
										if (onElementClick) {
											// Create unique element identifier for testimonial quote
											const uniqueElement = {
												...element,
												id: `${element.id}-testimonial-${idx}-quote`,
												elementType: 'testimonial-quote',
												textPath: `testimonial-${idx}-quote`,
												content: testimonial.quote
											};
											onElementClick(uniqueElement, position);
										}
									}}
									style={{
										fontSize: "16px",
										fontStyle: "italic",
										color: (props.quoteColor as string) || "#374151",
										lineHeight: "1.6",
										marginBottom: "24px",
										cursor: "pointer",
										padding: "8px",
										borderRadius: "6px",
										transition: "background-color 0.2s ease"
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = "transparent";
									}}
							>
								&ldquo;{testimonial.quote}&rdquo;
							</p>
								<div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
									<div
										style={{
											width: "48px",
											height: "48px",
											borderRadius: "50%",
											background: (props.avatarBackground as string) || "#e5e7eb",
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
											color: "#374151",
											fontWeight: "700",
											fontSize: "16px",
										}}
									>
										{testimonial.avatar}
									</div>
									<div>
										<div
											onClick={(e) => {
												e.stopPropagation();
												// Trigger tooltip for this element
												const rect = e.currentTarget.getBoundingClientRect();
												const position = { x: rect.left + rect.width / 2, y: rect.top };
												if (onElementClick) {
													// Create unique element identifier for testimonial author
													const uniqueElement = {
														...element,
														id: `${element.id}-testimonial-${idx}-author`,
														elementType: 'testimonial-author',
														textPath: `testimonial-${idx}-author`,
														content: testimonial.author
													};
													onElementClick(uniqueElement, position);
												}
											}}
											style={{
												fontSize: "16px",
												fontWeight: "600",
												color: (props.authorColor as string) || "#111827",
												cursor: "pointer",
												padding: "4px",
												borderRadius: "4px",
												transition: "background-color 0.2s ease"
											}}
											onMouseEnter={(e) => {
												e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
											}}
											onMouseLeave={(e) => {
												e.currentTarget.style.backgroundColor = "transparent";
											}}
										>
											{testimonial.author}
										</div>
										<div
											onClick={(e) => {
												e.stopPropagation();
												// Trigger tooltip for this element
												const rect = e.currentTarget.getBoundingClientRect();
												const position = { x: rect.left + rect.width / 2, y: rect.top };
												if (onElementClick) {
													// Create unique element identifier for testimonial role
													const uniqueElement = {
														...element,
														id: `${element.id}-testimonial-${idx}-role`,
														elementType: 'testimonial-role',
														textPath: `testimonial-${idx}-role`,
														content: testimonial.role
													};
													onElementClick(uniqueElement, position);
												}
											}}
											style={{
												fontSize: "14px",
												color: (props.roleColor as string) || "#6b7280",
												cursor: "pointer",
												padding: "4px",
												borderRadius: "4px",
												transition: "background-color 0.2s ease"
											}}
											onMouseEnter={(e) => {
												e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
											}}
											onMouseLeave={(e) => {
												e.currentTarget.style.backgroundColor = "transparent";
											}}
										>
											{testimonial.role}
										</div>
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			);
		}

		if (element.type === "faq") {
			const props = element.properties;

			return (
				<div
					style={{
						padding: "80px 40px",
						background: props.useGradient && props.gradientPreset 
							? GRADIENT_PRESETS[props.gradientPreset as keyof typeof GRADIENT_PRESETS] || "white"
							: props.backgroundColor || "white",
						width: "100%",
						borderRadius: "12px",
						margin: "20px 0"
					}}
				>
					<div style={{ maxWidth: "1200px", margin: "0 auto" }}>
						<div style={{ textAlign: "center", marginBottom: "64px" }}>
							<h2
								onClick={(e) => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = { x: rect.left + rect.width / 2, y: rect.top };
									if (onElementClick) {
										// Create unique element identifier for FAQ title
										const uniqueElement = {
											...element,
											id: `${element.id}-faq-title`,
											elementType: 'faq-title',
											textPath: 'faq-title'
										};
										onElementClick(uniqueElement, position);
									}
								}}
								style={{
									fontSize: "36px",
									fontWeight: "700",
									color: "#1f2937",
									marginBottom: "16px",
									letterSpacing: "-0.02em",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== element.content) {
										onUpdate({ content: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = element.content || "Frequently Asked Questions";
										e.currentTarget.blur();
									}
								}}
							>
								{typeof element.content === 'string' ? element.content : (element.content as any)?.title || (props.content as string) || "Frequently Asked Questions"}
							</h2>
							<p 
								onClick={(e) => {
									e.stopPropagation();
									// Inline editing - no popup needed
								}}
								style={{ 
									fontSize: "18px", 
									color: "#6b7280", 
									maxWidth: "600px", 
									margin: "0 auto", 
									lineHeight: "1.6",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== (props.subtitle as string)) {
										onUpdate({ subtitle: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = (props.subtitle as string) || "Get answers to common questions";
										e.currentTarget.blur();
									}
								}}
							>
								{(props.subtitle as string) || "Get answers to common questions"}
							</p>
						</div>

						<div style={{ maxWidth: "800px", margin: "0 auto" }}>
							{((props.faqs as Array<{
								question: string;
								answer: string;
							}>) || [
								{
									question: "How does this work?",
									answer: "Our solution is designed to be simple and effective. You'll get step-by-step guidance to achieve your goals."
								},
								{
									question: "Is there a money-back guarantee?",
									answer: "Yes! We offer a 30-day money-back guarantee. If you're not satisfied, we'll refund your purchase."
								},
								{
									question: "How quickly will I see results?",
									answer: "Most customers see results within the first week of implementation. Your success is our priority."
								},
								{
									question: "Do I need any technical skills?",
									answer: "Not at all! Our solution is designed for everyone, regardless of technical background."
								}
							]).map((faq, idx) => (
								<div
									key={idx}
									style={{
										padding: "24px",
										background: "#f9fafb",
										borderRadius: "8px",
										border: "1px solid #e5e7eb",
										marginBottom: "16px",
									}}
								>
									<h3
										onClick={(e) => {
											e.stopPropagation();
											// Trigger tooltip for this element
											const rect = e.currentTarget.getBoundingClientRect();
											const position = { x: rect.left + rect.width / 2, y: rect.top };
											if (onElementClick) {
												// Create unique element identifier for FAQ question
												const uniqueElement = {
													...element,
													id: `${element.id}-faq-${idx}-question`,
													elementType: 'faq-question',
													textPath: `faq-${idx}-question`,
													content: faq.question
												};
												onElementClick(uniqueElement, position);
											}
										}}
										style={{
											fontSize: "18px",
											fontWeight: "600",
											color: "#1f2937",
											marginBottom: "12px",
											cursor: "pointer",
											padding: "8px",
											borderRadius: "8px",
											transition: "background-color 0.2s ease"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "transparent";
										}}
										contentEditable
										suppressContentEditableWarning
										onBlur={(e) => {
											const newText = e.currentTarget.textContent || "";
											if (newText !== faq.question) {
												// Update the FAQ question in the faqs array
												const updatedFaqs = [...(props.faqs as Array<any>)];
												updatedFaqs[idx] = { ...updatedFaqs[idx], question: newText };
												onUpdate({ faqs: updatedFaqs });
											}
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												e.preventDefault();
												e.currentTarget.blur();
											}
											if (e.key === "Escape") {
												e.currentTarget.textContent = faq.question;
												e.currentTarget.blur();
											}
										}}
									>
										{faq.question}
									</h3>
									<p
										onClick={(e) => {
											e.stopPropagation();
											// Trigger tooltip for this element
											const rect = e.currentTarget.getBoundingClientRect();
											const position = { x: rect.left + rect.width / 2, y: rect.top };
											if (onElementClick) {
												// Create unique element identifier for FAQ answer
												const uniqueElement = {
													...element,
													id: `${element.id}-faq-${idx}-answer`,
													elementType: 'faq-answer',
													textPath: `faq-${idx}-answer`,
													content: faq.answer
												};
												onElementClick(uniqueElement, position);
											}
										}}
										style={{
											fontSize: "16px",
											color: "#6b7280",
											lineHeight: "1.6",
											margin: 0,
											cursor: "pointer",
											padding: "8px",
											borderRadius: "8px",
											transition: "background-color 0.2s ease"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "transparent";
										}}
										contentEditable
										suppressContentEditableWarning
										onBlur={(e) => {
											const newText = e.currentTarget.textContent || "";
											if (newText !== faq.answer) {
												// Update the FAQ answer in the faqs array
												const updatedFaqs = [...(props.faqs as Array<any>)];
												updatedFaqs[idx] = { ...updatedFaqs[idx], answer: newText };
												onUpdate({ faqs: updatedFaqs });
											}
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												e.preventDefault();
												e.currentTarget.blur();
											}
											if (e.key === "Escape") {
												e.currentTarget.textContent = faq.answer;
												e.currentTarget.blur();
											}
										}}
									>
										{faq.answer}
									</p>
								</div>
							))}
						</div>
					</div>
				</div>
			);
		}

		if (element.type === "form") {
			const props = element.properties;
			const bgStyle = props.useGradient
				? `linear-gradient(135deg, ${props.gradientStart || "#f9fafb"} 0%, ${props.gradientEnd || "#f3f4f6"} 100%)`
				: props.backgroundColor || "linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%)";
			
			return (
				<div
					style={{
						padding: `${props.padding?.top || 60}px ${props.padding?.right || 40}px ${props.padding?.bottom || 60}px ${props.padding?.left || 40}px`,
						background: bgStyle,
						borderRadius: `${props.borderRadius || 16}px`,
						maxWidth: (props.maxWidth as string) || "100%",
						minHeight: (props.height as string) || "auto",
					}}
				>
					<div style={{ maxWidth: "500px", margin: "0 auto" }}>
						<div 
							onClick={(e) => {
								e.stopPropagation();
								// Inline editing - no popup needed
							}}
							style={{ 
								textAlign: (props.textAlign as "left" | "center" | "right") || "center", 
								marginBottom: "40px",
								cursor: "pointer",
								padding: "8px",
								borderRadius: "8px",
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor = "transparent";
							}}
						>
							<h2
								data-element-content
								onClick={(e) => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = { x: rect.left + rect.width / 2, y: rect.top };
									if (onElementClick) {
										// Create unique element identifier for form title
										const uniqueElement = {
											...element,
											id: `${element.id}-form-title`,
											elementType: 'form-title',
											textPath: 'form-title'
										};
										onElementClick(uniqueElement, position);
									}
								}}
								style={{
									fontSize: (element.properties.formTitleFontSize as string) || (element.properties.fontSize as string) || "32px",
									fontWeight: (element.properties.formTitleFontWeight as string) || (element.properties.fontWeight as string) || "700",
									fontStyle: (element.properties.formTitleFontStyle as string) || (element.properties.fontStyle as string) || "normal",
									textDecoration: (element.properties.formTitleTextDecoration as string) || (element.properties.textDecoration as string) || "none",
									fontFamily: (element.properties.formTitleFontFamily as string) || (element.properties.fontFamily as string) || "Inter",
									color: (element.properties.formTitleColor as string) || (element.properties.color as string) || props.textColor || "#111827",
									textAlign: (element.properties.formTitleTextAlign as "left" | "center" | "right") || (element.properties.textAlign as "left" | "center" | "right") || "center",
									marginBottom: "12px",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== element.content) {
										onUpdate({ content: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = element.content || "Get Started Today";
										e.currentTarget.blur();
									}
								}}
							>
								{typeof element.content === 'string' ? element.content : (element.content as any)?.title || "Get Started Today"}
							</h2>
							<p 
								onClick={(e) => {
									e.stopPropagation();
									// Inline editing - no popup needed
								}}
								style={{ 
									fontSize: "16px", 
									color: props.textColor ? `${props.textColor}cc` : "#6b7280",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
								contentEditable
								suppressContentEditableWarning
								onBlur={(e) => {
									const newText = e.currentTarget.textContent || "";
									if (newText !== (props.subtitle as string)) {
										onUpdate({ subtitle: newText });
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										e.currentTarget.blur();
									}
									if (e.key === "Escape") {
										e.currentTarget.textContent = (props.subtitle as string) || "Join thousands of satisfied customers";
										e.currentTarget.blur();
									}
								}}
							>
								{(props.subtitle as string) || "Join thousands of satisfied customers"}
							</p>
						</div>
						<div
							onClick={(e) => {
								e.stopPropagation();
								// Inline editing - no popup needed
							}}
							style={{
								background: "white",
								padding: "40px",
								borderRadius: "16px",
								boxShadow: "0 10px 40px rgba(0, 0, 0, 0.1)",
								cursor: "pointer",
								transition: "all 0.2s ease",
								border: "2px solid transparent"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.borderColor = "rgba(59, 130, 246, 0.3)";
								e.currentTarget.style.transform = "translateY(-2px)";
								e.currentTarget.style.boxShadow = "0 15px 50px rgba(0, 0, 0, 0.15)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.borderColor = "transparent";
								e.currentTarget.style.transform = "translateY(0)";
								e.currentTarget.style.boxShadow = "0 10px 40px rgba(0, 0, 0, 0.1)";
							}}
						>
							<div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
								<div
									onClick={(e) => {
										e.stopPropagation();
										// Inline editing - no popup needed
									}}
									style={{
										cursor: "pointer",
										padding: "8px",
										borderRadius: "6px",
										transition: "background-color 0.2s ease"
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = "transparent";
									}}
								>
									<label
										onClick={(e) => {
											e.stopPropagation();
											// Inline editing - no popup needed
										}}
										style={{
											display: "block",
											fontSize: "14px",
											fontWeight: "600",
											color: "#374151",
											marginBottom: "8px",
											cursor: "pointer",
											padding: "4px",
											borderRadius: "4px",
											transition: "background-color 0.2s ease"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "transparent";
										}}
										contentEditable
										suppressContentEditableWarning
										onBlur={(e) => {
											const newText = e.currentTarget.textContent || "";
											if (newText !== "Full Name") {
												onUpdate({ nameLabel: newText });
											}
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												e.preventDefault();
												e.currentTarget.blur();
											}
											if (e.key === "Escape") {
												e.currentTarget.textContent = "Full Name";
												e.currentTarget.blur();
											}
										}}
									>
										{(props.nameLabel as string) || "Full Name"}
									</label>
									<input
										type="text"
										placeholder="John Doe"
										style={{
											width: "100%",
											padding: "12px 16px",
											border: "2px solid #e5e7eb",
											borderRadius: "10px",
											fontSize: "15px",
											outline: "none",
										}}
									/>
								</div>
								<div
									onClick={(e) => {
										e.stopPropagation();
										// Inline editing - no popup needed
									}}
									style={{
										cursor: "pointer",
										padding: "8px",
										borderRadius: "6px",
										transition: "background-color 0.2s ease"
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = "transparent";
									}}
								>
									<label
										onClick={(e) => {
											e.stopPropagation();
											// Inline editing - no popup needed
										}}
										style={{
											display: "block",
											fontSize: "14px",
											fontWeight: "600",
											color: "#374151",
											marginBottom: "8px",
											cursor: "pointer",
											padding: "4px",
											borderRadius: "4px",
											transition: "background-color 0.2s ease"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "transparent";
										}}
										contentEditable
										suppressContentEditableWarning
										onBlur={(e) => {
											const newText = e.currentTarget.textContent || "";
											if (newText !== "Email Address") {
												onUpdate({ emailLabel: newText });
											}
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												e.preventDefault();
												e.currentTarget.blur();
											}
											if (e.key === "Escape") {
												e.currentTarget.textContent = "Email Address";
												e.currentTarget.blur();
											}
										}}
									>
										{(props.emailLabel as string) || "Email Address"}
									</label>
									<input
										type="email"
										placeholder="<EMAIL>"
										style={{
											width: "100%",
											padding: "12px 16px",
											border: "2px solid #e5e7eb",
											borderRadius: "10px",
											fontSize: "15px",
											outline: "none",
										}}
									/>
								</div>
								<div
									onClick={(e) => {
										e.stopPropagation();
										// Inline editing - no popup needed
									}}
									style={{
										cursor: "pointer",
										padding: "8px",
										borderRadius: "6px",
										transition: "background-color 0.2s ease"
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = "transparent";
									}}
								>
									<label
										onClick={(e) => {
											e.stopPropagation();
											// Inline editing - no popup needed
										}}
										style={{
											display: "block",
											fontSize: "14px",
											fontWeight: "600",
											color: "#374151",
											marginBottom: "8px",
											cursor: "pointer",
											padding: "4px",
											borderRadius: "4px",
											transition: "background-color 0.2s ease"
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = "transparent";
										}}
										contentEditable
										suppressContentEditableWarning
										onBlur={(e) => {
											const newText = e.currentTarget.textContent || "";
											if (newText !== "Company Name (Optional)") {
												onUpdate({ companyLabel: newText });
											}
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												e.preventDefault();
												e.currentTarget.blur();
											}
											if (e.key === "Escape") {
												e.currentTarget.textContent = "Company Name (Optional)";
												e.currentTarget.blur();
											}
										}}
									>
										{(props.companyLabel as string) || "Company Name (Optional)"}
									</label>
									<input
										type="text"
										placeholder="Your Company"
										style={{
											width: "100%",
											padding: "12px 16px",
											border: "2px solid #e5e7eb",
											borderRadius: "10px",
											fontSize: "15px",
											outline: "none",
										}}
									/>
								</div>
								<button
									type="button"
									onClick={(e) => {
										e.stopPropagation();
										// Inline editing - no popup needed
									}}
									style={{
										width: "100%",
										padding: "16px",
										background: "linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)",
										color: "white",
										fontSize: "16px",
										fontWeight: "600",
										borderRadius: "10px",
										border: "none",
										cursor: "pointer",
										marginTop: "8px",
										boxShadow: "0 4px 14px rgba(59, 130, 246, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
										transition: "all 0.2s ease",
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.transform = "translateY(-1px)";
										e.currentTarget.style.boxShadow = "0 6px 20px rgba(59, 130, 246, 0.35), inset 0 2px 4px rgba(255, 255, 255, 0.3)";
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.transform = "translateY(0)";
										e.currentTarget.style.boxShadow = "0 4px 14px rgba(59, 130, 246, 0.25), inset 0 2px 4px rgba(255, 255, 255, 0.3)";
									}}
									contentEditable
									suppressContentEditableWarning
									onBlur={(e) => {
										const newText = e.currentTarget.textContent || "";
										if (newText !== (props.submitButtonText as string)) {
											onUpdate({ submitButtonText: newText });
										}
									}}
									onKeyDown={(e) => {
										if (e.key === "Enter") {
											e.preventDefault();
											e.currentTarget.blur();
										}
										if (e.key === "Escape") {
											e.currentTarget.textContent = (props.submitButtonText as string) || "Get Started Free";
											e.currentTarget.blur();
										}
									}}
								>
									{(props.submitButtonText as string) || "Get Started Free"}
								</button>
								<p 
									onClick={(e) => {
										e.stopPropagation();
										// Inline editing - no popup needed
									}}
									style={{ 
										fontSize: "12px", 
										color: "#9ca3af", 
										textAlign: "center", 
										marginTop: "8px",
										cursor: "pointer",
										padding: "8px",
										borderRadius: "6px",
										transition: "background-color 0.2s ease"
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = "transparent";
									}}
								>
									By signing up, you agree to our Terms of Service and Privacy Policy
								</p>
							</div>
						</div>
					</div>
				</div>
			);
		}

		if (element.type === "footer") {
			const props = element.properties;
			const bgStyle = props.useGradient
				? `linear-gradient(135deg, ${props.gradientStart || "#f8fafc"} 0%, ${props.gradientEnd || "#f1f5f9"} 100%)`
				: props.backgroundColor || "#ffffff";
			
			return (
				<div
					onClick={(e) => {
						e.stopPropagation();
						// Inline editing - no popup needed
					}}
					style={{
						padding: `${props.padding?.top || 40}px ${props.padding?.right || 40}px ${props.padding?.bottom || 40}px ${props.padding?.left || 40}px`,
						background: bgStyle,
						color: props.textColor || "#1f2937",
						width: "100%",
						minHeight: (props.height as string) || "auto",
						cursor: "pointer",
						transition: "all 0.2s ease",
						borderRadius: "12px",
						margin: "20px 0"
					}}
					onMouseEnter={(e) => {
						e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
						e.currentTarget.style.transform = "translateY(-2px)";
						e.currentTarget.style.boxShadow = "0 8px 25px rgba(0, 0, 0, 0.1)";
					}}
					onMouseLeave={(e) => {
						e.currentTarget.style.background = bgStyle;
						e.currentTarget.style.transform = "translateY(0)";
						e.currentTarget.style.boxShadow = "none";
					}}
				>
					<div
						style={{
							display: "grid",
							gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
							gap: "40px",
							marginBottom: "40px",
						}}
					>
						<div>
							<h3
								data-element-content
								onClick={(e) => {
									e.stopPropagation();
									// Trigger tooltip for this element
									const rect = e.currentTarget.getBoundingClientRect();
									const position = { x: rect.left + rect.width / 2, y: rect.top };
									if (onElementClick) {
										// Create unique element identifier for footer title
										const uniqueElement = {
											...element,
											id: `${element.id}-footer-title`,
											elementType: 'footer-title',
											textPath: 'footer-title'
										};
										onElementClick(uniqueElement, position);
									}
								}}
								style={{
									fontSize: (element.properties.footerTitleFontSize as string) || (element.properties.fontSize as string) || "18px",
									fontWeight: (element.properties.footerTitleFontWeight as string) || (element.properties.fontWeight as string) || "600",
									fontStyle: (element.properties.footerTitleFontStyle as string) || (element.properties.fontStyle as string) || "normal",
									textDecoration: (element.properties.footerTitleTextDecoration as string) || (element.properties.textDecoration as string) || "none",
									fontFamily: (element.properties.footerTitleFontFamily as string) || (element.properties.fontFamily as string) || "Inter",
									color: (element.properties.footerTitleColor as string) || (element.properties.color as string) || "#1f2937",
									textAlign: (element.properties.footerTitleTextAlign as "left" | "center" | "right") || (element.properties.textAlign as "left" | "center" | "right") || "left",
									marginBottom: "12px",
									cursor: "pointer",
									padding: "8px",
									borderRadius: "8px",
									transition: "background-color 0.2s ease"
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.05)";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = "transparent";
								}}
							>
								FunnelFlow AI
							</h3>
							<p style={{ fontSize: "14px", color: "#6b7280", lineHeight: "1.5" }}>
								Build beautiful sales funnels in minutes with the power of AI.
							</p>
						</div>
						<div>
							<h4 style={{ fontSize: "14px", fontWeight: "600", marginBottom: "16px", color: "#374151" }}>
								Product
							</h4>
							<ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
								{["Features", "Pricing", "Templates", "Integrations"].map((item, idx) => (
									<li key={idx} style={{ marginBottom: "12px" }}>
										<a
											href="#"
											style={{
												color: "#6b7280",
												textDecoration: "none",
												fontSize: "14px",
												transition: "color 0.2s",
											}}
										>
											{item}
										</a>
									</li>
								))}
							</ul>
						</div>
						<div>
							<h4 style={{ fontSize: "14px", fontWeight: "600", marginBottom: "16px", color: "#e5e7eb" }}>
								Company
							</h4>
							<ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
								{["About", "Blog", "Careers", "Contact"].map((item, idx) => (
									<li key={idx} style={{ marginBottom: "12px" }}>
										<a
											href="#"
											style={{
												color: "#9ca3af",
												textDecoration: "none",
												fontSize: "14px",
												transition: "color 0.2s",
											}}
										>
											{item}
										</a>
									</li>
								))}
							</ul>
						</div>
						<div>
							<h4 style={{ fontSize: "14px", fontWeight: "600", marginBottom: "16px", color: "#e5e7eb" }}>
								Legal
							</h4>
							<ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
								{["Privacy", "Terms", "Security", "Cookies"].map((item, idx) => (
									<li key={idx} style={{ marginBottom: "12px" }}>
										<a
											href="#"
											style={{
												color: "#9ca3af",
												textDecoration: "none",
												fontSize: "14px",
												transition: "color 0.2s",
											}}
										>
											{item}
										</a>
									</li>
								))}
							</ul>
						</div>
					</div>
					<div
						style={{
							borderTop: "1px solid #374151",
							paddingTop: "32px",
							display: "flex",
							justifyContent: "space-between",
							alignItems: "center",
							flexWrap: "wrap",
							gap: "20px",
						}}
					>
						<p style={{ fontSize: "14px", color: props.textColor ? `${props.textColor}cc` : "#9ca3af", margin: 0 }}>
							{typeof element.content === 'string' ? element.content : (element.content as any)?.title || "© 2024 FunnelFlow AI. All rights reserved."}
						</p>
						<div style={{ display: "flex", gap: "20px" }}>
							{["Twitter", "LinkedIn", "GitHub"].map((social, idx) => (
								<a
									key={idx}
									href="#"
									style={{
										width: "36px",
										height: "36px",
										borderRadius: "50%",
										background: "#374151",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										color: "#9ca3af",
										textDecoration: "none",
										fontSize: "12px",
										fontWeight: "600",
										transition: "all 0.2s",
									}}
								>
									{social[0]}
								</a>
							))}
						</div>
					</div>
				</div>
			);
		}

		// Handle individual element types
		switch (element.type) {
			case "heading":
				return (
					<div
						data-element-content
						style={{
							margin: 0,
							fontSize: getFontSizeValue(element.properties.fontSize) || "24px",
							fontFamily: (element.properties.fontFamily as string) || "Inter",
							fontWeight: (element.properties.fontWeight as string) || "600",
							fontStyle: (element.properties.fontStyle as string) || "normal",
							textDecoration: (element.properties.textDecoration as string) || "none",
							color: element.properties.textColor || "#111827",
							backgroundColor: element.properties.backgroundColor || "transparent",
							textAlign: (element.properties.textAlign as 'left' | 'center' | 'right' | undefined) || "left",
							display: element.properties.visible === false ? "none" : "block",
							lineHeight: "1.2",
							cursor: "pointer",
						}}
					>
						{isEditing ? (
							<input
								type="text"
								value={editValue}
								onChange={(e) => setEditValue(e.target.value)}
								onBlur={handleBlur}
								onKeyDown={handleKeyDown}
								autoFocus
								style={{
									background: "transparent",
									border: "none",
									outline: "none",
									fontSize: "inherit",
									fontWeight: "inherit",
									width: "100%",
									color: "inherit",
								}}
							/>
						) : (
							element.content || element.properties?.content || element.properties?.text || "Click to edit heading"
						)}
					</div>
				);
			case "text":
				return (
					<div
						data-element-content
						style={{
							margin: 0,
							lineHeight: "1.6",
							fontSize: getFontSizeValue(element.properties.fontSize) || "16px",
							fontFamily: (element.properties.fontFamily as string) || "Inter",
							fontWeight: (element.properties.fontWeight as string) || "400",
							fontStyle: (element.properties.fontStyle as string) || "normal",
							textDecoration: (element.properties.textDecoration as string) || "none",
							color: element.properties.textColor || "#374151",
							backgroundColor: element.properties.backgroundColor || "transparent",
							textAlign: (element.properties.textAlign as 'left' | 'center' | 'right' | undefined) || "left",
							display: element.properties.visible === false ? "none" : "block",
							cursor: "pointer",
						}}
					>
						{isEditing ? (
							<textarea
								value={editValue}
								onChange={(e) => setEditValue(e.target.value)}
								onBlur={handleBlur}
								onKeyDown={handleKeyDown}
								autoFocus
								style={{
									background: "transparent",
									border: "none",
									outline: "none",
									fontSize: "inherit",
									width: "100%",
									minHeight: "60px",
									resize: "none",
									color: "inherit",
								}}
							/>
						) : (
							element.content || element.properties?.content || element.properties?.text || "Click to edit text"
						)}
					</div>
				);
			case "button":
				return (
					<Button 
						size="2" 
						variant="solid" 
						style={{ 
							background: element.properties.backgroundColor || "var(--accent-9)",
							fontSize: getFontSizeValue(element.properties.fontSize),
							color: element.properties.textColor || "white",
							display: element.properties.visible === false ? "none" : undefined,
						}}
					>
						{isEditing ? (
							<input
								type="text"
								value={editValue}
								onChange={(e) => setEditValue(e.target.value)}
								onBlur={handleBlur}
								onKeyDown={handleKeyDown}
								autoFocus
								style={{
									background: "transparent",
									border: "none",
									outline: "none",
									color: "inherit",
									fontSize: "inherit",
									width: "100%",
									textAlign: "center",
								}}
							/>
						) : (
							element.content || element.properties?.content || element.properties?.text || "Click to edit button"
						)}
					</Button>
				);
			case "image":
				return (
					<div
						style={{
							width: "100%",
							height: "200px",
							background: "var(--gray-3)",
							border: "1px solid var(--gray-4)",
							borderRadius: "8px",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							flexDirection: "column",
							gap: "8px",
						}}
					>
						<svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="var(--gray-8)" strokeWidth="2">
							<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
							<circle cx="8.5" cy="8.5" r="1.5" />
							<polyline points="21,15 16,10 5,21" />
						</svg>
						<Text size="1" color="gray">Click to add image</Text>
					</div>
				);
			case "video":
				const videoUrl = element.properties.videoUrl as string;
				return (
					<div
						style={{
							width: "100%",
							maxWidth: "800px",
							margin: "0 auto",
						}}
					>
						{videoUrl ? (
							<div style={{
								position: "relative",
								paddingBottom: "56.25%", // 16:9 aspect ratio
								height: 0,
								borderRadius: "12px",
								overflow: "hidden",
								background: "white",
								boxShadow: "0 8px 25px rgba(0, 0, 0, 0.1)",
							}}>
								<video
									src={videoUrl}
									controls={element.properties.controls !== false}
									autoPlay={element.properties.autoplay === true}
									loop={element.properties.loop === true}
									muted={element.properties.muted === true}
									style={{
										position: "absolute",
										top: 0,
										left: 0,
										width: "100%",
										height: "100%",
										objectFit: "cover"
									}}
								/>
							</div>
						) : (
							<div
								style={{
									width: "100%",
									height: "200px",
									background: "var(--gray-3)",
									border: "1px solid var(--gray-4)",
									borderRadius: "8px",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									flexDirection: "column",
									gap: "8px",
									cursor: "pointer",
								}}
								onClick={(e) => {
									e.stopPropagation();
									// Inline editing - no popup needed
								}}
							>
								<HiPlay style={{ fontSize: "32px", color: "var(--gray-8)" }} />
								<Text size="1" color="gray">Click to add video</Text>
							</div>
						)}
					</div>
				);
			case "discount_banner":
				return (
					<div
						onClick={(e) => {
							e.stopPropagation();
							// Inline editing - no popup needed
						}}
						style={{
							width: "100%",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							padding: "12px 20px",
							background: "linear-gradient(135deg, #1e40af 0%, #1e3a8a 50%, #1e40af 100%)",
							fontSize: "16px",
							fontWeight: "600",
							color: "#ffffff",
							boxShadow: "0 4px 20px rgba(30, 64, 175, 0.3)",
							position: "relative",
							overflow: "hidden",
							borderRadius: "8px",
							marginBottom: "20px",
							cursor: "pointer",
							transition: "all 0.2s ease"
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.transform = "translateY(-1px)";
							e.currentTarget.style.boxShadow = "0 6px 25px rgba(30, 64, 175, 0.4)";
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.transform = "translateY(0)";
							e.currentTarget.style.boxShadow = "0 4px 20px rgba(30, 64, 175, 0.3)";
						}}
					>
						{/* Animated background shimmer */}
						<div
							style={{
								position: "absolute",
								top: 0,
								left: "-100%",
								width: "100%",
								height: "100%",
								background: "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)",
								animation: "shimmer 3s ease-in-out infinite",
							}}
						/>
						<span style={{ position: "relative", zIndex: 1 }}>
							{element.content || "🎉 Limited Time: Get 20% off using code \"FUNNEL\""}
						</span>
					</div>
				);
			default:
				return (
					<div
						style={{
							padding: "16px",
							background: "var(--gray-2)",
							border: "1px solid var(--gray-6)",
							borderRadius: "8px",
							textAlign: "center",
						}}
					>
						<Text size="2" color="gray">
							{element.type} element
						</Text>
					</div>
				);
		}
		
		// Default return for unknown element types
		return (
			<div
				style={{
					padding: "16px",
					background: "var(--gray-2)",
					border: "1px solid var(--gray-6)",
					borderRadius: "8px",
					textAlign: "center",
				}}
			>
				<Text size="2" color="gray">
					{element.type} element
				</Text>
			</div>
		);
	};

	return (
		<div
			style={{
				position: "relative",
				// No margin bottom - let components flow naturally
				cursor: "pointer",
				// Only show selection outline when selected, no borders or padding
				outline: isSelected ? "2px solid #3b82f6" : "none",
				outlineOffset: isSelected ? "2px" : "0",
				boxShadow: isSelected ? "0 0 0 4px rgba(59, 130, 246, 0.2), 0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2)" : "none",
				// Add drop zone styling for sections
				border: element.type === "hero" || element.type === "features" || element.type === "pricing" || element.type === "testimonials" || element.type === "form" || element.type === "footer" ? "2px solid transparent" : "none",
				borderRadius: element.type === "hero" || element.type === "features" || element.type === "pricing" || element.type === "testimonials" || element.type === "form" || element.type === "footer" ? "8px" : "none",
				transition: "all 0.2s ease",
			}}
			onClick={handleClick}
			onDoubleClick={handleDoubleClick}
		>
			{renderElement()}
		</div>
	);
}

export function CenterCanvas({
	elements,
	onSelectElement,
	onUpdateElement,
	onDeleteElement,
	onDuplicateElement,
	selectedElementId,
	onDrop,
	onDragOver,
	// Removed onEditElement - using inline editing instead
	onDropIntoSection,
}: CenterCanvasProps) {

	const [zoom, setZoom] = useState(100);
	const [showTooltip, setShowTooltip] = useState(false);
	const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
	const [selectedElement, setSelectedElement] = useState<any>(null);
	const canvasRef = useRef<HTMLDivElement>(null);

	// Remove canvas drag handlers - let the original drag system work

	const handleZoomIn = () => {
		setZoom(Math.min(zoom + 10, 200));
	};

	const handleZoomOut = () => {
		setZoom(Math.max(zoom - 10, 50));
	};

	const handleElementClick = (element: any, position: { x: number; y: number }, elementType?: string) => {
		console.log("Element clicked in CenterCanvas:", element.id, position, elementType);
		setSelectedElement({ ...element, elementType });
		setTooltipPosition(position);
		setShowTooltip(true);
	};

	const handleTooltipClose = () => {
		setShowTooltip(false);
		setSelectedElement(null);
	};

	return (
		<div
			style={{
				flex: 1,
				display: "flex",
				flexDirection: "column",
				background: "var(--gray-2)",
				overflow: "hidden",
			}}
		>
			{/* Canvas Header */}
			<div
				style={{
					padding: "12px 20px",
					borderBottom: "1px solid var(--gray-6)",
					borderLeft: "1px solid var(--gray-6)",
					background: "var(--color-panel-solid)",
					display: "flex",
					alignItems: "center",
					justifyContent: "space-between",
				}}
			>
				<div className="flex items-center gap-4">
					<Text size="2" weight="medium">
						Live Page Editor
					</Text>
					<Badge size="1" color="blue">
						{elements.length} elements
					</Badge>
				</div>

				<div className="flex items-center gap-2">
					{/* Full Screen Preview Button */}
					<button
						type="button"
						onClick={() => {
							// Create full screen preview with proper styling
							const fullScreenWindow = window.open('', '_blank', 'fullscreen=yes,scrollbars=yes');
							if (fullScreenWindow && canvasRef.current) {
								// Get all computed styles from the current canvas
								const canvasElement = canvasRef.current;
								const allElements = canvasElement.querySelectorAll('*');

								// Build CSS from computed styles
								let cssRules = '';
								allElements.forEach((element, index) => {
									const computedStyle = window.getComputedStyle(element);
									const className = `element-${index}`;
									element.classList.add(className);

									cssRules += `.${className} {`;
									// Copy essential styles
									const importantStyles = [
										'display', 'position', 'top', 'left', 'right', 'bottom',
										'width', 'height', 'margin', 'padding', 'border',
										'background', 'color', 'font-family', 'font-size', 'font-weight',
										'text-align', 'line-height', 'border-radius', 'box-shadow',
										'transform', 'z-index', 'overflow', 'flex-direction',
										'justify-content', 'align-items', 'gap', 'flex', 'grid-template-columns'
									];

									importantStyles.forEach(prop => {
										const value = computedStyle.getPropertyValue(prop);
										if (value && value !== 'auto' && value !== 'normal') {
											cssRules += `${prop}: ${value}; `;
										}
									});
									cssRules += '}\n';
								});

								fullScreenWindow.document.write(`
									<!DOCTYPE html>
									<html>
									<head>
										<title>Full Screen Preview</title>
										<meta charset="utf-8">
										<meta name="viewport" content="width=device-width, initial-scale=1">
										<style>
											* { margin: 0; padding: 0; box-sizing: border-box; }
											body {
												font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
												background: white;
												min-height: 100vh;
											}
											@keyframes pulse {
												0%, 100% { transform: scale(1); }
												50% { transform: scale(1.05); }
											}
											@keyframes shimmer {
												0% { left: -100%; }
												100% { left: 100%; }
											}
											${cssRules}
										</style>
									</head>
									<body>
										${canvasElement.innerHTML}
									</body>
									</html>
								`);
								fullScreenWindow.document.close();

								// Clean up temporary classes
								allElements.forEach((element, index) => {
									element.classList.remove(`element-${index}`);
								});
							}
						}}
						style={{
							padding: "6px 12px",
							borderRadius: "6px",
							background: "var(--gray-2)",
							border: "1px solid var(--gray-6)",
							cursor: "pointer",
							fontSize: "12px",
							color: "var(--gray-11)",
							display: "flex",
							alignItems: "center",
							gap: "6px",
						}}
					>
						<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
							<path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
						</svg>
						Full Screen
					</button>



					{/* Zoom Controls */}
					<div className="flex items-center gap-1">
						<button
							type="button"
							onClick={handleZoomOut}
							style={{
								width: "28px",
								height: "28px",
								borderRadius: "4px",
								background: "var(--gray-2)",
								border: "1px solid var(--gray-6)",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								cursor: "pointer",
							}}
						>
							<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="var(--gray-11)" strokeWidth="2">
								<circle cx="11" cy="11" r="8" />
								<path d="M21 21l-4.35-4.35" />
								<path d="M8 11h6" />
							</svg>
						</button>
						<Text size="1" style={{ minWidth: "40px", textAlign: "center" }}>
							{zoom}%
						</Text>
						<button
							type="button"
							onClick={handleZoomIn}
							style={{
								width: "28px",
								height: "28px",
								borderRadius: "4px",
								background: "var(--gray-2)",
								border: "1px solid var(--gray-6)",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								cursor: "pointer",
							}}
						>
							<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="var(--gray-11)" strokeWidth="2">
								<circle cx="11" cy="11" r="8" />
								<path d="M21 21l-4.35-4.35" />
								<path d="M11 8v6" />
								<path d="M8 11h6" />
							</svg>
						</button>
					</div>
				</div>
			</div>

			{/* Canvas Area - Full Website View */}
			<div
				style={{
					flex: 1,
					overflow: "auto",
					background: "#f1f5f9", // Light editor background
					borderLeft: "1px solid var(--gray-6)", // Clear separator between elements panel and website preview
				}}
			>
				<div
					ref={canvasRef}
					onDrop={onDrop}
					onDragOver={onDragOver}
					style={{
						width: "100%",
						minHeight: "100vh",
						background: "white", // Pure white website background
						// No border radius, padding, or box shadow - natural website appearance
						transform: `scale(${zoom / 100})`,
						transformOrigin: "top center",
						transition: "transform 0.2s ease",
					}}
				>
					{/* CSS Animations for promotional banner */}
					<style jsx>{`
						@keyframes pulse {
							0%, 100% { transform: scale(1); }
							50% { transform: scale(1.05); }
						}
						@keyframes shimmer {
							0% { left: -100%; }
							100% { left: 100%; }
						}
					`}</style>
					
					{/* Promotional Banner - Full width at top of page (only show if hero exists) */}
					{elements.some(el => el.type === "hero") && (
						<div
							data-promo-banner
							style={{
								width: "100%",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								padding: "12px 20px",
								background: "linear-gradient(135deg, #1e40af 0%, #1e3a8a 50%, #1e40af 100%)",
								fontSize: "16px",
								fontWeight: "600",
								color: "#ffffff",
								boxShadow: "0 4px 20px rgba(30, 64, 175, 0.3)",
								position: "relative",
								overflow: "hidden",
								zIndex: 1000,
							}}
						>
							{/* Animated background shimmer */}
							<div
								style={{
									position: "absolute",
									top: 0,
									left: "-100%",
									width: "100%",
									height: "100%",
									background: "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)",
									animation: "shimmer 3s ease-in-out infinite",
								}}
							/>
							<span style={{ position: "relative", zIndex: 1 }}>
								{(elements.find(el => el.type === "hero")?.properties.promoBannerText as string) || "🎉 Limited Time: Get 20% off using code \"FUNNEL\""}
							</span>
						</div>
					)}
					{elements.length === 0 ? (
						<div
							style={{
								minHeight: "400px",
								display: "flex",
								flexDirection: "column",
								alignItems: "center",
								justifyContent: "center",
								border: "1px solid var(--gray-4)",
								borderRadius: "12px",
								background: "var(--gray-1)",
								textAlign: "center",
								padding: "40px",
							}}
						>
							<svg
								width="64"
								height="64"
								viewBox="0 0 24 24"
								fill="none"
								stroke="var(--gray-8)"
								strokeWidth="1.5"
								style={{ marginBottom: "16px" }}
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									d="M12 4v16m8-8H4"
								/>
							</svg>
							<Heading size="4" style={{ marginBottom: "8px" }}>
								Start building your page
							</Heading>
							<Text size="2" color="gray" style={{ marginBottom: "16px" }}>
								Drag sections or elements from the sidebar to get started
							</Text>
							<Button size="2" variant="soft">
								Add your first element
							</Button>
						</div>
					) : (
						<div>
							{elements.map((element, index) => (
								<ElementWrapper
									key={`${element.id}-${index}`}
									element={element}
									isSelected={selectedElementId === element.id}
									onSelect={() => onSelectElement?.(element.id)}
									onUpdate={(properties) => onUpdateElement?.(element.id, properties)}
									onDelete={() => onDeleteElement?.(element.id)}
									onDuplicate={() => onDuplicateElement?.(element.id)}
									// Removed onEditElement - using inline editing instead
									onDropIntoSection={onDropIntoSection}
									onElementClick={handleElementClick}
								/>
							))}
						</div>
					)}

					{/* Helper function to get property key based on element type */}
					{(() => {
						const getPropertyKey = (baseProperty: string) => {
							switch (selectedElement?.elementType) {
								case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'social-proof-title': return `socialProofTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'form-title': return `formTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'footer-title': return `footerTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								// Pricing card elements
								case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								// Testimonial elements
								case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								// FAQ elements
								case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
								default: return baseProperty;
							}
						};

						const getCurrentValue = (baseProperty: string, fallback: any = '') => {
							const specificKey = getPropertyKey(baseProperty);
							return selectedElement?.properties?.[specificKey] || selectedElement?.properties?.[baseProperty] || fallback;
						};

						return null; // This is just for the helper functions
					})()}

					{/* SmallElementTooltip Overlay */}
					{showTooltip && selectedElement && (
						<SmallElementTooltip
							position={tooltipPosition}
							visible={showTooltip}
							isBold={(() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const specificKey = getPropertyKey('fontWeight');
								return (selectedElement.properties?.[specificKey] || selectedElement.properties?.fontWeight) === "bold";
							})()}
							isItalic={(() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const specificKey = getPropertyKey('fontStyle');
								return (selectedElement.properties?.[specificKey] || selectedElement.properties?.fontStyle) === "italic";
							})()}
							isUnderline={(() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const specificKey = getPropertyKey('textDecoration');
								return (selectedElement.properties?.[specificKey] || selectedElement.properties?.textDecoration) === "underline";
							})()}
							textAlign={(() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const specificKey = getPropertyKey('textAlign');
								return (selectedElement.properties?.[specificKey] || selectedElement.properties?.textAlign) as string || "center";
							})()}
							fontSize={(() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const specificKey = getPropertyKey('fontSize');
								const defaultSize = selectedElement.elementType === 'headline' ? "48px" :
									selectedElement.elementType === 'subheadline' ? "18px" :
									selectedElement.elementType === 'testimonials-title' ? "42px" :
									selectedElement.elementType === 'social-proof-title' ? "32px" :
									selectedElement.elementType === 'form-title' ? "32px" :
									selectedElement.elementType === 'footer-title' ? "18px" :
									selectedElement.elementType === 'faq-title' ? "42px" :
									selectedElement.elementType === 'plan-title' ? "24px" :
									selectedElement.elementType === 'plan-price' ? "32px" :
									selectedElement.elementType === 'plan-feature' ? "16px" :
									selectedElement.elementType === 'testimonial-quote' ? "18px" :
									selectedElement.elementType === 'testimonial-author' ? "16px" :
									selectedElement.elementType === 'testimonial-role' ? "14px" :
									selectedElement.elementType === 'faq-question' ? "18px" :
									selectedElement.elementType === 'faq-answer' ? "16px" : "16px";
								return (selectedElement.properties?.[specificKey] || selectedElement.properties?.fontSize) as string || defaultSize;
							})()}
							fontFamily={(() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const specificKey = getPropertyKey('fontFamily');
								return (selectedElement.properties?.[specificKey] || selectedElement.properties?.fontFamily) as string || "Inter";
							})()}
							textContent={selectedElement.content || ""}
							textColor={(() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const specificKey = getPropertyKey('color');
								return (selectedElement.properties?.[specificKey] || selectedElement.properties?.color || selectedElement.properties?.textColor) as string || "#000000";
							})()}
							onBold={() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const propertyKey = getPropertyKey('fontWeight');
								const currentWeight = selectedElement.properties?.[propertyKey] || selectedElement.properties?.fontWeight;
								const newWeight = currentWeight === "bold" ? "normal" : "bold";
								const updatedProperties = { ...selectedElement.properties, [propertyKey]: newWeight };
								const updatedElement = { ...selectedElement, properties: updatedProperties };
								setSelectedElement(updatedElement);
								// Use the original element ID (without the suffix)
								const originalId = selectedElement.id.replace(/-headline|-subheadline|-pricing-title|-features-title|-features-description|-testimonials-title|-faq-title|-plan-title-\d+|-plan-price-\d+|-plan-feature-\d+|-testimonial-quote-\d+|-testimonial-author-\d+|-testimonial-role-\d+|-faq-question-\d+|-faq-answer-\d+/, '');
								onUpdateElement?.(originalId, { [propertyKey]: newWeight });
							}}
							onItalic={() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const propertyKey = getPropertyKey('fontStyle');
								const currentStyle = selectedElement.properties?.[propertyKey] || selectedElement.properties?.fontStyle;
								const newStyle = currentStyle === "italic" ? "normal" : "italic";
								const updatedProperties = { ...selectedElement.properties, [propertyKey]: newStyle };
								const updatedElement = { ...selectedElement, properties: updatedProperties };
								setSelectedElement(updatedElement);
								const originalId = selectedElement.id.replace(/-headline|-subheadline|-pricing-title|-features-title|-features-description|-testimonials-title|-faq-title|-plan-title-\d+|-plan-price-\d+|-plan-feature-\d+|-testimonial-quote-\d+|-testimonial-author-\d+|-testimonial-role-\d+|-faq-question-\d+|-faq-answer-\d+/, '');
								onUpdateElement?.(originalId, { [propertyKey]: newStyle });
							}}
							onUnderline={() => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const propertyKey = getPropertyKey('textDecoration');
								const currentDecoration = selectedElement.properties?.[propertyKey] || selectedElement.properties?.textDecoration;
								const newDecoration = currentDecoration === "underline" ? "none" : "underline";
								const updatedProperties = { ...selectedElement.properties, [propertyKey]: newDecoration };
								const updatedElement = { ...selectedElement, properties: updatedProperties };
								setSelectedElement(updatedElement);
								const originalId = selectedElement.id.replace(/-headline|-subheadline|-pricing-title|-features-title|-features-description|-testimonials-title|-faq-title|-plan-title-\d+|-plan-price-\d+|-plan-feature-\d+|-testimonial-quote-\d+|-testimonial-author-\d+|-testimonial-role-\d+|-faq-question-\d+|-faq-answer-\d+/, '');
								onUpdateElement?.(originalId, { [propertyKey]: newDecoration });
							}}
							onTextAlign={(align) => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const propertyKey = getPropertyKey('textAlign');
								const updatedProperties = { ...selectedElement.properties, [propertyKey]: align };
								const updatedElement = { ...selectedElement, properties: updatedProperties };
								setSelectedElement(updatedElement);
								const originalId = selectedElement.id.replace(/-headline|-subheadline|-pricing-title|-features-title|-features-description|-testimonials-title|-faq-title|-plan-title-\d+|-plan-price-\d+|-plan-feature-\d+|-testimonial-quote-\d+|-testimonial-author-\d+|-testimonial-role-\d+|-faq-question-\d+|-faq-answer-\d+/, '');
								onUpdateElement?.(originalId, { [propertyKey]: align });
							}}
							onFontSize={(size) => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const propertyKey = getPropertyKey('fontSize');
								const updatedProperties = { ...selectedElement.properties, [propertyKey]: size };
								const updatedElement = { ...selectedElement, properties: updatedProperties };
								setSelectedElement(updatedElement);
								const originalId = selectedElement.id.replace(/-headline|-subheadline|-pricing-title|-features-title|-features-description|-testimonials-title|-faq-title|-plan-title-\d+|-plan-price-\d+|-plan-feature-\d+|-testimonial-quote-\d+|-testimonial-author-\d+|-testimonial-role-\d+|-faq-question-\d+|-faq-answer-\d+/, '');
								onUpdateElement?.(originalId, { [propertyKey]: size });
							}}
							onFontFamily={(family) => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const propertyKey = getPropertyKey('fontFamily');
								const updatedProperties = { ...selectedElement.properties, [propertyKey]: family };
								const updatedElement = { ...selectedElement, properties: updatedProperties };
								setSelectedElement(updatedElement);
								const originalId = selectedElement.id.replace(/-headline|-subheadline|-pricing-title|-features-title|-features-description|-testimonials-title|-faq-title|-plan-title-\d+|-plan-price-\d+|-plan-feature-\d+|-testimonial-quote-\d+|-testimonial-author-\d+|-testimonial-role-\d+|-faq-question-\d+|-faq-answer-\d+/, '');
								onUpdateElement?.(originalId, { [propertyKey]: family });
							}}
							onTextContent={(content) => {
								// Update both content and selectedElement state
								const updatedElement = { ...selectedElement, content };
								setSelectedElement(updatedElement);
								
								// Update the correct property based on element type
								if (selectedElement.elementType === 'headline') {
									onUpdateElement?.(selectedElement.id, { content });
								} else if (selectedElement.elementType === 'subtext') {
									onUpdateElement?.(selectedElement.id, { subtitle: content });
								} else if (selectedElement.elementType === 'social_proof_title') {
									onUpdateElement?.(selectedElement.id, { content });
								} else if (selectedElement.elementType === 'social_proof_subtitle') {
									onUpdateElement?.(selectedElement.id, { subtitle: content });
								} else if (selectedElement.elementType === 'problem_title') {
									onUpdateElement?.(selectedElement.id, { content });
								} else if (selectedElement.elementType === 'solution_title') {
									onUpdateElement?.(selectedElement.id, { content });
								} else if (selectedElement.elementType === 'features_title') {
									onUpdateElement?.(selectedElement.id, { content });
								} else if (selectedElement.elementType === 'pricing_title') {
									onUpdateElement?.(selectedElement.id, { content });
								} else if (selectedElement.elementType === 'testimonials_title') {
									onUpdateElement?.(selectedElement.id, { content });
								} else if (selectedElement.elementType === 'faq_title') {
									onUpdateElement?.(selectedElement.id, { content });
								} else if (selectedElement.elementType === 'form_title') {
									onUpdateElement?.(selectedElement.id, { content });
								} else if (selectedElement.elementType === 'footer_title') {
									onUpdateElement?.(selectedElement.id, { content });
								} else {
									// Fallback for other element types
									onUpdateElement?.(selectedElement.id, { content, text: content });
								}
							}}
							onTextColor={(color) => {
								const getPropertyKey = (baseProperty: string) => {
									switch (selectedElement?.elementType) {
										case 'headline': return `headline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'subheadline': return `subheadline${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'pricing-title': return `pricingTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-title': return `featuresTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'features-description': return `featuresDescription${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonials-title': return `testimonialsTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-title': return `faqTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-title': return `planTitle${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-price': return `planPrice${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'plan-feature': return `planFeature${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-quote': return `testimonialQuote${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-author': return `testimonialAuthor${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'testimonial-role': return `testimonialRole${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-question': return `faqQuestion${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										case 'faq-answer': return `faqAnswer${baseProperty.charAt(0).toUpperCase() + baseProperty.slice(1)}`;
										default: return baseProperty;
									}
								};
								const propertyKey = getPropertyKey('color');
								const updatedProperties = { ...selectedElement.properties, [propertyKey]: color };
								const updatedElement = { ...selectedElement, properties: updatedProperties };
								setSelectedElement(updatedElement);
								const originalId = selectedElement.id.replace(/-headline|-subheadline|-pricing-title|-features-title|-features-description|-testimonials-title|-faq-title|-plan-title-\d+|-plan-price-\d+|-plan-feature-\d+|-testimonial-quote-\d+|-testimonial-author-\d+|-testimonial-role-\d+|-faq-question-\d+|-faq-answer-\d+/, '');
								onUpdateElement?.(originalId, { [propertyKey]: color });
							}}
							onAIEdit={() => {
								console.log("AI Edit action");
							}}
							onLink={() => {
								console.log("Link action");
							}}
							onMore={() => {
								console.log("More options");
							}}
							onDelete={() => {
								onDeleteElement?.(selectedElement.id);
								handleTooltipClose();
							}}
							onClose={handleTooltipClose}
						/>
					)}
				</div>
			</div>
		</div>
	);
}
