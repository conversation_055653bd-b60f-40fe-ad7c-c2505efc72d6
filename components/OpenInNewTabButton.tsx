"use client";

import { Button } from "frosted-ui";
import { useState } from "react";

export function OpenInNewTabButton() {
	const [isLoading, setIsLoading] = useState(false);

	const handleOpenInNewTab = async () => {
		try {
			setIsLoading(true);

			// Call the API to generate a session token
			const response = await fetch("/api/session-token", {
				method: "POST",
			});

			if (!response.ok) {
				throw new Error("Failed to generate session token");
			}

			const { token, companyId } = await response.json();

			// Open the dashboard in a new tab with the session token
			const url = `/open?token=${token}&redirect=/dashboard/${companyId}`;
			window.open(url, "_blank");
		} catch (error) {
			console.error("Error opening in new tab:", error);
			alert("Failed to open in new tab. Please try again.");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Button
			size="2"
			variant="soft"
			onClick={handleOpenInNewTab}
			disabled={isLoading}
			style={{ width: "100%" }}
		>
			<span className="flex items-center gap-2">
				<svg
					width="14"
					height="14"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					strokeWidth="2"
					role="img"
					aria-label="External link icon"
				>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
					/>
				</svg>
				{isLoading ? "Opening..." : "Edit in Tab"}
			</span>
		</Button>
	);
}
