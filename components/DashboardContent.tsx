"use client";

import type { FunnelSchema } from "@/lib/types/funnel";
import { Callout } from "frosted-ui";
import { useState } from "react";
import { CreateWithAIModal } from "./CreateWithAIModal";
import { DashboardLayout } from "./DashboardLayout";
import { NewFunnelEditor } from "./NewFunnelEditor";
import { GeneratedSitePreview } from "./GeneratedSitePreview";
import { AnalyticsSection } from "./sections/AnalyticsSection";
import { FunnelsSection } from "./sections/FunnelsSection";
import { LeadsSection } from "./sections/LeadsSection";
import { ProductsSection } from "./sections/ProductsSection";
import { SettingsSection } from "./sections/SettingsSection";
import { TemplatesSection } from "./sections/TemplatesSection";

interface DashboardContentProps {
	userId: string;
	companyId: string;
	userName: string;
	username: string;
	companyTitle: string;
}

type ViewMode =
	| "funnels"
	| "templates"
	| "preview"
	| "edit"
	| "products"
	| "leads"
	| "analytics"
	| "settings";

export function DashboardContent({
	userId,
	companyId,
	userName,
	username,
	companyTitle,
}: DashboardContentProps) {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [currentFunnel, setCurrentFunnel] = useState<FunnelSchema | null>(null);
	const [showSuccess, setShowSuccess] = useState(false);
	const [successMessage, setSuccessMessage] = useState("");
	const [viewMode, setViewMode] = useState<ViewMode>("funnels");
	const [refreshKey, setRefreshKey] = useState(0);

	// Note: Funnel loading is now handled by FunnelsSection via Supabase
	// No need to load from localStorage anymore

	const handleFunnelCreated = async (funnel: FunnelSchema) => {
		try {
			// Save the new funnel to Supabase
			const response = await fetch('/api/funnels', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					companyId,
					userId,
					funnel,
					status: 'draft',
				}),
			});

			const data = await response.json();
			
			if (data.success) {
				// Store the funnel with its ID
				setCurrentFunnel({ ...funnel, id: data.funnel.id } as FunnelSchema & { id: string });
				// Trigger refresh of funnels list
				setRefreshKey(prev => prev + 1);
				setViewMode("preview");
				setSuccessMessage(
					"Funnel generated successfully! Review your site below.",
				);
				setShowSuccess(true);
				setTimeout(() => setShowSuccess(false), 3000);
			} else {
				console.error("Failed to save funnel:", data.error);
				setSuccessMessage("Failed to save funnel. Please try again.");
				setShowSuccess(true);
				setTimeout(() => setShowSuccess(false), 3000);
			}
		} catch (error) {
			console.error("Error saving funnel:", error);
			setSuccessMessage("Failed to save funnel. Please try again.");
			setShowSuccess(true);
			setTimeout(() => setShowSuccess(false), 3000);
		}
	};

	const handleTemplateSelected = async (template: FunnelSchema) => {
		try {
			// Save template as a new draft funnel
			const response = await fetch('/api/funnels', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					companyId,
					userId,
					funnel: template,
					status: 'draft',
				}),
			});

			const data = await response.json();
			
			if (data.success) {
				setCurrentFunnel({ ...template, id: data.funnel.id } as FunnelSchema & { id: string });
				// Trigger refresh of funnels list
				setRefreshKey(prev => prev + 1);
				setViewMode("edit");
				setSuccessMessage("Template loaded! Customize it to match your needs.");
				setShowSuccess(true);
				setTimeout(() => setShowSuccess(false), 3000);
			} else {
				console.error("Failed to save template as funnel:", data.error);
			}
		} catch (error) {
			console.error("Error saving template:", error);
		}
	};

	const handleEditFunnel = (funnel: FunnelSchema) => {
		setCurrentFunnel(funnel);
		setViewMode("edit");
	};

	const handleUpdateFunnel = async (funnel: FunnelSchema & { id?: string }) => {
		try {
			if (funnel.id) {
				// Update existing funnel in Supabase
				const response = await fetch(`/api/funnels/${funnel.id}`, {
					method: 'PUT',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						updates: {
							title: funnel.title,
							description: funnel.description,
							pages: funnel.pages,
							sections: funnel.sections,
							theme: funnel.theme,
							layout: funnel.layout,
						},
					}),
				});

				const data = await response.json();
				
				if (data.success) {
					setCurrentFunnel(funnel);
				} else {
					console.error("Failed to update funnel:", data.error);
				}
			} else {
				// Create new funnel
				const response = await fetch('/api/funnels', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						companyId,
						userId,
						funnel,
						status: 'draft',
					}),
				});

				const data = await response.json();
				
				if (data.success) {
					setCurrentFunnel({ ...funnel, id: data.funnel.id } as FunnelSchema & { id: string });
				} else {
					console.error("Failed to create funnel:", data.error);
				}
			}
		} catch (error) {
			console.error("Error saving funnel:", error);
		}
	};

	const renderContent = () => {
		switch (viewMode) {
			case "funnels":
				return (
					<FunnelsSection
						key={refreshKey}
						companyId={companyId}
						onCreateFunnel={() => setIsModalOpen(true)}
						onEditFunnel={handleEditFunnel}
					/>
				);
			case "templates":
				return <TemplatesSection onSelectTemplate={handleTemplateSelected} />;
			case "products":
				return <ProductsSection />;
			case "leads":
				return <LeadsSection />;
			case "analytics":
				return <AnalyticsSection />;
			case "settings":
				return <SettingsSection />;
			default:
				return null;
		}
	};

	const handleNavigate = (section: string) => {
		setViewMode(section as ViewMode);
		// Clear any existing funnel when navigating to a different section
		if (section !== "edit" && section !== "preview") {
			setCurrentFunnel(null);
		}
		// Refresh funnels list when navigating to funnels section
		if (section === "funnels") {
			setRefreshKey(prev => prev + 1);
		}
	};

	// Use new layout for edit mode and preview mode
	if ((viewMode === "edit" || viewMode === "preview") && currentFunnel) {
		if (viewMode === "preview") {
			return (
				<>
					<GeneratedSitePreview
						funnel={currentFunnel}
						onEdit={() => setViewMode("edit")}
						onCreateNew={() => setIsModalOpen(true)}
						companyId={companyId}
						userName={userName}
						username={username}
						companyTitle={companyTitle}
					/>
					{/* Modal */}
					<CreateWithAIModal
						isOpen={isModalOpen}
						onClose={() => setIsModalOpen(false)}
						onSuccess={handleFunnelCreated}
						userId={userId}
						companyId={companyId}
					/>
				</>
			);
		}

		return (
			<>
				<NewFunnelEditor
					funnel={currentFunnel}
					onSave={handleUpdateFunnel}
					onClose={() => setViewMode("preview")}
				/>
				{/* Modal */}
				<CreateWithAIModal
					isOpen={isModalOpen}
					onClose={() => setIsModalOpen(false)}
					onSuccess={handleFunnelCreated}
					userId={userId}
					companyId={companyId}
				/>
			</>
		);
	}

	return (
		<DashboardLayout
			userName={userName}
			username={username}
			companyTitle={companyTitle}
			onCreateFunnel={() => setIsModalOpen(true)}
			activeSection={viewMode === "edit" || viewMode === "preview" ? "funnels" : viewMode}
			onNavigate={handleNavigate}
		>
			{/* Success Toast */}
			{showSuccess && (
				<div
					className="whop-toast"
					style={{
						position: "fixed",
						top: "24px",
						right: "24px",
						zIndex: 50,
						minWidth: "480px",
						maxWidth: "700px",
					}}
				>
					<Callout.Root
						color="green"
						size="3"
						style={{
							padding: "24px 28px",
							borderRadius: "12px",
							boxShadow: "0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1)",
							border: "1px solid var(--green-6)",
							background: "var(--green-2)",
						}}
					>
						<Callout.Icon>
							<svg
								width="24"
								height="24"
								viewBox="0 0 20 20"
								fill="currentColor"
								role="img"
								aria-label="Success checkmark icon"
							>
								<path
									fillRule="evenodd"
									d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
									clipRule="evenodd"
								/>
							</svg>
						</Callout.Icon>
						<Callout.Text style={{ fontSize: "16px", fontWeight: "500" }}>
							{successMessage}
						</Callout.Text>
					</Callout.Root>
				</div>
			)}

			{/* Main Content */}
			{renderContent()}

			{/* Modal */}
			<CreateWithAIModal
				isOpen={isModalOpen}
				onClose={() => setIsModalOpen(false)}
				onSuccess={handleFunnelCreated}
				userId={userId}
				companyId={companyId}
			/>
		</DashboardLayout>
	);
}
