"use client";

import { useState } from "react";
import { HugeiconsIcon } from "@hugeicons/react";
import { 
	TextBoldIcon,
	TextItalicIcon,
	TextUnderlineIcon,
	TextAlignLeftIcon,
	TextAlignCenterIcon,
	TextAlignRightIcon,
	ResizeIcon,
	AlertIcon,
	BackgroundIcon,
	Link01Icon,
	MoreHorizontalIcon,
	SparklesIcon,
	RefreshIcon,
	TextCheckIcon,
	FlashlightIcon,
	Menu01Icon,
	Plug01Icon,
	AiPhone01Icon,
	TranslateIcon
} from "@hugeicons/core-free-icons";

interface InlineTextToolbarProps {
	onBold?: () => void;
	onItalic?: () => void;
	onUnderline?: () => void;
	onFontSize?: (size: string) => void;
	onTextColor?: (color: string) => void;
	onBackgroundColor?: (color: string) => void;
	onTextAlign?: (align: string) => void;
	onAIEdit?: (action: string) => void;
	onLink?: () => void;
	onMore?: () => void;
	isBold?: boolean;
	isItalic?: boolean;
	isUnderline?: boolean;
	fontSize?: string;
	textColor?: string;
	backgroundColor?: string;
	textAlign?: string;
	position?: { x: number; y: number };
	visible?: boolean;
}

export function InlineTextToolbar({
	onBold,
	onItalic,
	onUnderline,
	onFontSize,
	onTextColor,
	onBackgroundColor,
	onTextAlign,
	onAIEdit,
	onLink,
	onMore,
	isBold = false,
	isItalic = false,
	isUnderline = false,
	fontSize = "Large",
	textColor = "#000000",
	backgroundColor = "#ffffff",
	textAlign = "left",
	position = { x: 0, y: 0 },
	visible = true,
}: InlineTextToolbarProps) {
	const [showAIMenu, setShowAIMenu] = useState(false);
	const [showFontSizeMenu, setShowFontSizeMenu] = useState(false);
	const [showToneMenu, setShowToneMenu] = useState(false);
	const [showTranslateMenu, setShowTranslateMenu] = useState(false);

	const fontSizeOptions = ["Small", "Medium", "Large", "XL"];
	const toneOptions = ["Professional", "Casual", "Friendly", "Formal"];
	const translateOptions = ["English", "Spanish", "French", "German"];

	if (!visible) return null;

	return (
		<div
			style={{
				position: "fixed",
				left: position.x,
				top: position.y - 50,
				background: "white",
				border: "1px solid #e5e7eb",
				borderRadius: "8px",
				boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
				padding: "8px 12px",
				display: "flex",
				alignItems: "center",
				gap: "8px",
				zIndex: 1000,
				fontSize: "14px",
				transform: "translateX(-50%)",
			}}
		>
			{/* AI Edit Button */}
			<div style={{ position: "relative" }}>
				<button
					onClick={() => setShowAIMenu(!showAIMenu)}
					style={{
						background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
						color: "white",
						border: "none",
						borderRadius: "6px",
						padding: "6px 12px",
						cursor: "pointer",
						display: "flex",
						alignItems: "center",
						gap: "4px",
						fontSize: "12px",
						fontWeight: "500",
					}}
				>
					<HugeiconsIcon icon={SparklesIcon} size={14} />
					AI Edit
				</button>
				
				{showAIMenu && (
					<div
						style={{
							position: "absolute",
							top: "100%",
							left: 0,
							background: "white",
							border: "1px solid #e5e7eb",
							borderRadius: "6px",
							boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
							padding: "4px 0",
							minWidth: "160px",
							zIndex: 1001,
						}}
					>
						{[
							{ label: "Rephrase", icon: RefreshIcon },
							{ label: "Fix spelling", icon: TextCheckIcon },
							{ label: "Simplify", icon: FlashlightIcon },
							{ label: "Shorten", icon: Menu01Icon },
							{ label: "Expand", icon: Plug01Icon },
							{ label: "Change Tone", icon: AiPhone01Icon, hasArrow: true, onClick: () => setShowToneMenu(!showToneMenu) },
							{ label: "Translate", icon: TranslateIcon, hasArrow: true, onClick: () => setShowTranslateMenu(!showTranslateMenu) },
						].map((item, index) => (
							<div
								key={index}
								onClick={() => {
									if (item.onClick) {
										item.onClick();
									} else {
										onAIEdit?.(item.label);
										setShowAIMenu(false);
									}
								}}
								style={{
									padding: "8px 12px",
									cursor: "pointer",
									display: "flex",
									alignItems: "center",
									gap: "8px",
									fontSize: "13px",
									background: index === 0 ? "#f3f4f6" : "transparent",
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.background = "#f3f4f6";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.background = index === 0 ? "#f3f4f6" : "transparent";
								}}
							>
								<HugeiconsIcon icon={item.icon} size={14} color="#6b7280" />
								<span>{item.label}</span>
								{item.hasArrow && <span>→</span>}
							</div>
						))}
					</div>
				)}
			</div>

			{/* Font Size Dropdown */}
			<div style={{ position: "relative" }}>
				<button
					onClick={() => setShowFontSizeMenu(!showFontSizeMenu)}
					style={{
						background: "white",
						border: "1px solid #d1d5db",
						borderRadius: "4px",
						padding: "4px 8px",
						cursor: "pointer",
						fontSize: "12px",
						minWidth: "60px",
					}}
				>
					{fontSize}
				</button>
				
				{showFontSizeMenu && (
					<div
						style={{
							position: "absolute",
							top: "100%",
							left: 0,
							background: "white",
							border: "1px solid #e5e7eb",
							borderRadius: "4px",
							boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
							padding: "4px 0",
							minWidth: "80px",
							zIndex: 1001,
						}}
					>
						{fontSizeOptions.map((size) => (
							<div
								key={size}
								onClick={() => {
									onFontSize?.(size);
									setShowFontSizeMenu(false);
								}}
								style={{
									padding: "6px 12px",
									cursor: "pointer",
									fontSize: "12px",
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.background = "#f3f4f6";
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.background = "transparent";
								}}
							>
								{size}
							</div>
						))}
					</div>
				)}
			</div>

			{/* Formatting Buttons */}
			<div style={{ display: "flex", gap: "4px" }}>
				<button
					onClick={onBold}
					style={{
						background: isBold ? "#3b82f6" : "white",
						color: isBold ? "white" : "#374151",
						border: "1px solid #d1d5db",
						borderRadius: "4px",
						padding: "4px 8px",
						cursor: "pointer",
						fontWeight: "bold",
						fontSize: "12px",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
					}}
				>
					<HugeiconsIcon icon={TextBoldIcon} size={14} />
				</button>
				<button
					onClick={onItalic}
					style={{
						background: isItalic ? "#3b82f6" : "white",
						color: isItalic ? "white" : "#374151",
						border: "1px solid #d1d5db",
						borderRadius: "4px",
						padding: "4px 8px",
						cursor: "pointer",
						fontStyle: "italic",
						fontSize: "12px",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
					}}
				>
					<HugeiconsIcon icon={TextItalicIcon} size={14} />
				</button>
				<button
					onClick={onUnderline}
					style={{
						background: isUnderline ? "#3b82f6" : "white",
						color: isUnderline ? "white" : "#374151",
						border: "1px solid #d1d5db",
						borderRadius: "4px",
						padding: "4px 8px",
						cursor: "pointer",
						textDecoration: "underline",
						fontSize: "12px",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
					}}
				>
					<HugeiconsIcon icon={TextUnderlineIcon} size={14} />
				</button>
			</div>

			{/* Text Alignment */}
			<button
				onClick={() => onTextAlign?.(textAlign === "left" ? "center" : textAlign === "center" ? "right" : "left")}
				style={{
					background: "white",
					border: "1px solid #d1d5db",
					borderRadius: "4px",
					padding: "4px 8px",
					cursor: "pointer",
					fontSize: "12px",
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
				}}
			>
				{textAlign === "left" ? <HugeiconsIcon icon={TextAlignLeftIcon} size={14} /> : textAlign === "center" ? <HugeiconsIcon icon={TextAlignCenterIcon} size={14} /> : <HugeiconsIcon icon={TextAlignRightIcon} size={14} />}
			</button>

			{/* Text Color */}
			<div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
				<input
					type="color"
					value={textColor}
					onChange={(e) => onTextColor?.(e.target.value)}
					style={{
						width: "24px",
						height: "24px",
						border: "1px solid #d1d5db",
						borderRadius: "4px",
						cursor: "pointer",
					}}
				/>
			</div>

			{/* Link Button */}
			<button
				onClick={onLink}
				style={{
					background: "white",
					border: "1px solid #d1d5db",
					borderRadius: "4px",
					padding: "4px 8px",
					cursor: "pointer",
					fontSize: "12px",
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
				}}
			>
				<HugeiconsIcon icon={Link01Icon} size={14} />
			</button>

			{/* More Options */}
			<button
				onClick={onMore}
				style={{
					background: "white",
					border: "1px solid #d1d5db",
					borderRadius: "4px",
					padding: "4px 8px",
					cursor: "pointer",
					fontSize: "12px",
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
				}}
			>
				<HugeiconsIcon icon={MoreHorizontalIcon} size={14} />
			</button>
		</div>
	);
}
