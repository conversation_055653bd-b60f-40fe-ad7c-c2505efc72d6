"use client";

import { useState } from "react";

interface FormattingPanelProps {
	onTextColor?: (color: string) => void;
	onBackgroundColor?: (color: string) => void;
	onFontSize?: (size: string) => void;
	onTextAlign?: (align: string) => void;
	onBold?: () => void;
	onItalic?: () => void;
	onUnderline?: () => void;
	textColor?: string;
	backgroundColor?: string;
	fontSize?: string;
	textAlign?: string;
	isBold?: boolean;
	isItalic?: boolean;
	isUnderline?: boolean;
	visible?: boolean;
}

export function FormattingPanel({
	onTextColor,
	onBackgroundColor,
	onFontSize,
	onTextAlign,
	onBold,
	onItalic,
	onUnderline,
	textColor = "#000000",
	backgroundColor = "#ffffff",
	fontSize = "Medium",
	textAlign = "left",
	isBold = false,
	isItalic = false,
	isUnderline = false,
	visible = false,
}: FormattingPanelProps) {
	const [showFontSizeMenu, setShowFontSizeMenu] = useState(false);

	const fontSizeOptions = ["Small", "Medium", "Large", "XL"];

	if (!visible) return null;

	return (
		<div
			style={{
				background: "white",
				border: "1px solid #e5e7eb",
				borderRadius: "8px",
				padding: "16px",
				margin: "8px",
				boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
			}}
		>
			<div style={{ marginBottom: "16px" }}>
				<h3 style={{ fontSize: "14px", fontWeight: "600", margin: "0 0 12px 0", color: "#374151" }}>
					Text Formatting
				</h3>
				
				{/* Font Size */}
				<div style={{ marginBottom: "12px" }}>
					<label style={{ fontSize: "12px", fontWeight: "500", color: "#6b7280", display: "block", marginBottom: "4px" }}>
						Size
					</label>
					<div style={{ position: "relative" }}>
						<button
							onClick={() => setShowFontSizeMenu(!showFontSizeMenu)}
							style={{
								width: "100%",
								background: "white",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								padding: "6px 8px",
								cursor: "pointer",
								fontSize: "12px",
								textAlign: "left",
							}}
						>
							{fontSize}
						</button>
						
						{showFontSizeMenu && (
							<div
								style={{
									position: "absolute",
									top: "100%",
									left: 0,
									right: 0,
									background: "white",
									border: "1px solid #e5e7eb",
									borderRadius: "4px",
									boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
									padding: "4px 0",
									zIndex: 1001,
								}}
							>
								{fontSizeOptions.map((size) => (
									<div
										key={size}
										onClick={() => {
											onFontSize?.(size);
											setShowFontSizeMenu(false);
										}}
										style={{
											padding: "6px 8px",
											cursor: "pointer",
											fontSize: "12px",
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.background = "#f3f4f6";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.background = "transparent";
										}}
									>
										{size}
									</div>
								))}
							</div>
						)}
					</div>
				</div>

				{/* Formatting Buttons */}
				<div style={{ marginBottom: "12px" }}>
					<label style={{ fontSize: "12px", fontWeight: "500", color: "#6b7280", display: "block", marginBottom: "4px" }}>
						Style
					</label>
					<div style={{ display: "flex", gap: "4px" }}>
						<button
							onClick={onBold}
							style={{
								background: isBold ? "#3b82f6" : "white",
								color: isBold ? "white" : "#374151",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								padding: "6px 12px",
								cursor: "pointer",
								fontWeight: "bold",
								fontSize: "12px",
							}}
						>
							B
						</button>
						<button
							onClick={onItalic}
							style={{
								background: isItalic ? "#3b82f6" : "white",
								color: isItalic ? "white" : "#374151",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								padding: "6px 12px",
								cursor: "pointer",
								fontStyle: "italic",
								fontSize: "12px",
							}}
						>
							I
						</button>
						<button
							onClick={onUnderline}
							style={{
								background: isUnderline ? "#3b82f6" : "white",
								color: isUnderline ? "white" : "#374151",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								padding: "6px 12px",
								cursor: "pointer",
								textDecoration: "underline",
								fontSize: "12px",
							}}
						>
							U
						</button>
					</div>
				</div>

				{/* Text Alignment */}
				<div style={{ marginBottom: "12px" }}>
					<label style={{ fontSize: "12px", fontWeight: "500", color: "#6b7280", display: "block", marginBottom: "4px" }}>
						Alignment
					</label>
					<div style={{ display: "flex", gap: "4px" }}>
						{[
							{ align: "left", icon: "⬅" },
							{ align: "center", icon: "↔" },
							{ align: "right", icon: "➡" },
						].map(({ align, icon }) => (
							<button
								key={align}
								onClick={() => onTextAlign?.(align)}
								style={{
									background: textAlign === align ? "#3b82f6" : "white",
									color: textAlign === align ? "white" : "#374151",
									border: "1px solid #d1d5db",
									borderRadius: "4px",
									padding: "6px 12px",
									cursor: "pointer",
									fontSize: "12px",
								}}
							>
								{icon}
							</button>
						))}
					</div>
				</div>

				{/* Colors */}
				<div style={{ display: "flex", gap: "12px" }}>
					<div style={{ flex: 1 }}>
						<label style={{ fontSize: "12px", fontWeight: "500", color: "#6b7280", display: "block", marginBottom: "4px" }}>
							Text Color
						</label>
						<input
							type="color"
							value={textColor}
							onChange={(e) => onTextColor?.(e.target.value)}
							style={{
								width: "100%",
								height: "32px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								cursor: "pointer",
							}}
						/>
					</div>
					<div style={{ flex: 1 }}>
						<label style={{ fontSize: "12px", fontWeight: "500", color: "#6b7280", display: "block", marginBottom: "4px" }}>
							Background
						</label>
						<input
							type="color"
							value={backgroundColor}
							onChange={(e) => onBackgroundColor?.(e.target.value)}
							style={{
								width: "100%",
								height: "32px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								cursor: "pointer",
							}}
						/>
					</div>
				</div>
			</div>
		</div>
	);
}

