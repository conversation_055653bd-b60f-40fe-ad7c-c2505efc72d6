"use client";

import { useState } from "react";
import { HugeiconsIcon } from "@hugeicons/react";
import { 
	TextBoldIcon,
	TextItalicIcon,
	TextUnderlineIcon,
	TextAlignLeftIcon,
	TextAlignCenterIcon,
	TextAlignRightIcon,
	ResizeIcon,
	AlertIcon,
	BackgroundIcon,
	Link01Icon,
	MoreHorizontalIcon,
	SparklesIcon,
	RefreshIcon,
	TextCheckIcon,
	FlashlightIcon,
	Menu01Icon,
	Plug01Icon,
	AiPhone01Icon,
	TranslateIcon
} from "@hugeicons/core-free-icons";

interface FormattingSidebarProps {
	onTextColor?: (color: string) => void;
	onBackgroundColor?: (color: string) => void;
	onFontSize?: (size: string) => void;
	onFontFamily?: (family: string) => void;
	onTextAlign?: (align: string) => void;
	onBold?: () => void;
	onItalic?: () => void;
	onUnderline?: () => void;
	onAIEdit?: (action: string) => void;
	onLink?: () => void;
	onMore?: () => void;
	onTextChange?: (text: string) => void;
	textColor?: string;
	backgroundColor?: string;
	fontSize?: string;
	fontFamily?: string;
	textAlign?: string;
	textContent?: string;
	isBold?: boolean;
	isItalic?: boolean;
	isUnderline?: boolean;
	visible?: boolean;
}

export function FormattingSidebar({
	onTextColor,
	onBackgroundColor,
	onFontSize,
	onFontFamily,
	onTextAlign,
	onBold,
	onItalic,
	onUnderline,
	onAIEdit,
	onLink,
	onMore,
	onTextChange,
	textColor = "#000000",
	backgroundColor = "#ffffff",
	fontSize = "Medium",
	fontFamily = "Inter",
	textAlign = "left",
	textContent = "",
	isBold = false,
	isItalic = false,
	isUnderline = false,
	visible = false,
}: FormattingSidebarProps) {
	const [showAIMenu, setShowAIMenu] = useState(false);
	const [showFontSizeMenu, setShowFontSizeMenu] = useState(false);
	const [showFontFamilyMenu, setShowFontFamilyMenu] = useState(false);
	const [showToneMenu, setShowToneMenu] = useState(false);
	const [showTranslateMenu, setShowTranslateMenu] = useState(false);

	const fontSizeOptions = ["Small", "Medium", "Large", "XL"];
	const fontFamilyOptions = [
		"Inter", "Inter Tight", "Space Grotesk", "Roboto", "Roboto Condensed",
		"Roboto Flex", "Roboto Mono", "Roboto Serif", "Roboto Slab",
		"Open Sans", "Lato", "Montserrat", "Poppins", "Arial", "Helvetica",
		"Georgia", "Times New Roman"
	];
	const toneOptions = ["Professional", "Casual", "Friendly", "Formal"];
	const translateOptions = ["English", "Spanish", "French", "German"];

	if (!visible) return null;

	return (
		<div
			style={{
				width: "100%",
				background: "transparent",
				height: "100%",
				overflowY: "auto",
				display: "flex",
				flexDirection: "column",
			}}
		>
			{/* Content - no header when embedded */}
			<div style={{ padding: "0", flex: 1 }}>
				{/* Text Content Editing */}
				<div style={{ marginBottom: "16px" }}>
					<label style={{
						fontSize: "11px",
						fontWeight: "600",
						color: "#6b7280",
						display: "block",
						marginBottom: "6px",
						textTransform: "uppercase",
						letterSpacing: "0.5px"
					}}>
						Content
					</label>
					<textarea
						value={textContent}
						onChange={(e) => onTextChange?.(e.target.value)}
						placeholder="Enter your text..."
						style={{
							width: "100%",
							minHeight: "80px",
							padding: "8px 12px",
							border: "1px solid #d1d5db",
							borderRadius: "6px",
							fontSize: "12px",
							fontFamily: "inherit",
							resize: "vertical",
							outline: "none",
							transition: "border-color 0.2s ease",
						}}
						onFocus={(e) => {
							e.target.style.borderColor = "#3b82f6";
						}}
						onBlur={(e) => {
							e.target.style.borderColor = "#d1d5db";
						}}
					/>
				</div>

				{/* AI Edit Section */}
				<div style={{ marginBottom: "16px" }}>
					<label style={{
						fontSize: "11px",
						fontWeight: "600",
						color: "#6b7280",
						display: "block",
						marginBottom: "6px",
						textTransform: "uppercase",
						letterSpacing: "0.5px"
					}}>
						AI Tools
					</label>
					<div style={{ position: "relative" }}>
						<button
							onClick={() => setShowAIMenu(!showAIMenu)}
							style={{
								width: "100%",
								background: "#3b82f6",
								color: "white",
								border: "none",
								borderRadius: "4px",
								padding: "6px 10px",
								cursor: "pointer",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								gap: "4px",
								fontSize: "11px",
								fontWeight: "500",
								transition: "all 0.2s ease",
								boxShadow: "inset 0 1px 0 rgba(255, 255, 255, 0.1)",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.background = "#2563eb";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.background = "#3b82f6";
							}}
						>
							<HugeiconsIcon icon={SparklesIcon} size={12} />
							AI MODE
						</button>
						
						{showAIMenu && (
							<div
								style={{
									position: "absolute",
									top: "100%",
									left: 0,
									right: 0,
									background: "white",
									border: "1px solid #e5e7eb",
									borderRadius: "8px",
									boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
									padding: "8px 0",
									zIndex: 1001,
									marginTop: "4px",
								}}
							>
								{[
									{ label: "Rephrase", icon: RefreshIcon },
									{ label: "Fix spelling", icon: TextCheckIcon },
									{ label: "Simplify", icon: FlashlightIcon },
									{ label: "Shorten", icon: Menu01Icon },
									{ label: "Expand", icon: Plug01Icon },
									{ label: "Change Tone", icon: AiPhone01Icon, hasArrow: true, onClick: () => setShowToneMenu(!showToneMenu) },
									{ label: "Translate", icon: TranslateIcon, hasArrow: true, onClick: () => setShowTranslateMenu(!showTranslateMenu) },
								].map((item, index) => (
									<div
										key={index}
										onClick={() => {
											if (item.onClick) {
												item.onClick();
											} else {
												onAIEdit?.(item.label);
												setShowAIMenu(false);
											}
										}}
										style={{
											padding: "12px 16px",
											cursor: "pointer",
											display: "flex",
											alignItems: "center",
											gap: "12px",
											fontSize: "14px",
											background: index === 0 ? "#f3f4f6" : "transparent",
											transition: "background-color 0.2s ease",
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.background = "#f3f4f6";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.background = index === 0 ? "#f3f4f6" : "transparent";
										}}
									>
										<HugeiconsIcon icon={item.icon} size={16} color="#6b7280" />
										<span style={{ flex: 1 }}>{item.label}</span>
										{item.hasArrow && <span style={{ color: "#9ca3af" }}>→</span>}
									</div>
								))}
							</div>
						)}
					</div>
				</div>

				{/* Font Size */}
				<div style={{ marginBottom: "12px" }}>
					<label style={{
						fontSize: "11px",
						fontWeight: "600",
						color: "#6b7280",
						display: "block",
						marginBottom: "6px",
						textTransform: "uppercase",
						letterSpacing: "0.5px"
					}}>
						Size
					</label>
					<div style={{ position: "relative" }}>
						<button
							onClick={() => setShowFontSizeMenu(!showFontSizeMenu)}
							style={{
								width: "100%",
								background: "white",
								border: "1px solid #d1d5db",
								borderRadius: "6px",
								padding: "8px 12px",
								cursor: "pointer",
								fontSize: "12px",
								textAlign: "left",
								display: "flex",
								alignItems: "center",
								justifyContent: "space-between",
								transition: "all 0.2s ease",
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.borderColor = "#3b82f6";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.borderColor = "#d1d5db";
							}}
						>
							<span>{fontSize}</span>
							<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
								<path d="M6 9l6 6 6-6" />
							</svg>
						</button>
						
						{showFontSizeMenu && (
							<div
								style={{
									position: "absolute",
									top: "100%",
									left: 0,
									right: 0,
									background: "white",
									border: "1px solid #e5e7eb",
									borderRadius: "8px",
									boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
									padding: "8px 0",
									zIndex: 1001,
									marginTop: "4px",
								}}
							>
								{fontSizeOptions.map((size) => (
									<div
										key={size}
										onClick={() => {
											onFontSize?.(size);
											setShowFontSizeMenu(false);
										}}
										style={{
											padding: "12px 16px",
											cursor: "pointer",
											fontSize: "14px",
											transition: "background-color 0.2s ease",
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.background = "#f3f4f6";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.background = "transparent";
										}}
									>
										{size}
									</div>
								))}
							</div>
						)}
					</div>
				</div>

				{/* Font Family */}
				<div style={{ marginBottom: "12px" }}>
					<label style={{
						fontSize: "11px",
						fontWeight: "600",
						color: "#6b7280",
						display: "block",
						marginBottom: "6px",
						textTransform: "uppercase",
						letterSpacing: "0.5px"
					}}>
						Font
					</label>
					<div style={{ position: "relative" }}>
						<button
							onClick={() => setShowFontFamilyMenu(!showFontFamilyMenu)}
							style={{
								width: "100%",
								background: "white",
								border: "1px solid #d1d5db",
								borderRadius: "6px",
								padding: "8px 12px",
								cursor: "pointer",
								fontSize: "12px",
								textAlign: "left",
								display: "flex",
								alignItems: "center",
								justifyContent: "space-between",
								transition: "all 0.2s ease",
								fontFamily: fontFamily,
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.borderColor = "#3b82f6";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.borderColor = "#d1d5db";
							}}
						>
							<span>{fontFamily}</span>
							<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
								<path d="M6 9l6 6 6-6" />
							</svg>
						</button>

						{showFontFamilyMenu && (
							<div
								style={{
									position: "absolute",
									top: "100%",
									left: 0,
									right: 0,
									background: "white",
									border: "1px solid #e5e7eb",
									borderRadius: "8px",
									boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
									padding: "8px 0",
									zIndex: 1001,
									marginTop: "4px",
									maxHeight: "200px",
									overflowY: "auto",
								}}
							>
								{fontFamilyOptions.map((family) => (
									<div
										key={family}
										onClick={() => {
											onFontFamily?.(family);
											setShowFontFamilyMenu(false);
										}}
										style={{
											padding: "12px 16px",
											cursor: "pointer",
											fontSize: "14px",
											fontFamily: family,
											transition: "background-color 0.2s ease",
										}}
										onMouseEnter={(e) => {
											e.currentTarget.style.background = "#f3f4f6";
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.background = "transparent";
										}}
									>
										{family}
									</div>
								))}
							</div>
						)}
					</div>
				</div>

				{/* Formatting Buttons */}
				<div style={{ marginBottom: "12px" }}>
					<label style={{
						fontSize: "11px",
						fontWeight: "600",
						color: "#6b7280",
						display: "block",
						marginBottom: "6px",
						textTransform: "uppercase",
						letterSpacing: "0.5px"
					}}>
						Style
					</label>
					<div style={{ display: "flex", gap: "4px" }}>
						<button
							onClick={onBold}
							style={{
								background: isBold ? "#3b82f6" : "white",
								color: isBold ? "white" : "#374151",
								border: "1px solid #d1d5db",
								borderRadius: "6px",
								padding: "8px",
								cursor: "pointer",
								fontWeight: "bold",
								fontSize: "12px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								transition: "all 0.2s ease",
								flex: 1,
								minHeight: "32px",
							}}
							onMouseEnter={(e) => {
								if (!isBold) {
									e.currentTarget.style.borderColor = "#3b82f6";
								}
							}}
							onMouseLeave={(e) => {
								if (!isBold) {
									e.currentTarget.style.borderColor = "#d1d5db";
								}
							}}
						>
							<HugeiconsIcon icon={TextBoldIcon} size={14} />
						</button>
						<button
							onClick={onItalic}
							style={{
								background: isItalic ? "#3b82f6" : "white",
								color: isItalic ? "white" : "#374151",
								border: "1px solid #d1d5db",
								borderRadius: "6px",
								padding: "8px",
								cursor: "pointer",
								fontSize: "12px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								transition: "all 0.2s ease",
								flex: 1,
								minHeight: "32px",
							}}
							onMouseEnter={(e) => {
								if (!isItalic) {
									e.currentTarget.style.borderColor = "#3b82f6";
								}
							}}
							onMouseLeave={(e) => {
								if (!isItalic) {
									e.currentTarget.style.borderColor = "#d1d5db";
								}
							}}
						>
							<HugeiconsIcon icon={TextItalicIcon} size={14} />
						</button>
						<button
							onClick={onUnderline}
							style={{
								background: isUnderline ? "#3b82f6" : "white",
								color: isUnderline ? "white" : "#374151",
								border: "1px solid #d1d5db",
								borderRadius: "6px",
								padding: "8px",
								cursor: "pointer",
								fontSize: "12px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								transition: "all 0.2s ease",
								flex: 1,
								minHeight: "32px",
							}}
							onMouseEnter={(e) => {
								if (!isUnderline) {
									e.currentTarget.style.borderColor = "#3b82f6";
								}
							}}
							onMouseLeave={(e) => {
								if (!isUnderline) {
									e.currentTarget.style.borderColor = "#d1d5db";
								}
							}}
						>
							<HugeiconsIcon icon={TextUnderlineIcon} size={14} />
						</button>
					</div>
				</div>

				{/* Text Alignment */}
				<div style={{ marginBottom: "12px" }}>
					<label style={{
						fontSize: "11px",
						fontWeight: "600",
						color: "#6b7280",
						display: "block",
						marginBottom: "6px",
						textTransform: "uppercase",
						letterSpacing: "0.5px"
					}}>
						Alignment
					</label>
					<div style={{ display: "flex", gap: "4px" }}>
						{[
							{ align: "left", icon: TextAlignLeftIcon },
							{ align: "center", icon: TextAlignCenterIcon },
							{ align: "right", icon: TextAlignRightIcon },
						].map(({ align, icon: Icon }) => (
							<button
								key={align}
								onClick={() => onTextAlign?.(align)}
								style={{
									background: textAlign === align ? "#3b82f6" : "white",
									color: textAlign === align ? "white" : "#374151",
									border: "1px solid #d1d5db",
									borderRadius: "6px",
									padding: "8px",
									cursor: "pointer",
									fontSize: "12px",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									transition: "all 0.2s ease",
									flex: 1,
									minHeight: "32px",
								}}
								onMouseEnter={(e) => {
									if (textAlign !== align) {
										e.currentTarget.style.borderColor = "#3b82f6";
									}
								}}
								onMouseLeave={(e) => {
									if (textAlign !== align) {
										e.currentTarget.style.borderColor = "#d1d5db";
									}
								}}
							>
								<HugeiconsIcon icon={Icon} size={14} />
							</button>
						))}
					</div>
				</div>

				{/* Colors */}
				<div style={{ marginBottom: "12px" }}>
					<label style={{
						fontSize: "11px",
						fontWeight: "600",
						color: "#6b7280",
						display: "block",
						marginBottom: "6px",
						textTransform: "uppercase",
						letterSpacing: "0.5px"
					}}>
						Colors
					</label>
					<div style={{ display: "flex", gap: "12px" }}>
						<div style={{ flex: 1 }}>
							<label style={{ 
								fontSize: "11px", 
								fontWeight: "500", 
								color: "#6b7280", 
								marginBottom: "6px",
								display: "flex",
								alignItems: "center",
								gap: "4px"
							}}>
								<HugeiconsIcon icon={AlertIcon} size={12} />
								Text
							</label>
							<input
								type="color"
								value={textColor}
								onChange={(e) => onTextColor?.(e.target.value)}
								style={{
									width: "100%",
									height: "40px",
									border: "1px solid #d1d5db",
									borderRadius: "8px",
									cursor: "pointer",
									background: "transparent",
								}}
							/>
						</div>
						<div style={{ flex: 1 }}>
							<label style={{ 
								fontSize: "11px", 
								fontWeight: "500", 
								color: "#6b7280", 
								marginBottom: "6px",
								display: "flex",
								alignItems: "center",
								gap: "4px"
							}}>
								<HugeiconsIcon icon={BackgroundIcon} size={12} />
								Background
							</label>
							<input
								type="color"
								value={backgroundColor}
								onChange={(e) => onBackgroundColor?.(e.target.value)}
								style={{
									width: "100%",
									height: "40px",
									border: "1px solid #d1d5db",
									borderRadius: "8px",
									cursor: "pointer",
									background: "transparent",
								}}
							/>
						</div>
					</div>
				</div>

				{/* Additional Tools */}
				<div>
					<label style={{
						fontSize: "11px",
						fontWeight: "600",
						color: "#6b7280",
						display: "block",
						marginBottom: "6px",
						textTransform: "uppercase",
						letterSpacing: "0.5px"
					}}>
						Tools
					</label>
					<div style={{ display: "flex", gap: "4px" }}>
						<button
							onClick={onLink}
							style={{
								background: "white",
								color: "#374151",
								border: "1px solid #d1d5db",
								borderRadius: "6px",
								padding: "8px",
								cursor: "pointer",
								fontSize: "12px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								transition: "all 0.2s ease",
								flex: 1,
								minHeight: "32px",
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.borderColor = "#3b82f6";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.borderColor = "#d1d5db";
							}}
						>
							<HugeiconsIcon icon={Link01Icon} size={14} />
						</button>
						<button
							onClick={onMore}
							style={{
								background: "white",
								color: "#374151",
								border: "1px solid #d1d5db",
								borderRadius: "6px",
								padding: "8px",
								cursor: "pointer",
								fontSize: "12px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								transition: "all 0.2s ease",
								flex: 1,
								minHeight: "32px",
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.borderColor = "#3b82f6";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.borderColor = "#d1d5db";
							}}
						>
							<HugeiconsIcon icon={MoreHorizontalIcon} size={14} />
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
