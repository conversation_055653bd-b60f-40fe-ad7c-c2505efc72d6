"use client";

import type { FunnelPage, FunnelSection } from "@/lib/types/funnel";
import { <PERSON>ge, Button, Dialog, Heading, Text } from "frosted-ui";
import React from "react";

interface PagePreviewProps {
	page: FunnelPage | null;
	isOpen: boolean;
	onClose: () => void;
	theme?: {
		primaryColor: string;
		backgroundColor: string;
		textColor: string;
	};
}

export function PagePreview({
	page,
	isOpen,
	onClose,
	theme,
}: PagePreviewProps) {
	if (!page) return null;

	const renderSection = (section: FunnelSection) => {
		switch (section.type) {
			case "hero":
				return (
					<div
						key={section.id}
						style={{
							background: "linear-gradient(135deg, var(--accent-3) 0%, var(--accent-5) 100%)",
							borderRadius: "16px",
							padding: "48px 32px",
							textAlign: "center",
							marginBottom: "24px",
						}}
					>
						<Heading size="9" style={{ marginBottom: "16px", color: theme?.textColor || "inherit" }}>
							{section.heading}
						</Heading>
						<Text size="5" style={{ marginBottom: "24px", color: "var(--gray-11)" }}>
							{section.content}
						</Text>
						{section.cta && (
							<Button
								size="4"
								variant={section.cta.style === "primary" ? "solid" : "soft"}
								style={{ background: theme?.primaryColor || "var(--accent-9)" }}
							>
								{section.cta.text}
							</Button>
						)}
					</div>
				);

			case "features":
				return (
					<div key={section.id} style={{ marginBottom: "24px" }}>
						<Heading size="7" align="center" style={{ marginBottom: "16px" }}>
							{section.heading}
						</Heading>
						<Text size="3" color="gray" align="center" style={{ marginBottom: "32px" }}>
							{section.content}
						</Text>
						{section.items && (
							<div
								style={{
									display: "grid",
									gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
									gap: "24px",
								}}
							>
								{section.items.map((item, idx) => (
									<div
										key={`${section.id}-${idx}`}
										style={{
											padding: "24px",
											borderRadius: "12px",
											background: "var(--gray-3)",
											border: "1px solid var(--gray-6)",
										}}
									>
										<div
											style={{
												width: "48px",
												height: "48px",
												borderRadius: "50%",
												background: "var(--accent-4)",
												display: "flex",
												alignItems: "center",
												justifyContent: "center",
												marginBottom: "16px",
											}}
										>
											<svg
												width="24"
												height="24"
												viewBox="0 0 24 24"
												fill="none"
												stroke="var(--accent-9)"
												strokeWidth="2"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													d="M5 13l4 4L19 7"
												/>
											</svg>
										</div>
										<Heading size="4" style={{ marginBottom: "8px" }}>
											{item.title}
										</Heading>
										<Text size="2" color="gray">
											{item.description}
										</Text>
									</div>
								))}
							</div>
						)}
					</div>
				);

			case "pricing":
				return (
					<div key={section.id} style={{ marginBottom: "24px" }}>
						<Heading size="7" align="center" style={{ marginBottom: "16px" }}>
							{section.heading}
						</Heading>
						<Text size="3" color="gray" align="center" style={{ marginBottom: "32px" }}>
							{section.content}
						</Text>
						{section.items && section.items.length > 0 && (
							<div
								style={{
									display: "grid",
									gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
									gap: "24px",
								}}
							>
								{section.items.map((item, idx) => (
									<div
										key={`${section.id}-${idx}`}
										style={{
											padding: "32px",
											borderRadius: "16px",
											background: "var(--gray-2)",
											border: "2px solid var(--gray-6)",
											textAlign: "center",
										}}
									>
										<Heading size="5" style={{ marginBottom: "8px" }}>
											{item.title}
										</Heading>
										<Text size="2" color="gray" style={{ marginBottom: "24px" }}>
											{item.description}
										</Text>
										{section.cta && (
											<Button
												size="3"
												variant="solid"
												style={{ width: "100%" }}
											>
												{section.cta.text}
											</Button>
										)}
									</div>
								))}
							</div>
						)}
					</div>
				);

			case "testimonials":
				return (
					<div key={section.id} style={{ marginBottom: "24px" }}>
						<Heading size="7" align="center" style={{ marginBottom: "32px" }}>
							{section.heading}
						</Heading>
						{section.items && section.items.length > 0 && (
							<div
								style={{
									display: "grid",
									gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
									gap: "24px",
								}}
							>
								{section.items.map((item, idx) => (
									<div
										key={`${section.id}-${idx}`}
										style={{
											padding: "24px",
											borderRadius: "12px",
											background: "var(--gray-3)",
											border: "1px solid var(--gray-6)",
										}}
									>
									<Text size="3" color="gray" style={{ fontStyle: "italic", marginBottom: "16px" }}>
										&ldquo;{item.description}&rdquo;
									</Text>
										<div
											style={{
												borderTop: "1px solid var(--gray-6)",
												paddingTop: "16px",
											}}
										>
											<Text size="2" weight="bold">
												{item.title}
											</Text>
										</div>
									</div>
								))}
							</div>
						)}
					</div>
				);

			case "cta":
				return (
					<div
						key={section.id}
						style={{
							background: "linear-gradient(135deg, var(--accent-9) 0%, var(--accent-10) 100%)",
							borderRadius: "16px",
							padding: "48px 32px",
							textAlign: "center",
							marginBottom: "24px",
						}}
					>
						<Heading size="7" style={{ marginBottom: "16px", color: "white" }}>
							{section.heading}
						</Heading>
						<Text size="3" style={{ marginBottom: "24px", color: "rgba(255, 255, 255, 0.9)" }}>
							{section.content}
						</Text>
						{section.cta && (
							<Button size="4" variant="surface" highContrast>
								{section.cta.text}
							</Button>
						)}
					</div>
				);

			case "faq":
				return (
					<div key={section.id} style={{ marginBottom: "24px" }}>
						<Heading size="7" align="center" style={{ marginBottom: "32px" }}>
							{section.heading}
						</Heading>
						{section.items && section.items.length > 0 && (
							<div style={{ maxWidth: "800px", margin: "0 auto" }}>
								{section.items.map((item, idx) => (
									<div
										key={`${section.id}-${idx}`}
										style={{
											padding: "24px",
											borderRadius: "12px",
											background: "var(--gray-2)",
											border: "1px solid var(--gray-6)",
											marginBottom: "16px",
										}}
									>
										<Text size="3" weight="bold" style={{ marginBottom: "8px" }}>
											{item.title}
										</Text>
										<Text size="2" color="gray">
											{item.description}
										</Text>
									</div>
								))}
							</div>
						)}
					</div>
				);

			default:
				return (
					<div
						key={section.id}
						style={{
							padding: "24px",
							borderRadius: "12px",
							background: "var(--gray-2)",
							border: "1px solid var(--gray-6)",
							marginBottom: "16px",
						}}
					>
						<Heading size="5" style={{ marginBottom: "8px" }}>
							{section.heading}
						</Heading>
						<Text size="2" color="gray">
							{section.content}
						</Text>
					</div>
				);
		}
	};

	const getPageTypeColor = (type: string) => {
		switch (type) {
			case "landing":
				return "blue";
			case "upsell":
				return "green";
			case "downsell":
				return "amber";
			case "thank-you":
				return "purple";
			case "checkout":
				return "cyan";
			default:
				return "gray";
		}
	};

	return (
		<Dialog.Root open={isOpen} onOpenChange={onClose}>
			<Dialog.Content
				style={{
					maxWidth: "90vw",
					width: "1200px",
					maxHeight: "90vh",
					padding: 0,
					overflow: "hidden",
				}}
			>
				<div
					style={{
						display: "flex",
						flexDirection: "column",
						height: "90vh",
					}}
				>
					{/* Header */}
					<div
						style={{
							padding: "24px",
							borderBottom: "1px solid var(--gray-6)",
							background: "var(--gray-2)",
							display: "flex",
							alignItems: "center",
							justifyContent: "space-between",
						}}
					>
						<div>
							<div style={{ display: "flex", alignItems: "center", gap: "12px", marginBottom: "8px" }}>
								<Heading size="6">{page.name}</Heading>
								<Badge
									color={
										getPageTypeColor(page.type) as
											| "blue"
											| "green"
											| "amber"
											| "purple"
											| "cyan"
											| "gray"
									}
									size="2"
								>
									{page.type}
								</Badge>
							</div>
							<Text size="2" color="gray">
								Preview Mode • {page.sections.length} section
								{page.sections.length !== 1 ? "s" : ""}
							</Text>
						</div>
						<Button size="2" variant="soft" onClick={onClose}>
							Close Preview
						</Button>
					</div>

					{/* Content */}
					<div
						style={{
							flex: 1,
							overflowY: "auto",
							background: "transparent",
							padding: 0,
						}}
					>
						<div
							style={{
								width: "100%",
								color: theme?.textColor || "inherit",
							}}
						>
							{page.sections.length > 0 ? (
								page.sections.map((section) => renderSection(section))
							) : (
								<div
									style={{
										textAlign: "center",
										padding: "64px 32px",
										border: "1px solid var(--gray-4)",
										borderRadius: "16px",
										background: "var(--gray-2)",
									}}
								>
									<Heading size="6" style={{ marginBottom: "16px" }}>
										No Content Yet
									</Heading>
								<Text size="3" color="gray">
									This page doesn&apos;t have any sections yet. Edit the page to add content.
								</Text>
								</div>
							)}
						</div>
					</div>

					{/* Footer */}
					{page.type === "checkout" && page.whopPlanIds && page.whopPlanIds.length > 0 && (
						<div
							style={{
								padding: "16px 24px",
								borderTop: "1px solid var(--gray-6)",
								background: "var(--gray-2)",
								display: "flex",
								alignItems: "center",
								gap: "8px",
							}}
						>
							<svg
								width="16"
								height="16"
								viewBox="0 0 24 24"
								fill="none"
								stroke="var(--cyan-9)"
								strokeWidth="2"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3v-8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
								/>
							</svg>
							<Text size="2" color="gray">
								Whop Plans:
							</Text>
							{page.whopPlanIds.map((planId) => (
								<Badge key={planId} color="cyan" size="1">
									{planId}
								</Badge>
							))}
						</div>
					)}
				</div>
			</Dialog.Content>
		</Dialog.Root>
	);
}
