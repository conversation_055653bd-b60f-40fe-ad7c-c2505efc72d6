"use client";

import { Text } from "frosted-ui";
import React, { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { VideoUpload } from "./VideoUpload";

interface ElementProperties {
	padding?: { top: number; right: number; bottom: number; left: number };
	textColor?: string;
	textAlign?: string;
	visible?: boolean;
	animation?: string;
	fontSize?: number | string;
	backgroundColor?: string;
	imageUrl?: string;
	altText?: string;
	linkUrl?: string;
	actionType?: string;
	inputType?: string;
	placeholder?: string;
	content?: string;
	[key: string]: unknown;
}

interface EditorElement {
	id: string;
	type: string;
	name: string;
	content: string;
	properties: ElementProperties;
}

interface InlineElementTooltipProps {
	element: EditorElement;
	onUpdate: (properties: ElementProperties) => void;
	onDelete: () => void;
	position: { x: number; y: number };
	onClose: () => void;
	context?: { heroPart?: string; statIndex?: number; logoIndex?: number; badgeIndex?: number };
}

export function InlineElementTooltip({
	element,
	onUpdate,
	onDelete,
	position,
	onClose,
	context,
}: InlineElementTooltipProps) {
	const [activeTab, setActiveTab] = useState<"text" | "element">((element.type === "video" || context?.heroPart === "video") ? "element" : "text");
	const [tooltipHeight, setTooltipHeight] = useState<number>(600); // Default height
	const tooltipRef = useRef<HTMLDivElement>(null);
	
	// console.log('🎯 TOOLTIP RENDERED for element:', element.type, element.id, 'context:', context);
	

	// Define these variables first so they can be used in callbacks
	const isTextElement = [
		"headline", "subheadline", "paragraph", "button", "hero", 
		"features", "pricing", "testimonials", "form"
	].includes(element.type);

	const isHero = element.type === "hero";
	const heroPart = context?.heroPart;

	// Memoize the tooltip position to avoid recalculating on every render
	const tooltipStyle = useMemo(() => {
		const maxWidth = 320;
		const maxHeight = Math.max(tooltipHeight, 400); // Use actual height or minimum
		const margin = 20; // Minimum margin from screen edges
		
		// Calculate horizontal position
		let left = position.x;
		if (left + maxWidth > window.innerWidth - margin) {
			left = window.innerWidth - maxWidth - margin;
		}
		if (left < margin) {
			left = margin;
		}
		
		// Calculate vertical position
		let top = position.y + 10; // Default: position below click
		
		// Check if tooltip would overflow at bottom
		if (top + maxHeight > window.innerHeight - margin) {
			// Position above click instead
			top = position.y - maxHeight - 10;
			
			// If it still doesn't fit above, position at top of screen
			if (top < margin) {
				top = margin;
			}
		}
		
		// Ensure tooltip doesn't go above screen
		if (top < margin) {
			top = margin;
		}
		
		return {
			position: "fixed" as const,
			zIndex: 1000,
			background: "var(--gray-1)",
			border: "1px solid var(--gray-6)",
			borderRadius: "16px",
			boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08)",
			padding: "20px",
			width: `${maxWidth}px`,
			maxHeight: `${maxHeight}px`,
			overflowY: "auto" as const,
			left: `${left}px`,
			top: `${top}px`,
			// Optimize for performance
			transform: "translateZ(0)",
			backfaceVisibility: "hidden" as const,
			perspective: "1000px",
			backdropFilter: "blur(8px)",
		};
	}, [position.x, position.y, tooltipHeight]);

	// Measure tooltip height after render
	useEffect(() => {
		if (tooltipRef.current) {
			const height = tooltipRef.current.offsetHeight;
			setTooltipHeight(height);
		}
	}, [activeTab, element, context]); // Re-measure when content changes

	// Close tooltip when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
				onClose();
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, [onClose]);

	const handlePropertyChange = useCallback((key: string, value: unknown) => {
		console.log('🔧 Property change:', key, '→', value);
		if (isHero && heroPart) {
			// Special handling for videoUrl - don't add hero part prefix
			if (key === 'videoUrl') {
				console.log('🎬 Video URL - using direct property');
				onUpdate({
					...element.properties,
					[key]: value,
				});
			} else {
				// For other hero parts, use specific property keys
				const specificKey = `${heroPart}${key.charAt(0).toUpperCase() + key.slice(1)}`;
				console.log('🎯 Hero specific key:', specificKey);
				onUpdate({
					...element.properties,
					[specificKey]: value,
				});
			}
		} else {
			console.log('📝 Regular property update');
			onUpdate({
				...element.properties,
				[key]: value,
			});
		}
	}, [element.properties, onUpdate, isHero, heroPart]);

	const getDefaultFontSize = useCallback((): number => {
		if (isHero) {
			switch (heroPart) {
				case 'heading': return 48;
				case 'subheading': return 20;
				case 'button': return 16;
				default: return 48;
			}
		}
		return 16;
	}, [isHero, heroPart]);

	const getCurrentFontSize = useCallback((): number => {
		if (isHero && heroPart) {
			// For hero parts, use specific properties
			const fontSizeKey = `${heroPart}FontSize`;
			const value = element.properties[fontSizeKey];
			if (typeof value === 'number') return value;
			if (typeof value === 'string' && value.endsWith('px')) {
				return parseInt(value.replace('px', ''));
			}
			return getDefaultFontSize();
		}
		const value = element.properties.fontSize;
		if (typeof value === 'number') return value;
		if (typeof value === 'string' && value.endsWith('px')) {
			return parseInt(value.replace('px', ''));
		}
		return getDefaultFontSize();
	}, [isHero, heroPart, element.properties, getDefaultFontSize]);

	const getCurrentFontFamily = useCallback((): string => {
		// Check for specific hero part font family first
		if (heroPart) {
			const fontFamilyKey = `${heroPart}FontFamily`;
			const value = element.properties[fontFamilyKey];
			if (typeof value === 'string') return value;
		}
		// Check for general font family
		const value = element.properties.fontFamily;
		if (typeof value === 'string') return value;
		return 'Inter'; // Default font
	}, [heroPart, element.properties]);

	const handleFontFamilyChange = useCallback((fontFamily: string) => {
		console.log('🎨 Font family change:', fontFamily, 'isHero:', isHero, 'heroPart:', heroPart);
		if (heroPart) {
			// For any element with a specific part, use specific properties
			const fontFamilyKey = `${heroPart}FontFamily`;
			console.log('🎯 Setting font family key:', fontFamilyKey, 'to:', fontFamily);
			onUpdate({
				...element.properties,
				[fontFamilyKey]: fontFamily,
			});
		} else {
			console.log('📝 Setting general fontFamily to:', fontFamily);
			onUpdate({
				...element.properties,
				fontFamily: fontFamily,
			});
		}
	}, [heroPart, element.properties, onUpdate, isHero]);

	const getFontSizeOptions = useCallback(() => {
		if (isHero) {
			switch (heroPart) {
				case 'heading':
					return (
						<>
							<option value={32}>32px</option>
							<option value={40}>40px</option>
							<option value={48}>48px</option>
							<option value={56}>56px</option>
							<option value={64}>64px</option>
						</>
					);
				case 'subheading':
					return (
						<>
							<option value={14}>14px</option>
							<option value={16}>16px</option>
							<option value={18}>18px</option>
							<option value={20}>20px</option>
							<option value={24}>24px</option>
						</>
					);
				case 'button':
					return (
						<>
							<option value={12}>12px</option>
							<option value={14}>14px</option>
							<option value={16}>16px</option>
							<option value={18}>18px</option>
							<option value={20}>20px</option>
						</>
					);
				default:
					return (
						<>
							<option value={32}>32px</option>
							<option value={40}>40px</option>
							<option value={48}>48px</option>
							<option value={56}>56px</option>
							<option value={64}>64px</option>
						</>
					);
			}
		}
		return (
			<>
				<option value={12}>12px</option>
				<option value={16}>16px</option>
				<option value={20}>20px</option>
				<option value={24}>24px</option>
				<option value={32}>32px</option>
			</>
		);
	}, [isHero, heroPart]);

	const handleFontSizeChange = useCallback((fontSize: number) => {
		const fontSizeWithUnit = `${fontSize}px`;
		console.log('🔄 Font size change:', fontSize, '→', fontSizeWithUnit);
		if (isHero && heroPart) {
			// For hero parts, use specific properties
			const fontSizeKey = `${heroPart}FontSize`;
			console.log('🎯 Hero font size key:', fontSizeKey);
			handlePropertyChange(fontSizeKey, fontSizeWithUnit);
		} else {
			console.log('📝 Regular font size update');
			handlePropertyChange("fontSize", fontSizeWithUnit);
		}
	}, [isHero, heroPart, handlePropertyChange]);

	const renderTextControls = useCallback(() => {
		if (!isTextElement) return null;
		
		return (
			<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
				{/* Element Type Indicator */}
				<div style={{ fontSize: "10px", color: "#666", marginBottom: "8px", textTransform: "uppercase", fontWeight: "600" }}>
					{isHero ? `Hero ${heroPart || 'Section'}` : element.type}
				</div>

				{/* Font Size */}
				<div>
					<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Font Size</label>
					<select
						value={getCurrentFontSize()}
						onChange={(e) => {
							console.log('🔥 SELECT ONCHANGE FIRED!', e.target.value);
							handleFontSizeChange(parseInt(e.target.value));
						}}
						onClick={() => console.log('🔥 SELECT CLICKED!')}
						style={{ width: "100%", padding: "4px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px" }}
					>
						{getFontSizeOptions()}
					</select>
				</div>

				{/* Font Family */}
				<div>
					<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Font Family</label>
					<select
						value={getCurrentFontFamily()}
						onChange={(e) => handleFontFamilyChange(e.target.value)}
						style={{ width: "100%", padding: "4px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px" }}
					>
						<option value="Inter">Inter (Google Fonts)</option>
						<option value="Manrope">Manrope (Google Fonts)</option>
						<option value="Roboto">Roboto (Google Fonts)</option>
						<option value="'Open Sans', sans-serif">Open Sans (Google Fonts)</option>
						<option value="Lato">Lato (Google Fonts)</option>
						<option value="'Helvetica Neue', Helvetica, Arial, sans-serif">Helvetica Neue (System)</option>
						<option value="'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif">SF Pro Display (System)</option>
						<option value="'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">Segoe UI (System)</option>
					</select>
				</div>

				{/* Text Color */}
				<div>
					<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Text Color</label>
					<input
						type="color"
						value={(() => {
							const color = isHero && heroPart ? element.properties[`${heroPart}TextColor`] : element.properties.textColor;
							return typeof color === 'string' ? color : "#000000";
						})()}
						onChange={(e) => handlePropertyChange("textColor", e.target.value)}
						style={{ width: "100%", height: "32px", border: "1px solid #d1d5db", borderRadius: "4px", cursor: "pointer" }}
					/>
					<div style={{ fontSize: "10px", color: "#666", marginTop: "2px" }}>
						Current: {(() => {
							const color = isHero && heroPart ? element.properties[`${heroPart}TextColor`] : element.properties.textColor;
							return typeof color === 'string' ? color : "#000000";
						})()}
					</div>
				</div>

				{/* Content - Context-aware based on hero part */}
				{isHero && heroPart === 'heading' && (
					<div>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Main Headline</label>
						<textarea
							value={element.content || ""}
							onChange={(e) => {
								onUpdate({ ...element.properties, _content: e.target.value });
							}}
							onInput={(e) => {
								onUpdate({ ...element.properties, _content: (e.target as HTMLTextAreaElement).value });
							}}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px", minHeight: "50px", resize: "vertical" }}
							placeholder="Enter main headline..."
						/>
					</div>
				)}

				{isHero && heroPart === 'subheading' && (
					<div>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Subtitle</label>
						<textarea
							value={typeof element.properties.subtitle === 'string' ? element.properties.subtitle : ""}
							onChange={(e) => handlePropertyChange("subtitle", e.target.value)}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px", minHeight: "50px", resize: "vertical" }}
							placeholder="Enter subtitle text..."
						/>
					</div>
				)}

				{isHero && heroPart === 'button' && (
					<div>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Button Text</label>
						<textarea
							value={typeof element.properties.ctaText === 'string' ? element.properties.ctaText : ""}
							onChange={(e) => handlePropertyChange("ctaText", e.target.value)}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px", minHeight: "40px", resize: "vertical" }}
							placeholder="Enter button text..."
						/>
					</div>
				)}

				{/* Social Proof Section Text Inputs */}
				{isHero && heroPart === 'socialProofTitle' && (
					<div>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Section Title</label>
						<textarea
							value={element.content || ""}
							onChange={(e) => {
								onUpdate({ ...element.properties, _content: e.target.value });
							}}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px", minHeight: "50px", resize: "vertical" }}
							placeholder="Enter section title..."
						/>
					</div>
				)}

				{isHero && heroPart === 'socialProofSubtitle' && (
					<div>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Section Subtitle</label>
						<textarea
							value={typeof element.properties.subtitle === 'string' ? element.properties.subtitle : ""}
							onChange={(e) => handlePropertyChange("subtitle", e.target.value)}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px", minHeight: "50px", resize: "vertical" }}
							placeholder="Enter section subtitle..."
						/>
					</div>
				)}

				{isHero && heroPart === 'socialProofStat' && (
					<div>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Statistic Number</label>
						<input
							type="text"
							value={(() => {
								const stats = element.properties.stats as Array<{ number: string; label: string }> || [];
								const statIndex = context?.statIndex || 0;
								return stats[statIndex]?.number || "";
							})()}
							onChange={(e) => {
								const stats = element.properties.stats as Array<{ number: string; label: string }> || [];
								const statIndex = context?.statIndex || 0;
								const newStats = [...stats];
								if (!newStats[statIndex]) {
									newStats[statIndex] = { number: "", label: "" };
								}
								newStats[statIndex].number = e.target.value;
								handlePropertyChange("stats", newStats);
							}}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px", marginBottom: "8px" }}
							placeholder="Enter statistic number..."
						/>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Statistic Label</label>
						<input
							type="text"
							value={(() => {
								const stats = element.properties.stats as Array<{ number: string; label: string }> || [];
								const statIndex = context?.statIndex || 0;
								return stats[statIndex]?.label || "";
							})()}
							onChange={(e) => {
								const stats = element.properties.stats as Array<{ number: string; label: string }> || [];
								const statIndex = context?.statIndex || 0;
								const newStats = [...stats];
								if (!newStats[statIndex]) {
									newStats[statIndex] = { number: "", label: "" };
								}
								newStats[statIndex].label = e.target.value;
								handlePropertyChange("stats", newStats);
							}}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px" }}
							placeholder="Enter statistic label..."
						/>
					</div>
				)}

				{isHero && heroPart === 'socialProofLogo' && (
					<div>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Company Name</label>
						<input
							type="text"
							value={(() => {
								const logos = element.properties.companyLogos as string[] || [];
								const logoIndex = context?.logoIndex || 0;
								return logos[logoIndex] || "";
							})()}
							onChange={(e) => {
								const logos = element.properties.companyLogos as string[] || [];
								const logoIndex = context?.logoIndex || 0;
								const newLogos = [...logos];
								newLogos[logoIndex] = e.target.value;
								handlePropertyChange("companyLogos", newLogos);
							}}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px" }}
							placeholder="Enter company name..."
						/>
					</div>
				)}

				{isHero && heroPart === 'socialProofBadge' && (
					<div>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Badge Text</label>
						<input
							type="text"
							value={(() => {
								const badges = element.properties.trustBadges as Array<{ icon: string; text: string }> || [];
								const badgeIndex = context?.badgeIndex || 0;
								return badges[badgeIndex]?.text || "";
							})()}
							onChange={(e) => {
								const badges = element.properties.trustBadges as Array<{ icon: string; text: string }> || [];
								const badgeIndex = context?.badgeIndex || 0;
								const newBadges = [...badges];
								if (!newBadges[badgeIndex]) {
									newBadges[badgeIndex] = { icon: "check", text: "" };
								}
								newBadges[badgeIndex].text = e.target.value;
								handlePropertyChange("trustBadges", newBadges);
							}}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px" }}
							placeholder="Enter badge text..."
						/>
					</div>
				)}


				{/* For non-hero elements or hero without specific part */}
				{!isHero && element.type !== "video" && context?.heroPart !== "video" && (
					<div>
						<label style={{ display: "block", fontSize: "12px", fontWeight: "500", marginBottom: "4px" }}>Content</label>
						<textarea
							value={element.content || ""}
							onChange={(e) => {
								onUpdate({ ...element.properties, _content: e.target.value });
							}}
							onInput={(e) => {
								onUpdate({ ...element.properties, _content: (e.target as HTMLTextAreaElement).value });
							}}
							style={{ width: "100%", padding: "6px", fontSize: "12px", border: "1px solid #d1d5db", borderRadius: "4px", minHeight: "50px", resize: "vertical" }}
						/>
					</div>
				)}
			</div>
		);
	}, [isTextElement, handlePropertyChange, onUpdate, element, context, getCurrentFontFamily, getCurrentFontSize, getFontSizeOptions, handleFontFamilyChange, handleFontSizeChange, heroPart, isHero]);

	const renderElementControls = useCallback(() => {
		return (
			<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
				{/* Background Color */}
				<div>
					<label style={{ 
						display: "block", 
						fontSize: "12px", 
						fontWeight: "600", 
						marginBottom: "8px",
						color: "var(--gray-11)",
						letterSpacing: "0.025em"
					}}>
						{isHero && heroPart === 'button' ? 'Button Background' : 'Background'}
					</label>
					<div style={{ 
						position: "relative",
						background: "var(--gray-1)",
						border: "1px solid var(--gray-6)",
						borderRadius: "12px",
						padding: "4px",
						boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)"
					}}>
						<input
							type="color"
							value={(() => {
								if (isHero && heroPart === 'button') {
									const bgColor = element.properties.buttonBackgroundColor || element.properties.backgroundColor;
									return typeof bgColor === 'string' ? bgColor : "#3b82f6";
								}
								const bgColor = element.properties.backgroundColor;
								return typeof bgColor === 'string' ? bgColor : "#ffffff";
							})()}
							onChange={(e) => handlePropertyChange("backgroundColor", e.target.value)}
							style={{ 
								width: "100%", 
								height: "40px", 
								border: "none", 
								borderRadius: "8px", 
								cursor: "pointer",
								background: "transparent",
								outline: "none",
								transition: "all 0.2s ease"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.transform = "scale(1.02)";
								e.currentTarget.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.transform = "scale(1)";
								e.currentTarget.style.boxShadow = "none";
							}}
						/>
					</div>
				</div>

				{/* Visibility */}
				<div>
					<label style={{ 
						display: "flex", 
						alignItems: "center", 
						gap: "12px", 
						fontSize: "12px",
						fontWeight: "600",
						color: "var(--gray-11)",
						cursor: "pointer",
						padding: "8px",
						borderRadius: "8px",
						transition: "background-color 0.2s ease"
					}}
					onMouseEnter={(e) => {
						e.currentTarget.style.backgroundColor = "var(--gray-3)";
					}}
					onMouseLeave={(e) => {
						e.currentTarget.style.backgroundColor = "transparent";
					}}
					>
						<input
							type="checkbox"
							checked={isHero && heroPart ? 
								element.properties[`${heroPart}Visible`] !== false :
								element.properties.visible !== false
							}
							onChange={(e) => handlePropertyChange("visible", e.target.checked)}
							style={{ 
								margin: 0,
								width: "16px",
								height: "16px",
								accentColor: "var(--blue-9)",
								cursor: "pointer"
							}}
						/>
						Visible
					</label>
				</div>

				{/* Video Upload Section - Only show for video elements */}
				{(element.type === "video" || context?.heroPart === "video") && (
					<div>
						<label style={{ 
							display: "block", 
							fontSize: "12px", 
							fontWeight: "600", 
							marginBottom: "8px",
							color: "var(--gray-11)",
							letterSpacing: "0.025em"
						}}>
							Video Upload
						</label>
						<VideoUpload
							currentVideoUrl={(() => {
								if (context?.heroPart === "video") {
									return element.properties.videoUrl as string || "";
								}
								return element.properties.videoUrl as string || "";
							})()}
							onVideoUploaded={(videoUrl) => handlePropertyChange("videoUrl", videoUrl)}
						/>
					</div>
				)}

				{/* Video Controls - Only show for video elements */}
				{(element.type === "video" || context?.heroPart === "video") && (
					<>
						{/* Autoplay */}
						<div>
							<label style={{ 
								display: "flex", 
								alignItems: "center", 
								gap: "12px", 
								fontSize: "12px",
								fontWeight: "600",
								color: "var(--gray-11)",
								cursor: "pointer",
								padding: "8px",
								borderRadius: "8px",
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor = "var(--gray-3)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor = "transparent";
							}}
							>
								<input
									type="checkbox"
									checked={element.properties.autoplay === true}
									onChange={(e) => handlePropertyChange("autoplay", e.target.checked)}
									style={{ 
										margin: 0,
										width: "16px",
										height: "16px",
										accentColor: "var(--blue-9)",
										cursor: "pointer"
									}}
								/>
								Autoplay
							</label>
						</div>

						{/* Controls */}
						<div>
							<label style={{ 
								display: "flex", 
								alignItems: "center", 
								gap: "12px", 
								fontSize: "12px",
								fontWeight: "600",
								color: "var(--gray-11)",
								cursor: "pointer",
								padding: "8px",
								borderRadius: "8px",
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor = "var(--gray-3)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor = "transparent";
							}}
							>
								<input
									type="checkbox"
									checked={element.properties.controls !== false}
									onChange={(e) => handlePropertyChange("controls", e.target.checked)}
									style={{ 
										margin: 0,
										width: "16px",
										height: "16px",
										accentColor: "var(--blue-9)",
										cursor: "pointer"
									}}
								/>
								Show Controls
							</label>
						</div>

						{/* Loop */}
						<div>
							<label style={{ 
								display: "flex", 
								alignItems: "center", 
								gap: "12px", 
								fontSize: "12px",
								fontWeight: "600",
								color: "var(--gray-11)",
								cursor: "pointer",
								padding: "8px",
								borderRadius: "8px",
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor = "var(--gray-3)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor = "transparent";
							}}
							>
								<input
									type="checkbox"
									checked={element.properties.loop === true}
									onChange={(e) => handlePropertyChange("loop", e.target.checked)}
									style={{ 
										margin: 0,
										width: "16px",
										height: "16px",
										accentColor: "var(--blue-9)",
										cursor: "pointer"
									}}
								/>
								Loop Video
							</label>
						</div>

						{/* Muted */}
						<div>
							<label style={{ 
								display: "flex", 
								alignItems: "center", 
								gap: "12px", 
								fontSize: "12px",
								fontWeight: "600",
								color: "var(--gray-11)",
								cursor: "pointer",
								padding: "8px",
								borderRadius: "8px",
								transition: "background-color 0.2s ease"
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor = "var(--gray-3)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor = "transparent";
							}}
							>
								<input
									type="checkbox"
									checked={element.properties.muted === true}
									onChange={(e) => handlePropertyChange("muted", e.target.checked)}
									style={{ 
										margin: 0,
										width: "16px",
										height: "16px",
										accentColor: "var(--blue-9)",
										cursor: "pointer"
									}}
								/>
								Muted
							</label>
						</div>
					</>
				)}

				{/* Delete Button */}
				<button
					onClick={() => {
						if (isHero && heroPart) {
							// For hero parts, hide the specific part instead of deleting the entire element
							handlePropertyChange("visible", false);
						} else {
							// For other elements, delete the entire element
							onDelete();
						}
					}}
					style={{ 
						width: "100%", 
						padding: "12px 16px", 
						background: "var(--red-9)", 
						color: "var(--red-1)", 
						border: "none", 
						borderRadius: "10px", 
						fontSize: "12px", 
						fontWeight: "600",
						cursor: "pointer",
						transition: "all 0.2s ease",
						boxShadow: "0 2px 4px rgba(239, 68, 68, 0.2)"
					}}
					onMouseEnter={(e) => {
						e.currentTarget.style.background = "var(--red-10)";
						e.currentTarget.style.transform = "translateY(-1px)";
						e.currentTarget.style.boxShadow = "0 4px 8px rgba(239, 68, 68, 0.3)";
					}}
					onMouseLeave={(e) => {
						e.currentTarget.style.background = "var(--red-9)";
						e.currentTarget.style.transform = "translateY(0)";
						e.currentTarget.style.boxShadow = "0 2px 4px rgba(239, 68, 68, 0.2)";
					}}
				>
					{isHero && heroPart ? `Hide ${heroPart}` : "Delete Element"}
				</button>
			</div>
		);
	}, [handlePropertyChange, onDelete, element, heroPart, isHero]);

	return (
		<div
			ref={tooltipRef}
			style={tooltipStyle}
		>
			{/* Header */}
			<div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: "12px" }}>
				<Text size="2" weight="bold" style={{ textTransform: "capitalize" }}>
					{element.type.replace(/_/g, " ")}
				</Text>
				<button
					onClick={onClose}
					style={{
						background: "none",
						border: "none",
						color: "#9ca3af",
						fontSize: "18px",
						cursor: "pointer",
						padding: "0",
						lineHeight: 1,
					}}
				>
					×
				</button>
			</div>

			{/* Tabs */}
			<div style={{ 
				display: "flex", 
				gap: "2px", 
				marginBottom: "16px", 
				background: "var(--gray-3)", 
				padding: "4px", 
				borderRadius: "12px" 
			}}>
				<button
					onClick={() => setActiveTab("text")}
					style={{
						flex: 1,
						padding: "8px 12px",
						fontSize: "12px",
						fontWeight: "600",
						borderRadius: "8px",
						border: "none",
						background: activeTab === "text" ? "var(--gray-1)" : "transparent",
						boxShadow: activeTab === "text" ? "0 2px 4px rgba(0, 0, 0, 0.1)" : "none",
						color: activeTab === "text" ? "var(--gray-12)" : "var(--gray-9)",
						cursor: "pointer",
						transition: "all 0.2s ease"
					}}
				>
					Text
				</button>
				<button
					onClick={() => setActiveTab("element")}
					style={{
						flex: 1,
						padding: "8px 12px",
						fontSize: "12px",
						fontWeight: "600",
						borderRadius: "8px",
						border: "none",
						background: activeTab === "element" ? "var(--gray-1)" : "transparent",
						boxShadow: activeTab === "element" ? "0 2px 4px rgba(0, 0, 0, 0.1)" : "none",
						color: activeTab === "element" ? "var(--gray-12)" : "var(--gray-9)",
						cursor: "pointer",
						transition: "all 0.2s ease"
					}}
				>
					Element
				</button>
			</div>

			{/* Content */}
			{activeTab === "text" ? renderTextControls() : renderElementControls()}
		</div>
	);
}
