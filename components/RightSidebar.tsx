"use client";

import { Button, Heading, Text } from "frosted-ui";
import { useState, useEffect } from "react";

// Features List Editor Component
interface Feature {
	title: string;
	description: string;
	icon?: string;
	iconUrl?: string;
	ctaText?: string;
	ctaUrl?: string;
}

interface FeaturesListEditorProps {
	features: Feature[];
	onUpdateFeatures: (features: Feature[]) => void;
}

function FeaturesListEditor({ features, onUpdateFeatures }: FeaturesListEditorProps) {
	const [expandedFeature, setExpandedFeature] = useState<number | null>(0);

	const addFeature = () => {
		onUpdateFeatures([
			...features,
			{
				title: "New Feature",
				description: "Feature description",
				icon: "⭐",
				iconUrl: "",
				ctaText: "",
				ctaUrl: "",
			},
		]);
		setExpandedFeature(features.length);
	};

	const updateFeature = (index: number, field: string, value: unknown) => {
		const newFeatures = [...features];
		newFeatures[index] = { ...newFeatures[index], [field]: value };
		onUpdateFeatures(newFeatures);
	};

	const deleteFeature = (index: number) => {
		onUpdateFeatures(features.filter((_, i) => i !== index));
		setExpandedFeature(null);
	};

	return (
		<div style={{ marginBottom: "16px" }}>
			<div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "12px" }}>
				<Text size="2" weight="medium">
					Features
				</Text>
				<Button size="1" onClick={addFeature}>+ Add Feature</Button>
			</div>
			<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
				{features.map((feature, index) => (
					<div
						key={index}
						style={{
							border: "1px solid var(--gray-6)",
							borderRadius: "8px",
							overflow: "hidden",
							background: "var(--gray-1)",
						}}
					>
						<div
							style={{
								padding: "12px",
								background: "var(--gray-2)",
								cursor: "pointer",
								display: "flex",
								justifyContent: "space-between",
								alignItems: "center",
							}}
							onClick={() => setExpandedFeature(expandedFeature === index ? null : index)}
						>
							<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
								<span style={{ fontSize: "20px" }}>{feature.icon || "⭐"}</span>
								<Text size="2" weight="bold">{feature.title}</Text>
							</div>
							<svg
								width="16"
								height="16"
								viewBox="0 0 24 24"
								style={{
									transform: expandedFeature === index ? "rotate(180deg)" : "rotate(0deg)",
									transition: "transform 0.2s",
								}}
							>
								<path
									d="M6 9l6 6 6-6"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
									fill="none"
								/>
							</svg>
						</div>
						{expandedFeature === index && (
							<div style={{ padding: "12px", display: "flex", flexDirection: "column", gap: "12px" }}>
								<div>
									<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Feature Title</Text>
									<input
										type="text"
										value={feature.title}
										onChange={(e) => updateFeature(index, "title", e.target.value)}
										style={{
											width: "100%",
											padding: "6px 8px",
											border: "1px solid var(--gray-7)",
											borderRadius: "4px",
											fontSize: "13px",
										}}
									/>
								</div>
								<div>
									<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Description</Text>
									<textarea
										value={feature.description}
										onChange={(e) => updateFeature(index, "description", e.target.value)}
										rows={3}
										style={{
											width: "100%",
											padding: "6px 8px",
											border: "1px solid var(--gray-7)",
											borderRadius: "4px",
											fontSize: "13px",
											resize: "vertical",
										}}
									/>
								</div>
								<div>
									<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Icon (emoji or text)</Text>
									<input
										type="text"
										value={feature.icon || ""}
										onChange={(e) => updateFeature(index, "icon", e.target.value)}
										placeholder="⭐"
										style={{
											width: "100%",
											padding: "6px 8px",
											border: "1px solid var(--gray-7)",
											borderRadius: "4px",
											fontSize: "13px",
										}}
									/>
								</div>
								<div>
									<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Icon URL (optional)</Text>
									<input
										type="text"
										value={feature.iconUrl || ""}
										onChange={(e) => updateFeature(index, "iconUrl", e.target.value)}
										placeholder="https://example.com/icon.svg"
										style={{
											width: "100%",
											padding: "6px 8px",
											border: "1px solid var(--gray-7)",
											borderRadius: "4px",
											fontSize: "13px",
										}}
									/>
								</div>
								<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>CTA Text</Text>
										<input
											type="text"
											value={feature.ctaText || ""}
											onChange={(e) => updateFeature(index, "ctaText", e.target.value)}
											placeholder="Learn more"
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>CTA URL</Text>
										<input
											type="text"
											value={feature.ctaUrl || ""}
											onChange={(e) => updateFeature(index, "ctaUrl", e.target.value)}
											placeholder="#"
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
								</div>
								<Button size="1" color="red" variant="soft" onClick={() => deleteFeature(index)}>
									Delete Feature
								</Button>
							</div>
						)}
					</div>
				))}
			</div>
		</div>
	);
}

interface ElementProperties {
	padding?: { top: number; right: number; bottom: number; left: number };
	textColor?: string;
	textAlign?: string;
	visible?: boolean;
	animation?: string;
	fontSize?: number | string;
	backgroundColor?: string;
	imageUrl?: string;
	altText?: string;
	linkUrl?: string;
	actionType?: string;
	inputType?: string;
	placeholder?: string;
	content?: string;
	// Section-specific properties
	height?: string;
	width?: string;
	maxWidth?: string;
	margin?: { top: number; right: number; bottom: number; left: number };
	borderRadius?: number;
	gradientStart?: string;
	gradientEnd?: string;
	useGradient?: boolean;
	// Hero section
	title?: string;
	subtitle?: string;
	ctaText?: string;
	ctaUrl?: string;
	secondaryCtaText?: string;
	secondaryCtaUrl?: string;
	backgroundImage?: string;
	overlay?: boolean;
	overlayOpacity?: number;
	// Features section
	featureCount?: number;
	subheading?: string;
	features?: Array<{ 
		title: string; 
		description: string; 
		icon?: string;
		iconUrl?: string;
		ctaText?: string;
		ctaUrl?: string;
	}>;
	// Features styling
	columns?: number;
	featureBackground?: string;
	featureBorderColor?: string;
	featureBorderRadius?: number;
	featureGap?: number;
	iconBackground?: string;
	iconSize?: number;
	featureTitleColor?: string;
	featureDescriptionColor?: string;
	ctaBackground?: string;
	ctaTextColor?: string;
	// Pricing section
	pricingPlans?: Array<{ name: string; price: string; features: string[]; highlighted?: boolean }>;
	// Testimonials section
	testimonials?: Array<{ quote: string; author: string; role?: string; avatar?: string }>;
	// Form section
	formFields?: Array<{ label: string; type: string; required?: boolean }>;
	submitButtonText?: string;
	formAction?: string;
	[key: string]: unknown;
}

interface RightSidebarProps {
	selectedElement?: {
		id: string;
		type: string;
		name: string;
		content: string;
		properties: ElementProperties;
	} | null;
	onUpdateElement?: (properties: ElementProperties) => void;
	onCloseEditor?: () => void;
	userId?: string;
	companyId?: string;
}

type TabType = "content" | "design" | "actions";

export function RightSidebar({ selectedElement, onUpdateElement, onCloseEditor, userId, companyId }: RightSidebarProps) {
	const [activeTab, setActiveTab] = useState<TabType>("content");
	const [templateName, setTemplateName] = useState("");
	const [showSaveTemplate, setShowSaveTemplate] = useState(false);
	const [showLoadTemplate, setShowLoadTemplate] = useState(false);
	const [savedTemplates, setSavedTemplates] = useState<Array<{id: string, name: string, properties: ElementProperties}>>([]);


	// Load templates from Supabase on component mount
	useEffect(() => {
		const loadTemplates = async () => {
			if (!companyId) return;
			
			try {
				const response = await fetch(`/api/templates?companyId=${companyId}`);
				const data = await response.json();
				
				if (data.success) {
					setSavedTemplates(data.templates);
				}
			} catch (error) {
				console.error('Error loading templates:', error);
			}
		};
		
		loadTemplates();
	}, [companyId]);

	// Template management functions
	const saveTemplate = async () => {
		if (!templateName.trim() || !selectedElement || !userId || !companyId) return;
		
		try {
			const response = await fetch('/api/templates', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					userId,
					companyId,
					name: templateName.trim(),
					properties: selectedElement.properties,
				}),
			});
			
			const data = await response.json();
			
			if (data.success) {
				setSavedTemplates([...savedTemplates, data.template]);
				setTemplateName("");
				setShowSaveTemplate(false);
			}
		} catch (error) {
			console.error('Error saving template:', error);
		}
	};

	const loadTemplate = (template: {id: string, name: string, properties: ElementProperties}) => {
		if (!selectedElement) return;
		onUpdateElement?.(template.properties);
		setShowLoadTemplate(false);
	};

	const deleteTemplate = async (templateId: string) => {
		try {
			const response = await fetch(`/api/templates/${templateId}`, {
				method: 'DELETE',
			});
			
			const data = await response.json();
			
			if (data.success) {
				setSavedTemplates(savedTemplates.filter(t => t.id !== templateId));
			}
		} catch (error) {
			console.error('Error deleting template:', error);
		}
	};

	const getIcon = (iconName: string) => {
		const iconProps = {
			width: 16,
			height: 16,
			viewBox: "0 0 24 24",
			fill: "none",
			stroke: "currentColor",
			strokeWidth: 2,
			strokeLinecap: "round" as const,
			strokeLinejoin: "round" as const,
		};

		switch (iconName) {
			case "content":
				return (
					<svg {...iconProps}>
						<path d="M4 7V4h16v3" />
						<path d="M9 20h6" />
						<path d="M12 4v16" />
					</svg>
				);
			case "design":
				return (
					<svg {...iconProps}>
						<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
					</svg>
				);
			case "actions":
				return (
					<svg {...iconProps}>
						<path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
						<path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
					</svg>
				);
			default:
				return (
					<svg {...iconProps}>
						<circle cx="12" cy="12" r="3" />
					</svg>
				);
		}
	};

	const tabs = [
		{ id: "content" as TabType, label: "Content", icon: "content" },
		{ id: "design" as TabType, label: "Design", icon: "design" },
		{ id: "actions" as TabType, label: "Actions", icon: "actions" },
	];

	const renderContent = () => {
		if (!selectedElement) return null;

		return (
			<div className="flex flex-col gap-4 p-4">
				{/* Hero Section Content */}
				{selectedElement.type === "hero" && (
					<>
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Main Title
							</Text>
							<input
								type="text"
								value={String(selectedElement.properties.title || "Transform Your Business Today")}
								onChange={(e) => onUpdateElement?.({ title: e.target.value })}
								placeholder="Enter hero title..."
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Subtitle
							</Text>
							<textarea
								value={String(selectedElement.properties.subtitle || "The all-in-one solution to grow your revenue")}
								onChange={(e) => onUpdateElement?.({ subtitle: e.target.value })}
								placeholder="Enter subtitle..."
								style={{
									width: "100%",
									minHeight: "60px",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
									resize: "vertical",
								}}
							/>
						</div>
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Primary CTA Text
							</Text>
							<input
								type="text"
								value={String(selectedElement.properties.ctaText || "Get Started")}
								onChange={(e) => onUpdateElement?.({ ctaText: e.target.value })}
								placeholder="Get Started"
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Primary CTA URL
							</Text>
							<input
								type="url"
								value={String(selectedElement.properties.ctaUrl || "#")}
								onChange={(e) => onUpdateElement?.({ ctaUrl: e.target.value })}
								placeholder="https://example.com"
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Background Image URL
							</Text>
							<input
								type="url"
								value={String(selectedElement.properties.backgroundImage || "")}
								onChange={(e) => onUpdateElement?.({ backgroundImage: e.target.value })}
								placeholder="https://example.com/image.jpg"
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>
					</>
				)}

				{/* Features Section Content */}
				{selectedElement.type === "features" && (
					<>
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Section Heading
						</Text>
						<input
							type="text"
							value={selectedElement.content || "Powerful Features"}
							onChange={(e) => onUpdateElement?.({ content: e.target.value })}
							placeholder="Features heading..."
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						/>
					</div>
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Subheading
							</Text>
							<input
								type="text"
								value={String(selectedElement.properties.subheading || "Everything you need to succeed")}
								onChange={(e) => onUpdateElement?.({ subheading: e.target.value })}
								placeholder="Features subheading..."
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>
						{/* Features List Editor */}
						<FeaturesListEditor
							features={selectedElement.properties.features || []}
							onUpdateFeatures={(features) => onUpdateElement?.({ features })}
						/>

						{/* AI Notice */}
						<div style={{ marginTop: "24px", paddingTop: "24px", borderTop: "1px solid var(--gray-6)" }}>
							<Text size="2" weight="bold" style={{ marginBottom: "16px" }}>
								AI Assistant
							</Text>
							<div style={{
								background: "var(--gray-1)",
								border: "1px solid var(--gray-6)", 
								borderRadius: "8px", 
								padding: "16px",
								textAlign: "center"
							}}>
								<Text size="1" color="gray" style={{ marginBottom: "8px", display: "block" }}>
									🤖 Use the AI chat bar at the bottom of the screen to modify your entire funnel with natural language commands.
								</Text>
								<Text size="1" color="gray" style={{ fontSize: "12px" }}>
									Examples: &quot;Make all text blue&quot;, &quot;Change to dark theme&quot;, &quot;Add new features&quot;
								</Text>
							</div>
						</div>
					</>
				)}

				{/* Pricing Section Content */}
				{selectedElement.type === "pricing" && (
					<div className="space-y-4">
						{/* Pricing Title */}
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Pricing Title
						</Text>
						<input
							type="text"
								value={selectedElement.content || "Pricing Plans"}
							onChange={(e) => onUpdateElement?.({ content: e.target.value })}
								placeholder="Pricing title..."
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						/>
						</div>

						{/* Pricing Subtitle */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Pricing Subtitle
							</Text>
							<input
								type="text"
								value={selectedElement.properties.subtitle || "Choose the plan that's right for you"}
								onChange={(e) => onUpdateElement?.({ subtitle: e.target.value })}
								placeholder="Pricing subtitle..."
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>

						{/* Button Text */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Button Text
							</Text>
							<input
								type="text"
								value={String(selectedElement.properties.buttonText || "Get Started")}
								onChange={(e) => onUpdateElement?.({ buttonText: e.target.value })}
								placeholder="Button text..."
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>

						{/* Columns */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Number of Columns
							</Text>
							<select
								value={selectedElement.properties.columns || 3}
								onChange={(e) => onUpdateElement?.({ columns: parseInt(e.target.value) })}
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							>
								<option value={1}>1 Column</option>
								<option value={2}>2 Columns</option>
								<option value={3}>3 Columns</option>
								<option value={4}>4 Columns</option>
							</select>
						</div>

						{/* Price Color */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Price Color
							</Text>
							<div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
								<input
									type="color"
									value={String(selectedElement.properties.priceColor || "#667eea")}
									onChange={(e) => onUpdateElement?.({ priceColor: e.target.value })}
									style={{
										width: "40px",
										height: "40px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										cursor: "pointer",
									}}
								/>
								<input
									type="text"
									value={String(selectedElement.properties.priceColor || "#667eea")}
									onChange={(e) => onUpdateElement?.({ priceColor: e.target.value })}
									placeholder="#667eea"
									style={{
										flex: 1,
										padding: "8px 12px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										background: "var(--gray-1)",
										fontSize: "14px",
									}}
								/>
							</div>
						</div>

						{/* Popular Plan Color */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Popular Plan Color
							</Text>
							<div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
								<input
									type="color"
									value={String(selectedElement.properties.popularPlanColor || "#667eea")}
									onChange={(e) => onUpdateElement?.({ popularPlanColor: e.target.value })}
									style={{
										width: "40px",
										height: "40px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										cursor: "pointer",
									}}
								/>
								<input
									type="text"
									value={String(selectedElement.properties.popularPlanColor || "#667eea")}
									onChange={(e) => onUpdateElement?.({ popularPlanColor: e.target.value })}
									placeholder="#667eea"
									style={{
										flex: 1,
										padding: "8px 12px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										background: "var(--gray-1)",
										fontSize: "14px",
									}}
								/>
							</div>
						</div>

						{/* Plan Background Color */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Plan Background Color
							</Text>
							<div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
								<input
									type="color"
									value={String(selectedElement.properties.planBackground || "#ffffff")}
									onChange={(e) => onUpdateElement?.({ planBackground: e.target.value })}
									style={{
										width: "40px",
										height: "40px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										cursor: "pointer",
									}}
								/>
								<input
									type="text"
									value={String(selectedElement.properties.planBackground || "#ffffff")}
									onChange={(e) => onUpdateElement?.({ planBackground: e.target.value })}
									placeholder="#ffffff"
									style={{
										flex: 1,
										padding: "8px 12px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										background: "var(--gray-1)",
										fontSize: "14px",
									}}
								/>
							</div>
						</div>

						{/* Button Background Color */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Button Background Color
							</Text>
							<div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
								<input
									type="color"
									value={String(selectedElement.properties.buttonBackground || "#f3f4f6")}
									onChange={(e) => onUpdateElement?.({ buttonBackground: e.target.value })}
									style={{
										width: "40px",
										height: "40px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										cursor: "pointer",
									}}
								/>
								<input
									type="text"
									value={String(selectedElement.properties.buttonBackground || "#f3f4f6")}
									onChange={(e) => onUpdateElement?.({ buttonBackground: e.target.value })}
									placeholder="#f3f4f6"
									style={{
										flex: 1,
										padding: "8px 12px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										background: "var(--gray-1)",
										fontSize: "14px",
									}}
								/>
							</div>
						</div>

						{/* Button Text Color */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Button Text Color
							</Text>
							<div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
								<input
									type="color"
									value={String(selectedElement.properties.buttonTextColor || "#374151")}
									onChange={(e) => onUpdateElement?.({ buttonTextColor: e.target.value })}
									style={{
										width: "40px",
										height: "40px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										cursor: "pointer",
									}}
								/>
								<input
									type="text"
									value={String(selectedElement.properties.buttonTextColor || "#374151")}
									onChange={(e) => onUpdateElement?.({ buttonTextColor: e.target.value })}
									placeholder="#374151"
									style={{
										flex: 1,
										padding: "8px 12px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										background: "var(--gray-1)",
										fontSize: "14px",
									}}
								/>
							</div>
						</div>

						{/* Feature Text Color */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Feature Text Color
							</Text>
							<div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
								<input
									type="color"
									value={String(selectedElement.properties.featureColor || "#374151")}
									onChange={(e) => onUpdateElement?.({ featureColor: e.target.value })}
									style={{
										width: "40px",
										height: "40px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										cursor: "pointer",
									}}
								/>
								<input
									type="text"
									value={String(selectedElement.properties.featureColor || "#374151")}
									onChange={(e) => onUpdateElement?.({ featureColor: e.target.value })}
									placeholder="#374151"
									style={{
										flex: 1,
										padding: "8px 12px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										background: "var(--gray-1)",
										fontSize: "14px",
									}}
								/>
							</div>
						</div>

						{/* Plan Border Radius */}
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Plan Border Radius
							</Text>
							<input
								type="number"
								value={Number(selectedElement.properties.planBorderRadius || 16)}
								onChange={(e) => onUpdateElement?.({ planBorderRadius: parseInt(e.target.value) })}
								placeholder="16"
								min="0"
								max="50"
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>
					</div>
				)}

				{/* Testimonials Section Content */}
				{selectedElement.type === "testimonials" && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Section Heading
						</Text>
						<input
							type="text"
							value={selectedElement.content || "What Our Customers Say"}
							onChange={(e) => onUpdateElement?.({ content: e.target.value })}
							placeholder="Testimonials heading..."
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						/>
					</div>
				)}

				{/* Form Section Content */}
				{selectedElement.type === "form" && (
					<>
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Form Heading
							</Text>
							<input
								type="text"
								value={selectedElement.content || "Get Started Today"}
								onChange={(e) => onUpdateElement?.({ content: e.target.value })}
								placeholder="Form heading..."
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Submit Button Text
							</Text>
							<input
								type="text"
								value={selectedElement.properties.submitButtonText || "Submit"}
								onChange={(e) => onUpdateElement?.({ submitButtonText: e.target.value })}
								placeholder="Submit"
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>
						<div>
							<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
								Form Action URL
							</Text>
							<input
								type="url"
								value={selectedElement.properties.formAction || ""}
								onChange={(e) => onUpdateElement?.({ formAction: e.target.value })}
								placeholder="https://example.com/submit"
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							/>
						</div>
					</>
				)}

				{/* Footer Section Content */}
				{selectedElement.type === "footer" && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Copyright Text
						</Text>
						<input
							type="text"
							value={selectedElement.content || "© 2024 Your Company. All rights reserved."}
							onChange={(e) => onUpdateElement?.({ content: e.target.value })}
							placeholder="Copyright text..."
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						/>
					</div>
				)}

				{/* Text Content */}
				{(selectedElement.type === "heading" || selectedElement.type === "text" || selectedElement.type === "button") && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Text Content
						</Text>
						<textarea
							value={selectedElement.content}
							onChange={(e) => onUpdateElement?.({ content: e.target.value })}
							placeholder="Enter text content..."
							style={{
								width: "100%",
								minHeight: "80px",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
								resize: "vertical",
							}}
						/>
					</div>
				)}

				{/* Image URL */}
				{selectedElement.type === "image" && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Image URL
						</Text>
						<input
							type="url"
							value={selectedElement.properties.imageUrl || ""}
							onChange={(e) => onUpdateElement?.({ imageUrl: e.target.value })}
							placeholder="https://example.com/image.jpg"
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						/>
					</div>
				)}

				{/* Alt Text */}
				{selectedElement.type === "image" && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Alt Text
						</Text>
						<input
							type="text"
							value={selectedElement.properties.altText || ""}
							onChange={(e) => onUpdateElement?.({ altText: e.target.value })}
							placeholder="Describe the image..."
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						/>
					</div>
				)}

				{/* Button Link */}
				{selectedElement.type === "button" && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Link URL
						</Text>
						<input
							type="url"
							value={selectedElement.properties.linkUrl || ""}
							onChange={(e) => onUpdateElement?.({ linkUrl: e.target.value })}
							placeholder="https://example.com"
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						/>
					</div>
				)}
			</div>
		);
	};

	const renderDesign = () => {
		if (!selectedElement) return null;

		const isSectionType = ["hero", "features", "pricing", "testimonials", "form", "footer"].includes(selectedElement.type);

		return (
			<div className="flex flex-col gap-4 p-4">
				{/* Section Height (for sections only) */}
				{isSectionType && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Section Height
						</Text>
						<input
							type="text"
							value={selectedElement.properties.height || "auto"}
							onChange={(e) => onUpdateElement?.({ height: e.target.value })}
							placeholder="auto, 600px, 100vh"
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						/>
						<Text size="1" color="gray" style={{ marginTop: "4px" }}>
							Examples: auto, 600px, 100vh
						</Text>
					</div>
				)}

				{/* Max Width (for sections) */}
				{isSectionType && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Max Width
						</Text>
						<input
							type="text"
							value={selectedElement.properties.maxWidth || "1200px"}
							onChange={(e) => onUpdateElement?.({ maxWidth: e.target.value })}
							placeholder="1200px, 100%"
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						/>
					</div>
				)}

				{/* Padding */}
				<div>
					<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
						Padding
					</Text>
					<div className="flex gap-2">
						<input
							type="number"
							value={selectedElement.properties.padding?.top || 0}
							onChange={(e) => onUpdateElement?.({
								padding: { 
									top: parseInt(e.target.value) || 0,
									right: selectedElement.properties.padding?.right || 0,
									bottom: selectedElement.properties.padding?.bottom || 0,
									left: selectedElement.properties.padding?.left || 0
								}
							})}
							placeholder="Top"
							style={{
								width: "60px",
								padding: "6px 8px",
								border: "1px solid var(--gray-7)",
								borderRadius: "4px",
								background: "var(--gray-1)",
								fontSize: "12px",
								textAlign: "center",
							}}
						/>
						<input
							type="number"
							value={selectedElement.properties.padding?.right || 0}
							onChange={(e) => onUpdateElement?.({
								padding: { 
									top: selectedElement.properties.padding?.top || 0,
									right: parseInt(e.target.value) || 0,
									bottom: selectedElement.properties.padding?.bottom || 0,
									left: selectedElement.properties.padding?.left || 0
								}
							})}
							placeholder="Right"
							style={{
								width: "60px",
								padding: "6px 8px",
								border: "1px solid var(--gray-7)",
								borderRadius: "4px",
								background: "var(--gray-1)",
								fontSize: "12px",
								textAlign: "center",
							}}
						/>
						<input
							type="number"
							value={selectedElement.properties.padding?.bottom || 0}
							onChange={(e) => onUpdateElement?.({
								padding: { 
									top: selectedElement.properties.padding?.top || 0,
									right: selectedElement.properties.padding?.right || 0,
									bottom: parseInt(e.target.value) || 0,
									left: selectedElement.properties.padding?.left || 0
								}
							})}
							placeholder="Bottom"
							style={{
								width: "60px",
								padding: "6px 8px",
								border: "1px solid var(--gray-7)",
								borderRadius: "4px",
								background: "var(--gray-1)",
								fontSize: "12px",
								textAlign: "center",
							}}
						/>
						<input
							type="number"
							value={selectedElement.properties.padding?.left || 0}
							onChange={(e) => onUpdateElement?.({
								padding: { 
									top: selectedElement.properties.padding?.top || 0,
									right: selectedElement.properties.padding?.right || 0,
									bottom: selectedElement.properties.padding?.bottom || 0,
									left: parseInt(e.target.value) || 0
								}
							})}
							placeholder="Left"
							style={{
								width: "60px",
								padding: "6px 8px",
								border: "1px solid var(--gray-7)",
								borderRadius: "4px",
								background: "var(--gray-1)",
								fontSize: "12px",
								textAlign: "center",
							}}
						/>
					</div>
				</div>

				{/* Background Color */}
				<div>
					<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
						Background Color
					</Text>
					<input
						type="color"
						value={selectedElement.properties.backgroundColor || "#ffffff"}
						onChange={(e) => onUpdateElement?.({ backgroundColor: e.target.value })}
						style={{
							width: "100%",
							height: "36px",
							border: "1px solid var(--gray-7)",
							borderRadius: "6px",
							background: "var(--gray-1)",
							cursor: "pointer",
						}}
					/>
				</div>

				{/* Text Color */}
				<div>
					<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
						Text Color
					</Text>
					<input
						type="color"
						value={selectedElement.properties.textColor || "#000000"}
						onChange={(e) => onUpdateElement?.({ textColor: e.target.value })}
						style={{
							width: "100%",
							height: "36px",
							border: "1px solid var(--gray-7)",
							borderRadius: "6px",
							background: "var(--gray-1)",
							cursor: "pointer",
						}}
					/>
				</div>

				{/* Gradient Options (for sections) */}
				{isSectionType && (
					<>
						<div className="flex items-center gap-2">
							<input
								type="checkbox"
								id="useGradient"
								checked={selectedElement.properties.useGradient || false}
								onChange={(e) => onUpdateElement?.({ useGradient: e.target.checked })}
								style={{
									width: "16px",
									height: "16px",
									cursor: "pointer",
								}}
							/>
							<label htmlFor="useGradient" style={{ fontSize: "14px", cursor: "pointer" }}>
								Use Gradient Background
							</label>
						</div>
						{selectedElement.properties.useGradient && (
							<>
								<div>
									<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
										Gradient Start Color
									</Text>
									<input
										type="color"
										value={selectedElement.properties.gradientStart || "#667eea"}
										onChange={(e) => onUpdateElement?.({ gradientStart: e.target.value })}
										style={{
											width: "100%",
											height: "36px",
											border: "1px solid var(--gray-7)",
											borderRadius: "6px",
											background: "var(--gray-1)",
											cursor: "pointer",
										}}
									/>
								</div>
								<div>
									<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
										Gradient End Color
									</Text>
									<input
										type="color"
										value={selectedElement.properties.gradientEnd || "#764ba2"}
										onChange={(e) => onUpdateElement?.({ gradientEnd: e.target.value })}
										style={{
											width: "100%",
											height: "36px",
											border: "1px solid var(--gray-7)",
											borderRadius: "6px",
											background: "var(--gray-1)",
											cursor: "pointer",
										}}
									/>
								</div>
							</>
						)}
					</>
				)}

				{/* Border Radius (for sections) */}
				{isSectionType && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Border Radius (px)
						</Text>
						<input
							type="range"
							min="0"
							max="50"
							value={selectedElement.properties.borderRadius || 0}
							onChange={(e) => onUpdateElement?.({ borderRadius: parseInt(e.target.value) })}
							style={{
								width: "100%",
								height: "6px",
								borderRadius: "3px",
								background: "var(--gray-3)",
								outline: "none",
								cursor: "pointer",
							}}
						/>
						<div className="flex justify-between mt-1">
							<Text size="1" color="gray">0px</Text>
							<Text size="1" color="gray">{selectedElement.properties.borderRadius || 0}px</Text>
							<Text size="1" color="gray">50px</Text>
						</div>
					</div>
				)}

				{selectedElement.type === "button" && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Button Color
						</Text>
						<input
							type="color"
							value={selectedElement.properties.backgroundColor || "#3b82f6"}
							onChange={(e) => onUpdateElement?.({ backgroundColor: e.target.value })}
							style={{
								width: "100%",
								height: "36px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								cursor: "pointer",
							}}
						/>
					</div>
				)}

				{/* Font Size */}
				{(selectedElement.type === "heading" || selectedElement.type === "text") && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Font Size
						</Text>
						<input
							type="range"
							min="12"
							max="72"
							value={selectedElement.properties.fontSize || 16}
							onChange={(e) => onUpdateElement?.({ fontSize: parseInt(e.target.value) })}
							style={{
								width: "100%",
								height: "6px",
								borderRadius: "3px",
								background: "var(--gray-3)",
								outline: "none",
								cursor: "pointer",
							}}
						/>
						<div className="flex justify-between mt-1">
							<Text size="1" color="gray">12px</Text>
							<Text size="1" color="gray">{selectedElement.properties.fontSize || 16}px</Text>
							<Text size="1" color="gray">72px</Text>
						</div>
					</div>
				)}

				{/* Alignment */}
				<div>
					<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
						Text Alignment
					</Text>
					<div className="flex gap-1">
						{["left", "center", "right"].map((align) => (
							<button
								key={align}
								type="button"
								onClick={() => onUpdateElement?.({ textAlign: align })}
								style={{
									flex: 1,
									padding: "8px",
									border: "1px solid var(--gray-6)",
									borderRadius: "4px",
									background: selectedElement.properties.textAlign === align ? "var(--accent-3)" : "var(--gray-1)",
									color: selectedElement.properties.textAlign === align ? "var(--accent-11)" : "var(--gray-11)",
									cursor: "pointer",
									fontSize: "12px",
									textTransform: "capitalize",
								}}
							>
								{align}
							</button>
						))}
					</div>
				</div>
			</div>
		);
	};

	const renderActions = () => {
		if (!selectedElement) return null;

		return (
			<div className="flex flex-col gap-4 p-4">
				{/* Link Actions */}
				{selectedElement.type === "button" && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Button Actions
						</Text>
						<div className="flex flex-col gap-2">
							<select
								value={selectedElement.properties.actionType || "link"}
								onChange={(e) => onUpdateElement?.({ actionType: e.target.value })}
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							>
								<option value="link">Link to URL</option>
								<option value="scroll">Scroll to Element</option>
								<option value="modal">Open Modal</option>
								<option value="form">Submit Form</option>
							</select>
						</div>
					</div>
				)}

				{/* Form Actions */}
				{selectedElement.type === "input" && (
					<div>
						<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
							Form Actions
						</Text>
						<div className="flex flex-col gap-2">
							<select
								value={selectedElement.properties.inputType || "text"}
								onChange={(e) => onUpdateElement?.({ inputType: e.target.value })}
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									background: "var(--gray-1)",
									fontSize: "14px",
								}}
							>
								<option value="text">Text Input</option>
								<option value="email">Email Input</option>
								<option value="tel">Phone Input</option>
								<option value="number">Number Input</option>
								<option value="password">Password Input</option>
							</select>
						</div>
					</div>
				)}

				{/* Animation */}
				<div>
					<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
						Animation
					</Text>
					<div className="flex flex-col gap-2">
						<select
							value={selectedElement.properties.animation || "none"}
							onChange={(e) => onUpdateElement?.({ animation: e.target.value })}
							style={{
								width: "100%",
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								background: "var(--gray-1)",
								fontSize: "14px",
							}}
						>
							<option value="none">No Animation</option>
							<option value="fadeIn">Fade In</option>
							<option value="slideUp">Slide Up</option>
							<option value="slideDown">Slide Down</option>
							<option value="bounce">Bounce</option>
						</select>
					</div>
				</div>

				{/* Visibility */}
				<div>
					<Text size="2" weight="medium" style={{ marginBottom: "8px" }}>
						Visibility
					</Text>
					<div className="flex items-center gap-2">
						<input
							type="checkbox"
							id="visible"
							checked={selectedElement.properties.visible !== false}
							onChange={(e) => onUpdateElement?.({ visible: e.target.checked })}
							style={{
								width: "16px",
								height: "16px",
								cursor: "pointer",
							}}
						/>
						<label htmlFor="visible" style={{ fontSize: "14px", cursor: "pointer" }}>
							Visible on page
						</label>
					</div>
				</div>

				{/* Features Section Advanced Styling */}
				{selectedElement.type === "features" && (
					<>
						<div style={{ marginTop: "24px", paddingTop: "24px", borderTop: "1px solid var(--gray-6)" }}>
							<details style={{ marginBottom: "16px" }}>
								<summary style={{ 
									cursor: "pointer", 
									fontWeight: "600", 
									fontSize: "14px",
									padding: "8px 0",
									listStyle: "none",
									display: "flex",
									alignItems: "center",
									gap: "8px"
								}}>
									<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
										<path d="M9 18l6-6-6-6"/>
									</svg>
									Advanced Styling
								</summary>
								
								<div style={{ marginTop: "16px", paddingLeft: "24px" }}>
									{/* Layout Controls */}
									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											Columns
										</Text>
										<select
											value={selectedElement.properties.columns || 3}
											onChange={(e) => onUpdateElement?.({ columns: parseInt(e.target.value) })}
											style={{
												width: "100%",
												padding: "8px 12px",
												border: "1px solid var(--gray-7)",
												borderRadius: "6px",
												background: "var(--gray-1)",
												fontSize: "14px",
											}}
										>
											<option value={1}>1 Column</option>
											<option value={2}>2 Columns</option>
											<option value={3}>3 Columns</option>
											<option value={4}>4 Columns</option>
										</select>
									</div>

									{/* Feature Card Styling */}
									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											Feature Card Background
										</Text>
										<input
											type="color"
											value={selectedElement.properties.featureBackground || "#f9fafb"}
											onChange={(e) => onUpdateElement?.({ featureBackground: e.target.value })}
											style={{
												width: "100%",
												height: "40px",
												border: "1px solid var(--gray-7)",
												borderRadius: "6px",
												cursor: "pointer",
											}}
										/>
									</div>

									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											Feature Card Border Color
										</Text>
										<input
											type="color"
											value={selectedElement.properties.featureBorderColor || "#e5e7eb"}
											onChange={(e) => onUpdateElement?.({ featureBorderColor: e.target.value })}
											style={{
												width: "100%",
												height: "40px",
												border: "1px solid var(--gray-7)",
												borderRadius: "6px",
												cursor: "pointer",
											}}
										/>
									</div>

									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											Feature Card Border Radius
										</Text>
										<input
											type="range"
											min="0"
											max="24"
											value={selectedElement.properties.featureBorderRadius || 16}
											onChange={(e) => onUpdateElement?.({ featureBorderRadius: parseInt(e.target.value) })}
											style={{
												width: "100%",
											}}
										/>
										<Text size="1" color="gray" style={{ textAlign: "center", display: "block", marginTop: "4px" }}>
											{selectedElement.properties.featureBorderRadius || 16}px
										</Text>
									</div>

									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											Feature Gap
										</Text>
										<input
											type="range"
											min="8"
											max="64"
											value={selectedElement.properties.featureGap || 32}
											onChange={(e) => onUpdateElement?.({ featureGap: parseInt(e.target.value) })}
											style={{
												width: "100%",
											}}
										/>
										<Text size="1" color="gray" style={{ textAlign: "center", display: "block", marginTop: "4px" }}>
											{selectedElement.properties.featureGap || 32}px
										</Text>
									</div>

									{/* Icon Styling */}
									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											Icon Background Color
										</Text>
										<input
											type="color"
											value={selectedElement.properties.iconBackground || "#667eea"}
											onChange={(e) => onUpdateElement?.({ iconBackground: e.target.value })}
											style={{
												width: "100%",
												height: "40px",
												border: "1px solid var(--gray-7)",
												borderRadius: "6px",
												cursor: "pointer",
											}}
										/>
									</div>

									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											Icon Size
										</Text>
										<input
											type="range"
											min="24"
											max="80"
											value={selectedElement.properties.iconSize || 64}
											onChange={(e) => onUpdateElement?.({ iconSize: parseInt(e.target.value) })}
											style={{
												width: "100%",
											}}
										/>
										<Text size="1" color="gray" style={{ textAlign: "center", display: "block", marginTop: "4px" }}>
											{selectedElement.properties.iconSize || 64}px
										</Text>
									</div>

									{/* Typography */}
									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											Feature Title Color
										</Text>
										<input
											type="color"
											value={selectedElement.properties.featureTitleColor || "#111827"}
											onChange={(e) => onUpdateElement?.({ featureTitleColor: e.target.value })}
											style={{
												width: "100%",
												height: "40px",
												border: "1px solid var(--gray-7)",
												borderRadius: "6px",
												cursor: "pointer",
											}}
										/>
									</div>

									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											Feature Description Color
										</Text>
										<input
											type="color"
											value={selectedElement.properties.featureDescriptionColor || "#6b7280"}
											onChange={(e) => onUpdateElement?.({ featureDescriptionColor: e.target.value })}
											style={{
												width: "100%",
												height: "40px",
												border: "1px solid var(--gray-7)",
												borderRadius: "6px",
												cursor: "pointer",
											}}
										/>
									</div>

									{/* CTA Button Styling */}
									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											CTA Button Background
										</Text>
										<input
											type="color"
											value={selectedElement.properties.ctaBackground || "#667eea"}
											onChange={(e) => onUpdateElement?.({ ctaBackground: e.target.value })}
											style={{
												width: "100%",
												height: "40px",
												border: "1px solid var(--gray-7)",
												borderRadius: "6px",
												cursor: "pointer",
											}}
										/>
									</div>

									<div style={{ marginBottom: "16px" }}>
										<Text size="1" weight="medium" style={{ marginBottom: "8px", display: "block" }}>
											CTA Button Text Color
										</Text>
										<input
											type="color"
											value={selectedElement.properties.ctaTextColor || "#ffffff"}
											onChange={(e) => onUpdateElement?.({ ctaTextColor: e.target.value })}
											style={{
												width: "100%",
												height: "40px",
												border: "1px solid var(--gray-7)",
												borderRadius: "6px",
												cursor: "pointer",
											}}
										/>
									</div>
								</div>
							</details>
						</div>

						{/* Template Management */}
						<div style={{ marginTop: "24px", paddingTop: "24px", borderTop: "1px solid var(--gray-6)" }}>
							<Text size="2" weight="bold" style={{ marginBottom: "16px" }}>
								Templates
							</Text>
							
							{/* Save Template */}
							<div style={{ marginBottom: "16px" }}>
								{!showSaveTemplate ? (
									<Button 
										size="1" 
										variant="soft"
										onClick={() => setShowSaveTemplate(true)}
										style={{ width: "100%" }}
									>
										💾 Save as Template
									</Button>
								) : (
									<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
										<input
											type="text"
											value={templateName}
											onChange={(e) => setTemplateName(e.target.value)}
											placeholder="Template name..."
											style={{
												width: "100%",
												padding: "8px 12px",
												border: "1px solid var(--gray-7)",
												borderRadius: "6px",
												background: "var(--gray-1)",
												fontSize: "14px",
											}}
										/>
										<div style={{ display: "flex", gap: "8px" }}>
											<Button 
												size="1" 
												onClick={saveTemplate}
												disabled={!templateName.trim()}
												style={{ flex: 1 }}
											>
												Save
											</Button>
											<Button 
												size="1" 
												variant="soft"
												onClick={() => {
													setShowSaveTemplate(false);
													setTemplateName("");
												}}
												style={{ flex: 1 }}
											>
												Cancel
											</Button>
										</div>
									</div>
								)}
							</div>

							{/* Load Template */}
							<div style={{ marginBottom: "16px" }}>
								{!showLoadTemplate ? (
									<Button 
										size="1" 
										variant="soft"
										onClick={() => setShowLoadTemplate(true)}
										disabled={savedTemplates.length === 0}
										style={{ width: "100%" }}
									>
										📁 Load Template ({savedTemplates.length})
									</Button>
								) : (
									<div>
										<div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "12px" }}>
											<Text size="1" weight="medium">Saved Templates</Text>
											<Button 
												size="1" 
												variant="soft"
												onClick={() => setShowLoadTemplate(false)}
												style={{ padding: "4px 8px", fontSize: "12px" }}
											>
												✕
											</Button>
										</div>
										{savedTemplates.length === 0 ? (
											<Text size="1" color="gray" style={{ textAlign: "center", padding: "16px" }}>
												No templates saved yet
											</Text>
										) : (
											<div style={{ display: "flex", flexDirection: "column", gap: "8px", maxHeight: "200px", overflowY: "auto" }}>
												{savedTemplates.map((template) => (
													<div
														key={template.id}
														style={{
															display: "flex",
															justifyContent: "space-between",
															alignItems: "center",
															padding: "8px 12px",
															background: "var(--gray-2)",
															borderRadius: "6px",
															border: "1px solid var(--gray-6)",
														}}
													>
														<Text size="1" weight="medium" style={{ flex: 1 }}>
															{template.name}
														</Text>
														<div style={{ display: "flex", gap: "4px" }}>
															<Button 
																size="1" 
																variant="soft"
																onClick={() => loadTemplate(template)}
																style={{ padding: "4px 8px", fontSize: "12px" }}
															>
																Load
															</Button>
															<Button 
																size="1" 
																variant="soft"
																onClick={() => deleteTemplate(template.id)}
																style={{ padding: "4px 8px", fontSize: "12px", color: "var(--red-9)" }}
															>
																🗑️
															</Button>
														</div>
													</div>
												))}
											</div>
										)}
									</div>
								)}
							</div>
						</div>
					</>
				)}
			</div>
		);
	};

	return (
		<div
			style={{
				width: "320px",
				background: "var(--color-panel-solid)",
				borderLeft: "1px solid var(--gray-6)",
				display: "flex",
				flexDirection: "column",
				overflow: "hidden",
			}}
		>
			{/* Header */}
			<div
				style={{
					padding: "16px",
					borderBottom: "1px solid var(--gray-6)",
					background: "var(--gray-1)",
				}}
			>
				<Heading size="4" weight="bold">
					{selectedElement ? `${selectedElement.name} Properties` : "Properties"}
				</Heading>
				{selectedElement && (
					<Text size="1" color="gray" style={{ marginTop: "4px" }}>
						{selectedElement.type} element
					</Text>
				)}
			</div>

			{/* Tabs */}
			<div
				style={{
					display: "flex",
					borderBottom: "1px solid var(--gray-6)",
					background: "var(--gray-1)",
				}}
			>
				{tabs.map((tab) => (
					<button
						key={tab.id}
						type="button"
						onClick={() => setActiveTab(tab.id)}
						style={{
							flex: 1,
							padding: "12px 8px",
							background: activeTab === tab.id ? "var(--accent-3)" : "transparent",
							border: "none",
							borderBottom: activeTab === tab.id ? "2px solid var(--accent-9)" : "2px solid transparent",
							cursor: "pointer",
							transition: "all 0.2s",
							display: "flex",
							flexDirection: "column",
							alignItems: "center",
							gap: "4px",
						}}
					>
						<div style={{ color: "var(--gray-11)" }}>
							{getIcon(tab.icon)}
						</div>
						<Text size="1" weight={activeTab === tab.id ? "bold" : "medium"}>
							{tab.label}
						</Text>
					</button>
				))}
			</div>

			{/* Content */}
			<div
				style={{
					flex: 1,
					overflowY: "auto",
					background: "var(--gray-1)",
				}}
			>
				{selectedElement ? (
					<>
						{activeTab === "content" && renderContent()}
						{activeTab === "design" && renderDesign()}
						{activeTab === "actions" && renderActions()}
					</>
				) : (
					<div
						style={{
							padding: "40px 20px",
							textAlign: "center",
							color: "var(--gray-9)",
						}}
					>
						<svg
							width="48"
							height="48"
							viewBox="0 0 24 24"
							fill="none"
							stroke="var(--gray-8)"
							strokeWidth="1.5"
							style={{ margin: "0 auto 16px" }}
						>
							<circle cx="12" cy="12" r="3" />
							<path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" />
						</svg>
						<Text size="2" color="gray">
							Select an element to edit its properties
						</Text>
					</div>
				)}
			</div>

			{/* Footer */}
			<div
				style={{
					padding: "16px",
					borderTop: "1px solid var(--gray-6)",
					background: "var(--gray-1)",
				}}
			>
				{onCloseEditor && (
					<Button size="2" variant="soft" onClick={onCloseEditor} style={{ width: "100%" }}>
						Close Editor
					</Button>
				)}
			</div>
		</div>
	);
}
