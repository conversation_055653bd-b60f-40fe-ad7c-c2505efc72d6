"use client";

import { useState, useRef } from "react";
import { Button, Text } from "frosted-ui";
import { supabase } from "@/lib/supabase";

interface VideoUploadProps {
	onVideoUploaded: (videoUrl: string) => void;
	currentVideoUrl?: string;
	className?: string;
}

export function VideoUpload({ onVideoUploaded, currentVideoUrl, className }: VideoUploadProps) {
	const [isUploading, setIsUploading] = useState(false);
	const [uploadProgress, setUploadProgress] = useState(0);
	const [error, setError] = useState<string | null>(null);
	const fileInputRef = useRef<HTMLInputElement>(null);
	
	// console.log('🎬 VideoUpload component - currentVideoUrl:', currentVideoUrl);

	const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (!file) return;

		// Validate file type
		if (!file.type.startsWith('video/')) {
			setError('Please select a valid video file');
			return;
		}

		// Validate file size (max 100MB)
		const maxSize = 100 * 1024 * 1024; // 100MB
		if (file.size > maxSize) {
			setError('Video file must be less than 100MB');
			return;
		}

		setError(null);
		setIsUploading(true);
		setUploadProgress(0);

		try {
			// Generate unique filename
			const fileExt = file.name.split('.').pop();
			const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
			const filePath = `videos/${fileName}`;

			// Upload to Supabase Storage
			const { data, error: uploadError } = await supabase.storage
				.from('videos')
				.upload(filePath, file, {
					cacheControl: '3600',
					upsert: false
				});

			if (uploadError) {
				throw uploadError;
			}

			// Get public URL
			const { data: { publicUrl } } = supabase.storage
				.from('videos')
				.getPublicUrl(filePath);

			// console.log('🎬 Video uploaded successfully, URL:', publicUrl);
			onVideoUploaded(publicUrl);
			setUploadProgress(100);
		} catch (err) {
			console.error('Upload error:', err);
			setError(err instanceof Error ? err.message : 'Failed to upload video');
		} finally {
			setIsUploading(false);
			setUploadProgress(0);
		}
	};

	const handleUploadClick = () => {
		fileInputRef.current?.click();
	};

	const handleRemoveVideo = () => {
		onVideoUploaded('');
		setError(null);
	};

	return (
		<div className={className}>
			<input
				ref={fileInputRef}
				type="file"
				accept="video/*"
				onChange={handleFileSelect}
				style={{ display: 'none' }}
			/>

			{currentVideoUrl ? (
				<div style={{ marginBottom: '16px' }}>
					<div style={{
						position: 'relative',
						paddingBottom: '56.25%', // 16:9 aspect ratio
						height: 0,
						borderRadius: '8px',
						overflow: 'hidden',
						background: '#f1f5f9'
					}}>
						<video
							src={currentVideoUrl}
							controls
							style={{
								position: 'absolute',
								top: 0,
								left: 0,
								width: '100%',
								height: '100%',
								objectFit: 'cover'
							}}
						/>
					</div>
					<div style={{ display: 'flex', gap: '8px', marginTop: '12px' }}>
						<Button
							size="1"
							variant="soft"
							color="blue"
							onClick={handleUploadClick}
							disabled={isUploading}
						>
							Replace Video
						</Button>
						<Button
							size="1"
							variant="soft"
							color="red"
							onClick={handleRemoveVideo}
							disabled={isUploading}
						>
							Remove Video
						</Button>
					</div>
				</div>
			) : (
				<div style={{
					border: '2px dashed #d1d5db',
					borderRadius: '8px',
					padding: '32px',
					textAlign: 'center',
					background: '#f9fafb',
					cursor: 'pointer',
					transition: 'all 0.2s ease'
				}}
				onClick={handleUploadClick}
				onMouseEnter={(e) => {
					e.currentTarget.style.borderColor = '#3b82f6';
					e.currentTarget.style.background = '#eff6ff';
				}}
				onMouseLeave={(e) => {
					e.currentTarget.style.borderColor = '#d1d5db';
					e.currentTarget.style.background = '#f9fafb';
				}}
				>
					<div style={{
						width: '48px',
						height: '48px',
						background: '#3b82f6',
						borderRadius: '8px',
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center',
						margin: '0 auto 16px',
						color: 'white',
						fontSize: '20px'
					}}>
						📹
					</div>
					<Text size="2" weight="medium" style={{ color: '#374151', marginBottom: '8px' }}>
						Upload Video
					</Text>
					<Text size="1" style={{ color: '#6b7280' }}>
						Click to select a video file (max 100MB)
					</Text>
				</div>
			)}

			{isUploading && (
				<div style={{ marginTop: '12px' }}>
					<div style={{
						width: '100%',
						height: '4px',
						background: '#e5e7eb',
						borderRadius: '2px',
						overflow: 'hidden'
					}}>
						<div style={{
							width: `${uploadProgress}%`,
							height: '100%',
							background: '#3b82f6',
							transition: 'width 0.3s ease'
						}} />
					</div>
					<Text size="1" style={{ color: '#6b7280', marginTop: '8px' }}>
						Uploading... {uploadProgress}%
					</Text>
				</div>
			)}

			{error && (
				<Text size="1" style={{ color: '#dc2626', marginTop: '8px' }}>
					{error}
				</Text>
			)}
		</div>
	);
}
