"use client";

import { <PERSON><PERSON>, <PERSON>, Heading, Text } from "frosted-ui";
import { useState } from "react";

export function SettingsSection() {
	const [isSavingGeneral, setIsSavingGeneral] = useState(false);

	const handleSaveGeneral = async () => {
		setIsSavingGeneral(true);
		await new Promise(resolve => setTimeout(resolve, 1000));
		alert("General settings saved! (Note: This will be connected to a database in the full version)");
		setIsSavingGeneral(false);
	};

	return (
		<div style={{ padding: "32px" }}>
			<div className="mb-6">
				<Heading size="8">Settings</Heading>
				<Text color="gray" size="2">
					Manage your FunnelFlow AI configuration
				</Text>
			</div>

			<div className="flex flex-col gap-4">
				<Card className="whop-card" style={{ padding: "28px" }}>
					<Heading size="5" style={{ marginBottom: "8px" }}>
						General Settings
					</Heading>
					<Text
						size="2"
						color="gray"
						style={{ display: "block", marginBottom: "16px" }}
					>
						Configure basic settings for your funnels
					</Text>
					<div className="flex flex-col gap-3">
						<div>
							<Text
								size="2"
								weight="bold"
								style={{ display: "block", marginBottom: "8px" }}
							>
								Default Domain
							</Text>
							<input
								type="text"
								placeholder="funnels.yourcompany.com"
								style={{
									width: "100%",
									padding: "8px 12px",
									borderRadius: "8px",
									border: "1px solid var(--gray-7)",
									background: "var(--gray-2)",
									color: "var(--gray-12)",
									fontSize: "14px",
								}}
							/>
						</div>
						<Button 
							size="2" 
							variant="soft" 
							style={{ alignSelf: "flex-start" }}
							onClick={handleSaveGeneral}
							disabled={isSavingGeneral}
							loading={isSavingGeneral}
						>
							{isSavingGeneral ? "Saving..." : "Save Changes"}
						</Button>
					</div>
				</Card>
			</div>
		</div>
	);
}
