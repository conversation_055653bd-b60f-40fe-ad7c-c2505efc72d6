"use client";

import { Bad<PERSON>, Card, Heading, Text, Button } from "frosted-ui";
import { useState } from "react";

interface AnalyticsData {
	totalVisits: number;
	totalConversions: number;
	totalRevenue: number;
	conversionRate: number;
	averageOrderValue: number;
	monthlyRecurringRevenue: number;
	insights: Array<{
		id: string;
		type: "warning" | "success" | "info";
		message: string;
		suggestion: string;
	}>;
	funnelPerformance: Array<{
		funnelName: string;
		visits: number;
		conversions: number;
		conversionRate: number;
		revenue: number;
	}>;
}

export function AnalyticsSection() {
	const [analytics] = useState<AnalyticsData>({
		totalVisits: 1247,
		totalConversions: 89,
		totalRevenue: 12450,
		conversionRate: 7.1,
		averageOrderValue: 140,
		monthlyRecurringRevenue: 2890,
		insights: [
			{
				id: "1",
				type: "warning",
				message: "Your checkout page is converting 15% lower than average",
				suggestion:
					"Consider A/B testing your checkout CTA or adding trust badges",
			},
			{
				id: "2",
				type: "success",
				message: "Your upsell funnel is performing 23% above average",
				suggestion: "Great work! Consider creating more upsell offers",
			},
			{
				id: "3",
				type: "info",
				message: "Mobile traffic accounts for 64% of your visitors",
				suggestion: "Ensure your funnels are fully optimized for mobile",
			},
		],
		funnelPerformance: [
			{
				funnelName: "Digital Product Funnel",
				visits: 845,
				conversions: 67,
				conversionRate: 7.9,
				revenue: 9450,
			},
			{
				funnelName: "Lead Capture Funnel",
				visits: 402,
				conversions: 22,
				conversionRate: 5.5,
				revenue: 3000,
			},
		],
	});

	const [dateRange, setDateRange] = useState("30d");


	return (
		<div style={{ padding: "32px" }}>
			{/* Header */}
			<div className="flex items-center justify-between mb-8">
				<div>
					<Heading size="8">Analytics</Heading>
					<Text color="gray" size="2">
						Track your funnel performance and conversion metrics
					</Text>
				</div>

				{/* Date Range Selector */}
				<div className="flex items-center gap-3">
					<select
						value={dateRange}
						onChange={(e) => setDateRange(e.target.value)}
						style={{
							minWidth: "140px",
							padding: "8px 12px",
							borderRadius: "6px",
							border: "1px solid var(--gray-6)",
							background: "var(--gray-1)",
							color: "var(--gray-12)",
							fontSize: "14px",
						}}
					>
						<option value="7d">Last 7 days</option>
						<option value="30d">Last 30 days</option>
						<option value="90d">Last 90 days</option>
						<option value="1y">Last year</option>
					</select>
					<Button variant="soft" size="2">
						Export Data
					</Button>
				</div>
			</div>

			{/* Key Metrics */}
			<div className="grid grid-cols-6 gap-4 mb-8">
				<Card 
					className="aspect-square"
					style={{ 
						padding: "20px",
						background: "linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(59, 130, 246, 0.05) 100%)",
						border: "none",
						boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
					}}
				>
					<div className="flex flex-col gap-2 h-full justify-center">
						<Text size="1" color="gray">
							Visits
						</Text>
						<Heading size="6">
							{analytics.totalVisits.toLocaleString()}
						</Heading>
					</div>
				</Card>

				<Card 
					className="aspect-square"
					style={{ 
						padding: "20px",
						background: "linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(59, 130, 246, 0.05) 100%)",
						border: "none",
						boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
					}}
				>
					<div className="flex flex-col gap-2 h-full justify-center">
						<Text size="1" color="gray">
							Conversions
						</Text>
						<Heading size="6">
							{analytics.totalConversions.toLocaleString()}
						</Heading>
					</div>
				</Card>

				<Card 
					className="aspect-square"
					style={{ 
						padding: "20px",
						background: "linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(59, 130, 246, 0.05) 100%)",
						border: "none",
						boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
					}}
				>
					<div className="flex flex-col gap-2 h-full justify-center">
						<Text size="1" color="gray">
							Revenue
						</Text>
						<Heading size="6">
							${analytics.totalRevenue.toLocaleString()}
						</Heading>
					</div>
				</Card>

				<Card 
					className="aspect-square"
					style={{ 
						padding: "20px",
						background: "linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(59, 130, 246, 0.05) 100%)",
						border: "none",
						boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
					}}
				>
					<div className="flex flex-col gap-2 h-full justify-center">
						<Text size="1" color="gray">
							Conv. Rate
						</Text>
						<Heading size="6">
							{analytics.conversionRate}%
						</Heading>
					</div>
				</Card>

				<Card 
					className="aspect-square"
					style={{ 
						padding: "20px",
						background: "linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(59, 130, 246, 0.05) 100%)",
						border: "none",
						boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
					}}
				>
					<div className="flex flex-col gap-2 h-full justify-center">
						<Text size="1" color="gray">
							AOV
						</Text>
						<Heading size="6">
							${analytics.averageOrderValue.toLocaleString()}
						</Heading>
					</div>
				</Card>

				<Card 
					className="aspect-square"
					style={{ 
						padding: "20px",
						background: "linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(59, 130, 246, 0.05) 100%)",
						border: "none",
						boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
					}}
				>
					<div className="flex flex-col gap-2 h-full justify-center">
						<Text size="1" color="gray">
							MRR
						</Text>
						<Heading size="6">
							${analytics.monthlyRecurringRevenue.toLocaleString()}
						</Heading>
					</div>
				</Card>
			</div>

			{/* AI Insights */}
			<div className="mb-8">
				<div className="flex items-center gap-3 mb-6">
					<Heading size="5">AI Insights</Heading>
					<Badge color="purple" size="1">
						Powered by AI
					</Badge>
				</div>

				<div className="flex flex-col gap-3">
					{analytics.insights.map((insight) => (
						<Card key={insight.id}>
							<div className="flex items-start gap-3">
								<div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0 mt-0.5">
									<svg
										width="16"
										height="16"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
										className="text-purple-600"
									>
										<path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
									</svg>
								</div>
								<div className="flex-1">
									<Text size="2" weight="medium" style={{ lineHeight: "1.5" }}>
										{insight.message}
									</Text>
								</div>
							</div>
						</Card>
					))}
				</div>
			</div>

			{/* Funnel Performance */}
			<div>
				<div className="flex items-center justify-between mb-6">
					<Heading size="5">Funnel Performance</Heading>
					<Button variant="soft" size="2">
						View Details
					</Button>
				</div>
				<Card>
					<table className="w-full">
						<thead>
							<tr>
								<th className="text-left p-4">
									<Text size="1" weight="medium" color="gray">
										Funnel
									</Text>
								</th>
								<th className="text-right p-4">
									<Text size="1" weight="medium" color="gray">
										Visits
									</Text>
								</th>
								<th className="text-right p-4">
									<Text size="1" weight="medium" color="gray">
										Conversions
									</Text>
								</th>
								<th className="text-right p-4">
									<Text size="1" weight="medium" color="gray">
										Conv. Rate
									</Text>
								</th>
								<th className="text-right p-4">
									<Text size="1" weight="medium" color="gray">
										Revenue
									</Text>
								</th>
							</tr>
						</thead>
						<tbody>
							{analytics.funnelPerformance.map((funnel) => (
								<tr key={funnel.funnelName} className="border-t border-gray-6">
									<td className="p-4">
										<Text size="2" weight="medium">
											{funnel.funnelName}
										</Text>
									</td>
									<td className="p-4 text-right">
										<Text size="2">
											{funnel.visits.toLocaleString()}
										</Text>
									</td>
									<td className="p-4 text-right">
										<Text size="2">
											{funnel.conversions.toLocaleString()}
										</Text>
									</td>
									<td className="p-4 text-right">
										<Badge
											color={
												funnel.conversionRate >= 7
													? "green"
													: funnel.conversionRate >= 5
														? "amber"
														: "red"
											}
											size="1"
										>
											{funnel.conversionRate}%
										</Badge>
									</td>
									<td className="p-4 text-right">
										<Text size="2">
											${funnel.revenue.toLocaleString()}
										</Text>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</Card>
			</div>
		</div>
	);
}
