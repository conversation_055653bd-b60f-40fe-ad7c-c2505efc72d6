"use client";

import type { FunnelSchema } from "@/lib/types/funnel";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Heading, Text } from "frosted-ui";
import { useState } from "react";

interface TemplatesSectionProps {
	onSelectTemplate: (template: FunnelSchema) => void;
}

interface FunnelTemplate {
	id: string;
	name: string;
	description: string;
	category:
		| "lead-capture"
		| "webinar"
		| "digital-product"
		| "waitlist"
		| "membership";
	icon: string;
	schema: FunnelSchema;
	popular?: boolean;
}

const templates: FunnelTemplate[] = [
	{
		id: "lead-capture-basic",
		name: "Lead Capture Funnel",
		description:
			"Simple opt-in funnel to capture leads with email capture and thank you page",
		category: "lead-capture",
		icon: "",
		popular: true,
		schema: {
			title: "Lead Magnet Funnel",
			description: "Capture leads with an irresistible lead magnet",
			layout: "multi-step",
			pages: [
				{
					id: "page-1",
					name: "Opt-In Page",
					type: "landing",
					order: 1,
					sections: [
						{
							id: "hero-1",
							type: "hero",
							heading: "Get Your Free Guide",
							content:
								"Enter your email to receive our comprehensive guide instantly",
							cta: {
								text: "Download Now",
								url: "/thank-you",
								style: "primary",
							},
						},
					],
				},
				{
					id: "page-2",
					name: "Thank You",
					type: "thank-you",
					order: 2,
					sections: [
						{
							id: "hero-2",
							type: "hero",
							heading: "Check Your Email!",
							content:
								"We've sent your free guide to your inbox. Check your email now.",
						},
					],
				},
			],
			sections: [],
			theme: {
				primaryColor: "#6366f1",
				backgroundColor: "#ffffff",
				textColor: "#1f2937",
			},
		},
	},
	{
		id: "webinar-funnel",
		name: "Webinar Registration Funnel",
		description:
			"Complete webinar funnel with registration, reminder, and replay pages",
		category: "webinar",
		icon: "",
		popular: true,
		schema: {
			title: "Live Webinar Funnel",
			description: "Drive webinar registrations and attendance",
			layout: "multi-step",
			pages: [
				{
					id: "page-1",
					name: "Registration Page",
					type: "landing",
					order: 1,
					sections: [
						{
							id: "hero-1",
							type: "hero",
							heading: "Join Our Exclusive Live Training",
							content:
								"Learn the proven strategies to 10x your business in 60 minutes",
							cta: {
								text: "Register Now",
								url: "/thank-you",
								style: "primary",
							},
						},
						{
							id: "features-1",
							type: "features",
							heading: "What You'll Learn",
							content: "Key takeaways from this training",
							items: [
								{
									title: "Strategy #1",
									description: "How to scale efficiently",
									icon: "",
								},
								{
									title: "Strategy #2",
									description: "Automation secrets",
									icon: "",
								},
								{
									title: "Strategy #3",
									description: "Growth frameworks",
									icon: "",
								},
							],
						},
					],
				},
				{
					id: "page-2",
					name: "Thank You",
					type: "thank-you",
					order: 2,
					sections: [
						{
							id: "hero-2",
							type: "hero",
							heading: "You're Registered!",
							content:
								"Add this to your calendar and we'll send you a reminder",
						},
					],
				},
			],
			sections: [],
			theme: {
				primaryColor: "#8b5cf6",
				backgroundColor: "#ffffff",
				textColor: "#1f2937",
			},
		},
	},
	{
		id: "digital-product-sales",
		name: "Digital Product Sales Funnel",
		description:
			"Full sales funnel for digital products with upsells and downsells",
		category: "digital-product",
		icon: "",
		popular: true,
		schema: {
			title: "Digital Product Funnel",
			description: "Sell your digital products with maximum conversions",
			layout: "multi-step",
			pages: [
				{
					id: "page-1",
					name: "Sales Page",
					type: "landing",
					order: 1,
					sections: [
						{
							id: "hero-1",
							type: "hero",
							heading: "Transform Your Business Today",
							content:
								"Get instant access to our proven system that's generated $10M+",
							cta: {
								text: "Get Started Now",
								url: "/checkout",
								style: "primary",
							},
						},
						{
							id: "features-1",
							type: "features",
							heading: "What's Included",
							content: "Everything you need to succeed",
							items: [
								{
									title: "Video Training",
									description: "10+ hours of content",
									icon: "",
								},
								{
									title: "Templates",
									description: "Ready-to-use resources",
									icon: "",
								},
								{
									title: "Community",
									description: "Private member group",
									icon: "",
								},
							],
						},
						{
							id: "pricing-1",
							type: "pricing",
							heading: "Special Launch Price",
							content: "Limited time offer",
							items: [
								{
									title: "$497",
									description: "One-time payment. Lifetime access.",
								},
							],
						},
					],
				},
				{
					id: "page-2",
					name: "Checkout",
					type: "checkout",
					order: 2,
					sections: [
						{
							id: "checkout-1",
							type: "cta",
							heading: "Complete Your Order",
							content: "Secure checkout powered by Whop",
						},
					],
				},
				{
					id: "page-3",
					name: "Upsell",
					type: "upsell",
					order: 3,
					sections: [
						{
							id: "hero-3",
							type: "hero",
							heading: "Wait! Special One-Time Offer",
							content: "Add our premium implementation package for just $297",
							cta: {
								text: "Yes, Add This!",
								url: "/thank-you",
								style: "primary",
							},
						},
					],
				},
				{
					id: "page-4",
					name: "Thank You",
					type: "thank-you",
					order: 4,
					sections: [
						{
							id: "hero-4",
							type: "hero",
							heading: "Welcome to the Program!",
							content: "Check your email for instant access details",
						},
					],
				},
			],
			sections: [],
			theme: {
				primaryColor: "#10b981",
				backgroundColor: "#ffffff",
				textColor: "#1f2937",
			},
		},
	},
	{
		id: "waitlist-launch",
		name: "Waitlist / Launch Page",
		description:
			"Build anticipation with a waitlist funnel for upcoming product launches",
		category: "waitlist",
		icon: "",
		schema: {
			title: "Product Launch Waitlist",
			description: "Build anticipation and collect early sign-ups",
			layout: "single-page",
			pages: [
				{
					id: "page-1",
					name: "Waitlist Page",
					type: "landing",
					order: 1,
					sections: [
						{
							id: "hero-1",
							type: "hero",
							heading: "Something Big Is Coming",
							content:
								"Join the waitlist to get early access and exclusive bonuses",
							cta: { text: "Join Waitlist", url: "#", style: "primary" },
						},
						{
							id: "features-1",
							type: "features",
							heading: "Why Join Early?",
							content: "Exclusive benefits for early adopters",
							items: [
								{
									title: "Early Access",
									description: "Be the first to use it",
									icon: "",
								},
								{
									title: "Special Pricing",
									description: "Founder pricing locked in",
									icon: "",
								},
								{
									title: "Bonus Content",
									description: "Exclusive training",
									icon: "",
								},
							],
						},
					],
				},
			],
			sections: [],
			theme: {
				primaryColor: "#f59e0b",
				backgroundColor: "#ffffff",
				textColor: "#1f2937",
			},
		},
	},
	{
		id: "membership-funnel",
		name: "Membership Funnel",
		description: "Subscription-based membership funnel with recurring payments",
		category: "membership",
		icon: "",
		schema: {
			title: "Premium Membership Funnel",
			description: "Convert visitors into recurring members",
			layout: "multi-step",
			pages: [
				{
					id: "page-1",
					name: "Sales Page",
					type: "landing",
					order: 1,
					sections: [
						{
							id: "hero-1",
							type: "hero",
							heading: "Join Our Exclusive Community",
							content: "Get unlimited access to all our resources and training",
							cta: {
								text: "Start Membership",
								url: "/checkout",
								style: "primary",
							},
						},
						{
							id: "features-1",
							type: "features",
							heading: "Membership Benefits",
							content: "Everything included in your membership",
							items: [
								{
									title: "All Content",
									description: "Unlimited access",
									icon: "",
								},
								{
									title: "Live Calls",
									description: "Weekly coaching",
									icon: "",
								},
								{
									title: "Community",
									description: "Private Discord",
									icon: "",
								},
							],
						},
						{
							id: "pricing-1",
							type: "pricing",
							heading: "Membership Plans",
							content: "Choose the plan that works for you",
							items: [{ title: "$97/month", description: "Cancel anytime" }],
						},
					],
				},
				{
					id: "page-2",
					name: "Checkout",
					type: "checkout",
					order: 2,
					sections: [
						{
							id: "checkout-1",
							type: "cta",
							heading: "Start Your Membership",
							content: "Secure recurring billing powered by Whop",
						},
					],
				},
				{
					id: "page-3",
					name: "Welcome",
					type: "thank-you",
					order: 3,
					sections: [
						{
							id: "hero-3",
							type: "hero",
							heading: "Welcome to the Community!",
							content: "Your membership is active. Here's how to get started",
						},
					],
				},
			],
			sections: [],
			theme: {
				primaryColor: "#ec4899",
				backgroundColor: "#ffffff",
				textColor: "#1f2937",
			},
		},
	},
];

export function TemplatesSection({ onSelectTemplate }: TemplatesSectionProps) {
	const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
	const [searchQuery, setSearchQuery] = useState("");

	const categories = [
		{ id: "lead-capture", label: "Lead Capture", icon: "" },
		{ id: "webinar", label: "Webinar", icon: "" },
		{ id: "digital-product", label: "Digital Product", icon: "" },
		{ id: "waitlist", label: "Waitlist", icon: "" },
		{ id: "membership", label: "Membership", icon: "" },
	];

	const filteredTemplates = templates.filter((template) => {
		const matchesCategory =
			!selectedCategory || template.category === selectedCategory;
		const matchesSearch =
			!searchQuery ||
			template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			template.description.toLowerCase().includes(searchQuery.toLowerCase());
		return matchesCategory && matchesSearch;
	});

	return (
		<div style={{ padding: "32px" }}>
			{/* Header */}
			<div className="mb-6">
				<Heading size="8">Template Library</Heading>
				<Text color="gray" size="2">
					Start with a proven funnel template and customize it to your needs
				</Text>
			</div>

			{/* Search and Filters */}
			<div className="flex flex-col gap-4 mb-6">
				<input
					type="search"
					placeholder="Search templates..."
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
					style={{
						padding: "10px 14px",
						borderRadius: "12px",
						border: "1px solid var(--gray-6)",
						background: "var(--gray-1)",
						color: "var(--gray-12)",
						fontSize: "14px",
						width: "100%",
						maxWidth: "400px",
						transition: "all 0.2s ease",
					}}
					onFocus={(e) => {
						e.target.style.borderColor = "var(--gray-8)";
						e.target.style.background = "white";
					}}
					onBlur={(e) => {
						e.target.style.borderColor = "var(--gray-6)";
						e.target.style.background = "var(--gray-1)";
					}}
				/>

				<div className="flex items-center gap-2">
					<button
						type="button"
						onClick={() => setSelectedCategory(null)}
						style={{
							padding: "6px 12px",
							borderRadius: "20px",
							background: "transparent",
							color: !selectedCategory ? "var(--gray-12)" : "var(--gray-7)",
							border: "1px solid var(--gray-5)",
							cursor: "pointer",
							fontSize: "13px",
							fontWeight: "500",
							transition: "all 0.2s ease",
						}}
					>
						All Templates
					</button>
					{categories.map((cat) => (
						<button
							key={cat.id}
							type="button"
							onClick={() => setSelectedCategory(cat.id)}
							style={{
								padding: "6px 12px",
								borderRadius: "20px",
								background: "transparent",
								color: selectedCategory === cat.id ? "var(--gray-12)" : "var(--gray-7)",
								border: "1px solid var(--gray-5)",
								cursor: "pointer",
								fontSize: "13px",
								fontWeight: "500",
								transition: "all 0.2s ease",
							}}
						>
							{cat.label}
						</button>
					))}
				</div>
			</div>

			{/* Templates Grid */}
			<div className="grid grid-cols-3 gap-4">
			{filteredTemplates.map((template) => (
				<Card
					key={template.id}
					className="whop-card clickable"
					style={{
						padding: "28px",
						cursor: "pointer",
						position: "relative",
					}}
					onClick={() => onSelectTemplate(template.schema)}
				>
						{template.popular && (
							<div
								style={{
									position: "absolute",
									top: "12px",
									right: "12px",
								}}
							>
								<Badge color="orange" size="1">
									Popular
								</Badge>
							</div>
						)}

						<div className="flex flex-col gap-3">
						{/* Icon */}
						<div
							style={{
								width: "64px",
								height: "64px",
								borderRadius: "16px",
								background: "linear-gradient(135deg, var(--accent-4), var(--accent-3))",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								boxShadow: "0 4px 12px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
							}}
						>
					<svg
						width="32"
						height="32"
						viewBox="0 0 24 24"
						fill="none"
						stroke="var(--accent-10)"
						strokeWidth="2"
						role="img"
						aria-label="Template icon"
					>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
									/>
								</svg>
							</div>

							{/* Title */}
							<div>
								<Heading size="4" style={{ marginBottom: "4px" }}>
									{template.name}
								</Heading>
								<Text size="2" color="gray">
									{template.description}
								</Text>
							</div>

							{/* Preview Info */}
							<div
								className="flex items-center gap-4"
								style={{
									paddingTop: "12px",
									borderTop: "1px solid var(--gray-6)",
								}}
							>
								<div className="flex items-center gap-1">
									<svg
										width="14"
										height="14"
										viewBox="0 0 24 24"
										fill="none"
										stroke="var(--gray-9)"
										strokeWidth="2"
										role="img"
										aria-label="Pages icon"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
										/>
									</svg>
									<Text size="1" color="gray">
										{template.schema.pages.length} pages
									</Text>
								</div>
							</div>

					{/* Action */}
					<Button size="2" variant="soft" style={{ marginTop: "12px", width: "100%" }}>
						<span className="flex items-center justify-center gap-2">
							<svg
								width="14"
								height="14"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
							>
								<path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
							</svg>
							Use Template
						</span>
					</Button>
						</div>
					</Card>
				))}
			</div>

			{filteredTemplates.length === 0 && (
				<div
					style={{
						padding: "48px 48px 80px 48px",
						textAlign: "center",
						background: "var(--color-panel-solid)",
						borderRadius: "12px",
					}}
				>
					<div className="flex flex-col items-center gap-3" style={{ marginTop: "32px" }}>
						<Text size="3" color="gray">
							No templates found matching your criteria
						</Text>
						<Button
							size="2"
							variant="soft"
							onClick={() => {
								setSearchQuery("");
								setSelectedCategory(null);
							}}
						>
							Clear Filters
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
