"use client";

import type { FunnelSchema } from "@/lib/types/funnel";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Heading, Text, SegmentedControlNav, DropdownMenu } from "frosted-ui";
import { useCallback, useEffect, useState } from "react";

interface FunnelsSectionProps {
	companyId: string;
	onCreateFunnel: () => void;
	onEditFunnel: (funnel: FunnelSchema) => void;
}

interface SavedFunnel extends FunnelSchema {
	id: string;
	createdAt: string;
	publishedAt?: string;
	status: "draft" | "published";
	stats?: {
		visits: number;
		conversions: number;
		revenue: number;
	};
}

export function FunnelsSection({
	companyId,
	onCreateFunnel,
	onEditFunnel,
}: FunnelsSectionProps) {
	const [funnels, setFunnels] = useState<SavedFunnel[]>([]);
	const [loading, setLoading] = useState(true);
	const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
	const [filterStatus, setFilterStatus] = useState<
		"all" | "draft" | "published"
	>("all");
  const [isBusy, setIsBusy] = useState<string | null>(null);

	const loadFunnels = useCallback(async () => {
		setLoading(true);
		try {
			const response = await fetch(`/api/funnels?companyId=${companyId}`);
			const data = await response.json();
			
			if (data.success) {
				setFunnels(data.funnels);
			} else {
				console.error("Failed to load funnels:", data.error);
				setFunnels([]);
			}
		} catch (error) {
			console.error("Failed to load funnels:", error);
			setFunnels([]);
		} finally {
			setLoading(false);
		}
	}, [companyId]);

	useEffect(() => {
		loadFunnels();
	}, [loadFunnels]);

	const filteredFunnels =
		filterStatus === "all"
			? funnels
			: funnels.filter((f) => f.status === filterStatus);

  const handleDeleteFunnel = async (id: string) => {
    try {
      setIsBusy(id);
      const response = await fetch(`/api/funnels/${id}`, {
        method: 'DELETE',
      });
      const data = await response.json();
      if (data.success) {
        loadFunnels();
      } else {
        console.error("Failed to delete funnel:", data.error);
      }
    } catch (error) {
      console.error("Failed to delete funnel:", error);
    } finally {
      setIsBusy(null);
    }
  };

  const handlePublishToggle = async (id: string, nextStatus: 'draft' | 'published') => {
    try {
      setIsBusy(id);
      const response = await fetch(`/api/funnels/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates: { status: nextStatus } }),
      });
      const data = await response.json();
      if (data.success) {
        await loadFunnels();
      } else {
        console.error('Failed to update funnel:', data.error);
      }
    } catch (error) {
      console.error('Failed to update funnel:', error);
    } finally {
      setIsBusy(null);
    }
  };

  const handleAddDomain = async (funnel: SavedFunnel) => {
    const hostname = prompt('Enter domain (e.g., mysite.com)');
    if (!hostname) return;
    try {
      setIsBusy(funnel.id);
      const response = await fetch('/api/domains', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ funnelId: funnel.id, whopCompanyId: companyId, hostname }),
      });
      const data = await response.json();
      if (data.success) {
        alert(`Domain added. Verify DNS and activate: ${data.domain.hostname}`);
      } else {
        alert(`Failed to add domain: ${data.error || 'unknown error'}`);
      }
    } catch (error) {
      alert('Failed to add domain.');
    } finally {
      setIsBusy(null);
    }
  };

	return (
		<div style={{ padding: "32px" }}>
			{/* Header */}
			<div className="flex items-center justify-between mb-6">
				<div>
					<Heading
						size="8"
						style={{
							color: "var(--gray-12)",
						}}
					>
						Funnels
					</Heading>
					<Text color="gray" size="2">
						Manage your sales funnels and conversion flows
					</Text>
				</div>
				<Button size="3" onClick={onCreateFunnel}>
					<span className="flex items-center gap-2">
						<svg
							width="16"
							height="16"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
							role="img"
							aria-label="Plus icon"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								d="M12 4v16m8-8H4"
							/>
						</svg>
						Create Funnel
					</span>
				</Button>
			</div>

			{/* Filters */}
			<div className="flex items-center justify-between mb-6">
				<div
					style={{
						width: 600
					}}
				>
					<SegmentedControlNav.Root>
						<SegmentedControlNav.Link
							active={filterStatus === "all"}
							href="#"
							onClick={(e) => {
								e.preventDefault();
								setFilterStatus("all");
							}}
						>
							All ({funnels.length})
						</SegmentedControlNav.Link>
						<SegmentedControlNav.Link
							active={filterStatus === "draft"}
							href="#"
							onClick={(e) => {
								e.preventDefault();
								setFilterStatus("draft");
							}}
						>
							Drafts ({funnels.filter((f) => f.status === "draft").length})
						</SegmentedControlNav.Link>
						<SegmentedControlNav.Link
							active={filterStatus === "published"}
							href="#"
							onClick={(e) => {
								e.preventDefault();
								setFilterStatus("published");
							}}
						>
							Published ({funnels.filter((f) => f.status === "published").length})
						</SegmentedControlNav.Link>
					</SegmentedControlNav.Root>
				</div>

				<div className="flex items-center gap-2">
					<button
						type="button"
						onClick={() => setViewMode("grid")}
						style={{
							width: "32px",
							height: "32px",
							borderRadius: "6px",
							background:
								viewMode === "grid" ? "var(--accent-3)" : "transparent",
							border:
								viewMode === "grid"
									? "1px solid var(--accent-6)"
									: "1px solid var(--gray-6)",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							cursor: "pointer",
						}}
					>
						<svg
							width="16"
							height="16"
							viewBox="0 0 24 24"
							fill="none"
							stroke="var(--gray-11)"
							strokeWidth="2"
							role="img"
							aria-label="Grid view icon"
						>
							<rect x="3" y="3" width="7" height="7" />
							<rect x="14" y="3" width="7" height="7" />
							<rect x="14" y="14" width="7" height="7" />
							<rect x="3" y="14" width="7" height="7" />
						</svg>
					</button>
					<button
						type="button"
						onClick={() => setViewMode("list")}
						style={{
							width: "32px",
							height: "32px",
							borderRadius: "6px",
							background:
								viewMode === "list" ? "var(--accent-3)" : "transparent",
							border:
								viewMode === "list"
									? "1px solid var(--accent-6)"
									: "1px solid var(--gray-6)",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							cursor: "pointer",
						}}
					>
						<svg
							width="16"
							height="16"
							viewBox="0 0 24 24"
							fill="none"
							stroke="var(--gray-11)"
							strokeWidth="2"
							role="img"
							aria-label="List view icon"
						>
							<line x1="8" y1="6" x2="21" y2="6" />
							<line x1="8" y1="12" x2="21" y2="12" />
							<line x1="8" y1="18" x2="21" y2="18" />
							<line x1="3" y1="6" x2="3.01" y2="6" />
							<line x1="3" y1="12" x2="3.01" y2="12" />
							<line x1="3" y1="18" x2="3.01" y2="18" />
						</svg>
					</button>
				</div>
			</div>

			{/* Funnels Grid/List */}
			{loading ? (
				<div
					style={{
						padding: "48px",
						textAlign: "center",
						background: "var(--color-panel-solid)",
						borderRadius: "12px",
					}}
				>
					<Text size="3" color="gray">
						Loading funnels...
					</Text>
				</div>
			) : filteredFunnels.length === 0 ? (
				<div
					style={{
						padding: "48px 48px 80px 48px",
						textAlign: "center",
						background: "var(--color-panel-solid)",
						borderRadius: "12px",
					}}
				>
					<div className="flex flex-col items-center gap-3" style={{ marginTop: "32px" }}>
						<div
							style={{
								width: "64px",
								height: "64px",
								borderRadius: "50%",
								background: "var(--gray-3)",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
							}}
						>
							<svg
								width="32"
								height="32"
								viewBox="0 0 24 24"
								fill="none"
								stroke="var(--gray-9)"
								strokeWidth="2"
								role="img"
								aria-label="Funnel icon"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
								/>
							</svg>
						</div>
						<Heading size="5">No funnels yet</Heading>
						<Text color="gray" size="2">
							Create your first funnel to start converting visitors
						</Text>
						<Button size="2" onClick={onCreateFunnel} style={{ marginTop: 8 }}>
							Create Funnel
						</Button>
					</div>
				</div>
			) : (
				<div
					className={
						viewMode === "grid"
							? "grid grid-cols-3 gap-4"
							: "flex flex-col gap-3"
					}
				>
					{filteredFunnels.map((funnel) => (
						<Card
							key={funnel.id}
							style={{
								padding: "20px",
								cursor: "pointer",
								transition: "all 0.2s",
							}}
							onClick={() => onEditFunnel(funnel)}
						>
							<div className="flex flex-col gap-3">
								{/* Header */}
								<div className="flex items-start justify-between">
									<div style={{ flex: 1 }}>
										<div className="flex items-center gap-2 mb-1">
											<Heading size="4">{funnel.title}</Heading>
											<Badge
												color={
													funnel.status === "published" ? "green" : "amber"
												}
												size="1"
											>
												{funnel.status}
											</Badge>
										</div>
										<Text size="1" color="gray" style={{ display: "block" }}>
											{funnel.pages.length} pages
										</Text>
									</div>

									{/* Dropdown Menu */}
									<DropdownMenu.Root>
										<DropdownMenu.Trigger>
											<Button
												size="1"
												variant="ghost"
												style={{
													width: "32px",
													height: "32px",
													padding: "0",
													display: "flex",
													alignItems: "center",
													justifyContent: "center",
												}}
											>
												<svg
													width="16"
													height="16"
													viewBox="0 0 24 24"
													fill="none"
													stroke="currentColor"
													strokeWidth="2"
												>
													<circle cx="12" cy="12" r="1" />
													<circle cx="12" cy="5" r="1" />
													<circle cx="12" cy="19" r="1" />
												</svg>
											</Button>
										</DropdownMenu.Trigger>
										<DropdownMenu.Content align="end">
											<DropdownMenu.Item
												onClick={(e) => {
													e.stopPropagation();
													window.open(`/f/${funnel.id}`, '_blank');
												}}
											>
												View Live
											</DropdownMenu.Item>
											<DropdownMenu.Item
												onClick={(e) => {
													e.stopPropagation();
													handleAddDomain(funnel);
												}}
												disabled={isBusy === funnel.id}
											>
												Add Domain
											</DropdownMenu.Item>
											<DropdownMenu.Separator />
											<DropdownMenu.Item
												onClick={(e) => {
													e.stopPropagation();
													if (
														confirm(
															`Delete "${funnel.title}"? This action cannot be undone.`,
														)
													) {
														handleDeleteFunnel(funnel.id);
													}
												}}
												disabled={isBusy === funnel.id}
												color="red"
											>
												Delete
											</DropdownMenu.Item>
										</DropdownMenu.Content>
									</DropdownMenu.Root>
								</div>

								{/* Description */}
								<Text
									size="2"
									color="gray"
									style={{
										overflow: "hidden",
										textOverflow: "ellipsis",
										display: "-webkit-box",
										WebkitLineClamp: 2,
										WebkitBoxOrient: "vertical",
									}}
								>
									{funnel.description}
								</Text>

								{/* Stats */}
								{funnel.stats && (
									<div
										className="flex items-center gap-3"
										style={{
											paddingTop: "16px",
											borderTop: "1px solid var(--gray-6)",
										}}
									>
										<div
											style={{
												background: "var(--gray-2)",
												borderRadius: "8px",
												padding: "8px 12px",
												border: "1px solid var(--gray-6)",
												flex: 1,
												textAlign: "center",
											}}
										>
											<Text size="1" color="gray" style={{ display: "block", marginBottom: "2px" }}>
												Visits
											</Text>
											<Text size="2" weight="bold" style={{ display: "block" }}>
												{funnel.stats.visits.toLocaleString()}
											</Text>
										</div>
										<div
											style={{
												background: "var(--green-2)",
												borderRadius: "8px",
												padding: "8px 12px",
												border: "1px solid var(--green-6)",
												flex: 1,
												textAlign: "center",
											}}
										>
											<Text size="1" color="gray" style={{ display: "block", marginBottom: "2px" }}>
												Conversions
											</Text>
											<Text
												size="2"
												weight="bold"
												style={{ color: "var(--green-11)", display: "block" }}
											>
												{funnel.stats.conversions.toLocaleString()}
											</Text>
										</div>
										<div
											style={{
												background: "var(--blue-2)",
												borderRadius: "8px",
												padding: "8px 12px",
												border: "1px solid var(--blue-6)",
												flex: 1,
												textAlign: "center",
											}}
										>
											<Text size="1" color="gray" style={{ display: "block", marginBottom: "2px" }}>
												Revenue
											</Text>
											<Text
												size="2"
												weight="bold"
												style={{ color: "var(--blue-11)", display: "block" }}
											>
												${funnel.stats.revenue.toLocaleString()}
											</Text>
										</div>
									</div>
								)}

							{/* Actions */}
							<div className="flex items-center gap-3 mt-4">
								<Button
									size="3"
									variant="surface"
									onClick={(e) => {
										e.stopPropagation();
										onEditFunnel(funnel);
									}}
									disabled={isBusy === funnel.id}
									style={{
										flex: 1,
										fontSize: "14px",
										fontWeight: "500",
									}}
								>
									Edit
								</Button>
								{funnel.status === 'published' ? (
									<Button
										size="3"
										variant="classic"
										onClick={(e) => {
											e.stopPropagation();
											handlePublishToggle(funnel.id, 'draft');
										}}
										disabled={isBusy === funnel.id}
										style={{
											flex: 1,
											fontSize: "14px",
											fontWeight: "500",
										}}
									>
										Unpublish
									</Button>
								) : (
									<Button
										size="3"
										variant="classic"
										onClick={(e) => {
											e.stopPropagation();
											handlePublishToggle(funnel.id, 'published');
										}}
										disabled={isBusy === funnel.id}
										style={{
											flex: 1,
											fontSize: "14px",
											fontWeight: "500",
										}}
									>
										Publish
									</Button>
								)}

							</div>
							</div>
						</Card>
					))}
				</div>
			)}
		</div>
	);
}
