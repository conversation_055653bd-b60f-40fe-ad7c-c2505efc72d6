"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON>ing, Text, Badge, Dialog } from "frosted-ui";
import { useState, useEffect } from "react";

interface WhopProduct {
	id: string;
	title: string;
	headline?: string;
	visibility: string;
	created_at: string;
	member_count?: number;
}

export function ProductsSection() {
	const [products, setProducts] = useState<WhopProduct[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const [isCreating, setIsCreating] = useState(false);
	
	// Form state for creating new product
	const [newProduct, setNewProduct] = useState({
		name: "",
		description: "",
		price: "",
		currency: "usd",
		billingPeriod: "month"
	});

	useEffect(() => {
		loadProducts();
	}, []);

	const loadProducts = async () => {
		try {
			setIsLoading(true);
			setError(null);
			const response = await fetch('/api/products');
			
			if (!response.ok) {
				throw new Error('Failed to fetch products');
			}
			
			const data = await response.json();
			setProducts(data.products || []);
		} catch (err) {
			setError(err instanceof Error ? err.message : 'Failed to load products');
			console.error('Error loading products:', err);
		} finally {
			setIsLoading(false);
		}
	};

	const handleCreateProduct = () => {
		// Redirect to Whop dashboard to create products
		const companyId = process.env.NEXT_PUBLIC_WHOP_COMPANY_ID;
		window.open(`https://whop.com/biz/${companyId}/products`, '_blank');
		setIsCreateDialogOpen(false);
	};

	const formatDate = (dateString: string) => {
		try {
			return new Date(dateString).toLocaleDateString();
		} catch {
			return 'N/A';
		}
	};

	const getVisibilityBadge = (visibility: string) => {
		const color = visibility === 'visible' ? 'green' : 'gray';
		return <Badge color={color}>{visibility}</Badge>;
	};

	return (
		<div style={{ padding: "32px" }}>
			<div className="flex items-center justify-between mb-6">
				<div>
					<Heading size="8">Whop Products</Heading>
					<Text color="gray" size="2">
						Manage your Whop products and connect them to your funnels
					</Text>
				</div>
				<Button 
					size="3"
					onClick={() => setIsCreateDialogOpen(true)}
				>
					<span className="flex items-center gap-2">
						<svg
							width="16"
							height="16"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
							role="img"
							aria-label="Plus icon"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								d="M12 4v16m8-8H4"
							/>
						</svg>
						Create Product
					</span>
				</Button>
			</div>

			{isLoading ? (
				<Card style={{ padding: "48px", textAlign: "center" }}>
					<Text color="gray">Loading products...</Text>
				</Card>
			) : error ? (
				<Card style={{ padding: "48px", textAlign: "center", border: "2px solid var(--red-7)" }}>
					<div className="flex flex-col items-center gap-3">
						<div
							style={{
								width: "64px",
								height: "64px",
								borderRadius: "50%",
								background: "var(--red-3)",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
							}}
						>
							<svg
								width="32"
								height="32"
								viewBox="0 0 24 24"
								fill="none"
								stroke="var(--red-9)"
								strokeWidth="2"
								role="img"
								aria-label="Error icon"
							>
								<circle cx="12" cy="12" r="10" />
								<line x1="12" y1="8" x2="12" y2="12" />
								<line x1="12" y1="16" x2="12.01" y2="16" />
							</svg>
						</div>
						<Heading size="5">Error Loading Products</Heading>
						<Text color="gray" size="2">{error}</Text>
						<Button size="2" onClick={loadProducts}>
							Retry
						</Button>
					</div>
				</Card>
			) : products.length === 0 ? (
				<div
					style={{
						padding: "48px 48px 80px 48px",
						textAlign: "center",
						background: "var(--color-panel-solid)",
						borderRadius: "12px",
					}}
				>
					<div className="flex flex-col items-center gap-3" style={{ marginTop: "32px" }}>
						<div
							style={{
								width: "64px",
								height: "64px",
								borderRadius: "50%",
								background: "var(--cyan-3)",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
							}}
						>
							<svg
								width="32"
								height="32"
								viewBox="0 0 24 24"
								fill="none"
								stroke="var(--cyan-9)"
								strokeWidth="2"
								role="img"
								aria-label="Credit card icon"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3v-8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
								/>
							</svg>
						</div>
						<Heading size="5">No Products Yet</Heading>
						<Text color="gray" size="2" style={{ maxWidth: "400px" }}>
							Create your first product to enable checkout pages and manage your
							product offerings through FunnelFlow AI
						</Text>
						<Button 
							size="2" 
							style={{ marginTop: 8 }}
							onClick={() => setIsCreateDialogOpen(true)}
						>
							Create Your First Product
						</Button>
					</div>
				</div>
			) : (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{products.map((product) => (
						<Card key={product.id} style={{ padding: "24px" }}>
							<div className="flex flex-col gap-3">
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<Heading size="4">{product.title}</Heading>
										<Text color="gray" size="1" style={{ marginTop: "4px" }}>
											ID: {product.id}
										</Text>
									</div>
									{getVisibilityBadge(product.visibility)}
								</div>
								
								{product.headline && (
									<Text color="gray" size="2">
										{product.headline}
									</Text>
								)}
								
								<div className="flex flex-col gap-2 pt-2 border-t border-gray-200">
									{product.member_count !== undefined && (
										<Text size="2" color="gray">
											Members: <strong>{product.member_count}</strong>
										</Text>
									)}
									<Text size="2" color="gray">
										Created: {formatDate(product.created_at)}
									</Text>
									<Button size="1" variant="soft" style={{ marginTop: "8px" }}>
										View on Whop
									</Button>
								</div>
							</div>
						</Card>
					))}
				</div>
			)}

			{/* Create Product Dialog */}
			<Dialog.Root open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
				<Dialog.Content style={{ maxWidth: "500px" }}>
					<Dialog.Title>Create Product on Whop</Dialog.Title>
					<Dialog.Description size="2" style={{ marginBottom: "16px" }}>
						Products are managed directly through your Whop dashboard. Click below to create a new product.
					</Dialog.Description>

					<div className="flex flex-col gap-3">
						<Card style={{ padding: "16px", background: "var(--blue-2)", border: "1px solid var(--blue-6)" }}>
							<Text size="2" color="gray">
								You&apos;ll be redirected to your Whop dashboard where you can create and manage products with full features including pricing, plans, and access controls.
							</Text>
						</Card>
					</div>

					<div className="flex gap-3 mt-4 justify-end">
						<Dialog.Close>
							<Button variant="soft" color="gray">
								Cancel
							</Button>
						</Dialog.Close>
						<Button onClick={handleCreateProduct}>
							Go to Whop Dashboard
						</Button>
					</div>
				</Dialog.Content>
			</Dialog.Root>
		</div>
	);
}
