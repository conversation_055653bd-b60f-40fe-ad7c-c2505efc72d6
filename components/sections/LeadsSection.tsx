"use client";

import { Badge, Card, Heading, Text } from "frosted-ui";

export function LeadsSection() {
	const leads = [
		{
			id: "1",
			name: "<PERSON>",
			email: "<EMAIL>",
			status: "new",
			funnel: "Premium Course Funnel",
			visitedAt: "2024-10-15",
			source: "Landing Page",
		},
		{
			id: "2",
			name: "<PERSON>",
			email: "<EMAIL>",
			status: "contacted",
			funnel: "Coaching Program Funnel",
			visitedAt: "2024-10-14",
			source: "Checkout Page",
		},
		{
			id: "3",
			name: "<PERSON>",
			email: "<EMAIL>",
			status: "converted",
			funnel: "Premium Course Funnel",
			visitedAt: "2024-10-13",
			source: "Upsell Page",
		},
	];

	const getStatusBadge = (status: string) => {
		const statusConfig: Record<string, { color: "green" | "yellow" | "blue" | "gray", label: string }> = {
			new: { color: "blue", label: "New" },
			contacted: { color: "yellow", label: "Contacted" },
			converted: { color: "green", label: "Converted" },
		};
		const config = statusConfig[status] || { color: "gray" as const, label: status };
		return <Badge color={config.color} size="1">{config.label}</Badge>;
	};

	return (
		<div style={{ padding: "32px" }}>
			<div className="mb-6">
				<Heading size="8">Leads</Heading>
				<Text color="gray" size="2">
					Track and manage leads captured from your funnels
				</Text>
			</div>

			<Card style={{ padding: 0, overflow: "hidden" }}>
				<table style={{ width: "100%", borderCollapse: "collapse" }}>
					<thead>
						<tr
							style={{
								background: "var(--gray-3)",
								borderBottom: "1px solid var(--gray-6)",
							}}
						>
							<th
								style={{
									padding: "12px 20px",
									textAlign: "left",
									fontSize: "12px",
									fontWeight: "bold",
									color: "var(--gray-11)",
								}}
							>
								LEAD
							</th>
							<th
								style={{
									padding: "12px 20px",
									textAlign: "left",
									fontSize: "12px",
									fontWeight: "bold",
									color: "var(--gray-11)",
								}}
							>
								FUNNEL
							</th>
							<th
								style={{
									padding: "12px 20px",
									textAlign: "left",
									fontSize: "12px",
									fontWeight: "bold",
									color: "var(--gray-11)",
								}}
							>
								SOURCE
							</th>
							<th
								style={{
									padding: "12px 20px",
									textAlign: "left",
									fontSize: "12px",
									fontWeight: "bold",
									color: "var(--gray-11)",
								}}
							>
								STATUS
							</th>
							<th
								style={{
									padding: "12px 20px",
									textAlign: "left",
									fontSize: "12px",
									fontWeight: "bold",
									color: "var(--gray-11)",
								}}
							>
								VISITED
							</th>
						</tr>
					</thead>
					<tbody>
						{leads.map((lead) => (
							<tr
								key={lead.id}
								style={{
									borderBottom: "1px solid var(--gray-5)",
								}}
							>
								<td style={{ padding: "16px 20px" }}>
									<div>
										<Text size="2" weight="bold" style={{ display: "block" }}>
											{lead.name}
										</Text>
										<Text size="1" color="gray">
											{lead.email}
										</Text>
									</div>
								</td>
								<td style={{ padding: "16px 20px" }}>
									<Text size="2">{lead.funnel}</Text>
								</td>
								<td style={{ padding: "16px 20px" }}>
									<Text size="2" color="gray">{lead.source}</Text>
								</td>
								<td style={{ padding: "16px 20px" }}>
									{getStatusBadge(lead.status)}
								</td>
								<td style={{ padding: "16px 20px" }}>
									<Text size="2">{lead.visitedAt}</Text>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</Card>
		</div>
	);
}
