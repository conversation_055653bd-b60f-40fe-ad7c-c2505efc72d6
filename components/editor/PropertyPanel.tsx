"use client";

import type { EditorComponent } from "@/lib/types/editor";
import { <PERSON><PERSON>, <PERSON><PERSON>, Heading, Text } from "frosted-ui";
import React from "react";
import { VideoUpload } from "@/components/VideoUpload";

interface PropertyPanelProps {
	component: EditorComponent | null;
	onUpdate: (properties: Record<string, unknown>) => void;
	onDelete: () => void;
}

export function PropertyPanel({
	component,
	onUpdate,
	onDelete,
}: PropertyPanelProps) {
	if (!component) {
		return (
			<div
				style={{
					width: "320px",
					height: "100%",
					background: "var(--gray-2)",
					borderLeft: "1px solid var(--gray-6)",
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
					padding: "24px",
					textAlign: "center",
				}}
			>
				<div>
					<svg
						width="48"
						height="48"
						viewBox="0 0 24 24"
						fill="none"
						stroke="var(--gray-8)"
						strokeWidth="2"
						style={{ margin: "0 auto 16px" }}
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
						/>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
						/>
					</svg>
					<Text size="2" color="gray">
						Select a component to edit its properties
					</Text>
				</div>
			</div>
		);
	}

	const handlePropertyChange = (key: string, value: unknown) => {
		onUpdate({
			...component.properties,
			[key]: value,
		});
	};

	// Specialized editors for complex array properties
	const PricingPlansEditor = ({
		plans,
		handlePropertyChange,
	}: {
		plans: Array<{
			name: string;
			price: string;
			priceMonthly?: string;
			priceYearly?: string;
			period?: string;
			description?: string;
			features: string[];
			highlighted?: boolean;
			ctaText?: string;
			ctaUrl?: string;
		}>;
		handlePropertyChange: (key: string, value: unknown) => void;
	}) => {
		const [expandedPlan, setExpandedPlan] = React.useState<number | null>(0);

		const addPlan = () => {
			handlePropertyChange("plans", [
				...plans,
				{
					name: "New Plan",
					price: "$99",
					priceMonthly: "$99",
					priceYearly: "$990",
					period: "/month",
					description: "Plan description",
					features: ["Feature 1", "Feature 2"],
					highlighted: false,
					ctaText: "Get Started",
					ctaUrl: "#",
				},
			]);
			setExpandedPlan(plans.length);
		};

		const updatePlan = (index: number, field: string, value: unknown) => {
			const newPlans = [...plans];
			newPlans[index] = { ...newPlans[index], [field]: value };
			handlePropertyChange("plans", newPlans);
		};

		const deletePlan = (index: number) => {
			handlePropertyChange("plans", plans.filter((_, i) => i !== index));
			setExpandedPlan(null);
		};

		return (
			<div key="plans" style={{ marginBottom: "16px" }}>
				<div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "12px" }}>
					<label style={{ fontSize: "12px", fontWeight: "600", textTransform: "capitalize" }}>
						Pricing Plans
					</label>
					<Button size="1" onClick={addPlan}>+ Add Plan</Button>
				</div>
				<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
					{plans.map((plan, index) => (
						<div
							key={index}
							style={{
								border: "1px solid var(--gray-6)",
								borderRadius: "8px",
								overflow: "hidden",
								background: "var(--gray-1)",
							}}
						>
							<div
								style={{
									padding: "12px",
									background: plan.highlighted ? "var(--accent-3)" : "var(--gray-2)",
									cursor: "pointer",
									display: "flex",
									justifyContent: "space-between",
									alignItems: "center",
								}}
								onClick={() => setExpandedPlan(expandedPlan === index ? null : index)}
							>
								<div>
									<Text size="2" weight="bold">{plan.name}</Text>
									<Text size="1" color="gray" style={{ marginLeft: "8px" }}>{plan.price}{plan.period}</Text>
								</div>
								<div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
									{plan.highlighted && <Badge color="blue" size="1">Popular</Badge>}
									<svg
										width="16"
										height="16"
										viewBox="0 0 24 24"
										style={{
											transform: expandedPlan === index ? "rotate(180deg)" : "rotate(0deg)",
											transition: "transform 0.2s",
										}}
									>
										<path
											d="M6 9l6 6 6-6"
											stroke="currentColor"
											strokeWidth="2"
											strokeLinecap="round"
											strokeLinejoin="round"
											fill="none"
										/>
									</svg>
								</div>
							</div>
							{expandedPlan === index && (
								<div style={{ padding: "12px", display: "flex", flexDirection: "column", gap: "12px" }}>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Plan Name</Text>
										<input
											type="text"
											value={plan.name}
											onChange={(e) => updatePlan(index, "name", e.target.value)}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Price (Current Display)</Text>
										<input
											type="text"
											value={plan.price}
											onChange={(e) => updatePlan(index, "price", e.target.value)}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
										<div>
											<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Monthly Price</Text>
											<input
												type="text"
												value={plan.priceMonthly || ""}
												onChange={(e) => updatePlan(index, "priceMonthly", e.target.value)}
												placeholder="$99"
												style={{
													width: "100%",
													padding: "6px 8px",
													border: "1px solid var(--gray-7)",
													borderRadius: "4px",
													fontSize: "13px",
												}}
											/>
										</div>
										<div>
											<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Yearly Price</Text>
											<input
												type="text"
												value={plan.priceYearly || ""}
												onChange={(e) => updatePlan(index, "priceYearly", e.target.value)}
												placeholder="$990"
												style={{
													width: "100%",
													padding: "6px 8px",
													border: "1px solid var(--gray-7)",
													borderRadius: "4px",
													fontSize: "13px",
												}}
											/>
										</div>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Period</Text>
										<input
											type="text"
											value={plan.period || ""}
											onChange={(e) => updatePlan(index, "period", e.target.value)}
											placeholder="/month"
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Description</Text>
										<input
											type="text"
											value={plan.description || ""}
											onChange={(e) => updatePlan(index, "description", e.target.value)}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Features (one per line)</Text>
										<textarea
											value={plan.features.join("\n")}
											onChange={(e) => updatePlan(index, "features", e.target.value.split("\n").filter(Boolean))}
											rows={4}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
												resize: "vertical",
											}}
										/>
									</div>
									<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
										<div>
											<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>CTA Text</Text>
											<input
												type="text"
												value={plan.ctaText || ""}
												onChange={(e) => updatePlan(index, "ctaText", e.target.value)}
												placeholder="Get Started"
												style={{
													width: "100%",
													padding: "6px 8px",
													border: "1px solid var(--gray-7)",
													borderRadius: "4px",
													fontSize: "13px",
												}}
											/>
										</div>
										<div>
											<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>CTA URL</Text>
											<input
												type="text"
												value={plan.ctaUrl || ""}
												onChange={(e) => updatePlan(index, "ctaUrl", e.target.value)}
												placeholder="#"
												style={{
													width: "100%",
													padding: "6px 8px",
													border: "1px solid var(--gray-7)",
													borderRadius: "4px",
													fontSize: "13px",
												}}
											/>
										</div>
									</div>
									<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
										<input
											type="checkbox"
											id={`highlighted-${index}`}
											checked={plan.highlighted || false}
											onChange={(e) => updatePlan(index, "highlighted", e.target.checked)}
											style={{ width: "16px", height: "16px" }}
										/>
										<label htmlFor={`highlighted-${index}`} style={{ fontSize: "13px" }}>
											Highlight as popular/recommended
										</label>
									</div>
									<Button size="1" color="red" variant="soft" onClick={() => deletePlan(index)}>
										Delete Plan
									</Button>
								</div>
							)}
						</div>
					))}
				</div>
			</div>
		);
	};

	const TestimonialsEditor = ({
		testimonials,
		handlePropertyChange,
	}: {
		testimonials: Array<{
			quote: string;
			author: string;
			role?: string;
			company?: string;
			avatar?: string;
			companyLogo?: string;
			rating?: number;
		}>;
		handlePropertyChange: (key: string, value: unknown) => void;
	}) => {
		const [expandedTestimonial, setExpandedTestimonial] = React.useState<number | null>(0);

		const addTestimonial = () => {
			handlePropertyChange("testimonials", [
				...testimonials,
				{
					quote: "This is an amazing product!",
					author: "Customer Name",
					role: "Position",
					company: "Company Name",
					avatar: "",
					companyLogo: "",
					rating: 5,
				},
			]);
			setExpandedTestimonial(testimonials.length);
		};

		const updateTestimonial = (index: number, field: string, value: unknown) => {
			const newTestimonials = [...testimonials];
			newTestimonials[index] = { ...newTestimonials[index], [field]: value };
			handlePropertyChange("testimonials", newTestimonials);
		};

		const deleteTestimonial = (index: number) => {
			handlePropertyChange("testimonials", testimonials.filter((_, i) => i !== index));
			setExpandedTestimonial(null);
		};

		return (
			<div key="testimonials" style={{ marginBottom: "16px" }}>
				<div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "12px" }}>
					<label style={{ fontSize: "12px", fontWeight: "600", textTransform: "capitalize" }}>
						Testimonials
					</label>
					<Button size="1" onClick={addTestimonial}>+ Add Testimonial</Button>
				</div>
				<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
					{testimonials.map((testimonial, index) => (
						<div
							key={index}
							style={{
								border: "1px solid var(--gray-6)",
								borderRadius: "8px",
								overflow: "hidden",
								background: "var(--gray-1)",
							}}
						>
							<div
								style={{
									padding: "12px",
									background: "var(--gray-2)",
									cursor: "pointer",
									display: "flex",
									justifyContent: "space-between",
									alignItems: "center",
								}}
								onClick={() => setExpandedTestimonial(expandedTestimonial === index ? null : index)}
							>
								<div>
									<Text size="2" weight="bold">{testimonial.author}</Text>
									{testimonial.role && <Text size="1" color="gray" style={{ display: "block" }}>{testimonial.role}</Text>}
								</div>
								<svg
									width="16"
									height="16"
									viewBox="0 0 24 24"
									style={{
										transform: expandedTestimonial === index ? "rotate(180deg)" : "rotate(0deg)",
										transition: "transform 0.2s",
									}}
								>
									<path
										d="M6 9l6 6 6-6"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
										fill="none"
									/>
								</svg>
							</div>
							{expandedTestimonial === index && (
								<div style={{ padding: "12px", display: "flex", flexDirection: "column", gap: "12px" }}>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Quote</Text>
										<textarea
											value={testimonial.quote}
											onChange={(e) => updateTestimonial(index, "quote", e.target.value)}
											rows={3}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
												resize: "vertical",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Author Name</Text>
										<input
											type="text"
											value={testimonial.author}
											onChange={(e) => updateTestimonial(index, "author", e.target.value)}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
										<div>
											<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Role/Position</Text>
											<input
												type="text"
												value={testimonial.role || ""}
												onChange={(e) => updateTestimonial(index, "role", e.target.value)}
												placeholder="CEO"
												style={{
													width: "100%",
													padding: "6px 8px",
													border: "1px solid var(--gray-7)",
													borderRadius: "4px",
													fontSize: "13px",
												}}
											/>
										</div>
										<div>
											<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Company</Text>
											<input
												type="text"
												value={testimonial.company || ""}
												onChange={(e) => updateTestimonial(index, "company", e.target.value)}
												placeholder="Company Name"
												style={{
													width: "100%",
													padding: "6px 8px",
													border: "1px solid var(--gray-7)",
													borderRadius: "4px",
													fontSize: "13px",
												}}
											/>
										</div>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Avatar URL</Text>
										<input
											type="text"
											value={testimonial.avatar || ""}
											onChange={(e) => updateTestimonial(index, "avatar", e.target.value)}
											placeholder="https://example.com/avatar.jpg"
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Company Logo URL</Text>
										<input
											type="text"
											value={testimonial.companyLogo || ""}
											onChange={(e) => updateTestimonial(index, "companyLogo", e.target.value)}
											placeholder="https://example.com/logo.png"
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Rating (1-5)</Text>
										<input
											type="number"
											min="1"
											max="5"
											value={testimonial.rating || 5}
											onChange={(e) => updateTestimonial(index, "rating", Number(e.target.value))}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<Button size="1" color="red" variant="soft" onClick={() => deleteTestimonial(index)}>
										Delete Testimonial
									</Button>
								</div>
							)}
						</div>
					))}
				</div>
			</div>
		);
	};

	const FormFieldsEditor = ({
		fields,
		handlePropertyChange,
	}: {
		fields: Array<{
			type: "text" | "email" | "textarea" | "tel" | "number" | "dropdown" | "checkbox";
			label: string;
			placeholder?: string;
			required?: boolean;
			options?: string[];
		}>;
		handlePropertyChange: (key: string, value: unknown) => void;
	}) => {
		const [expandedField, setExpandedField] = React.useState<number | null>(0);

		const addField = () => {
			handlePropertyChange("fields", [
				...fields,
				{
					type: "text",
					label: "New Field",
					placeholder: "",
					required: false,
					options: [],
				},
			]);
			setExpandedField(fields.length);
		};

		const updateField = (index: number, field: string, value: unknown) => {
			const newFields = [...fields];
			newFields[index] = { ...newFields[index], [field]: value };
			handlePropertyChange("fields", newFields);
		};

		const deleteField = (index: number) => {
			handlePropertyChange("fields", fields.filter((_, i) => i !== index));
			setExpandedField(null);
		};

		return (
			<div key="fields" style={{ marginBottom: "16px" }}>
				<div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "12px" }}>
					<label style={{ fontSize: "12px", fontWeight: "600", textTransform: "capitalize" }}>
						Form Fields
					</label>
					<Button size="1" onClick={addField}>+ Add Field</Button>
				</div>
				<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
					{fields.map((field, index) => (
						<div
							key={index}
							style={{
								border: "1px solid var(--gray-6)",
								borderRadius: "8px",
								overflow: "hidden",
								background: "var(--gray-1)",
							}}
						>
							<div
								style={{
									padding: "12px",
									background: "var(--gray-2)",
									cursor: "pointer",
									display: "flex",
									justifyContent: "space-between",
									alignItems: "center",
								}}
								onClick={() => setExpandedField(expandedField === index ? null : index)}
							>
								<div>
									<Text size="2" weight="bold">{field.label}</Text>
									<Text size="1" color="gray" style={{ marginLeft: "8px" }}>({field.type})</Text>
								</div>
								<svg
									width="16"
									height="16"
									viewBox="0 0 24 24"
									style={{
										transform: expandedField === index ? "rotate(180deg)" : "rotate(0deg)",
										transition: "transform 0.2s",
									}}
								>
									<path
										d="M6 9l6 6 6-6"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
										fill="none"
									/>
								</svg>
							</div>
							{expandedField === index && (
								<div style={{ padding: "12px", display: "flex", flexDirection: "column", gap: "12px" }}>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Field Label</Text>
										<input
											type="text"
											value={field.label}
											onChange={(e) => updateField(index, "label", e.target.value)}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Field Type</Text>
										<select
											value={field.type}
											onChange={(e) => updateField(index, "type", e.target.value)}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										>
											<option value="text">Text</option>
											<option value="email">Email</option>
											<option value="textarea">Textarea</option>
											<option value="tel">Phone</option>
											<option value="number">Number</option>
											<option value="dropdown">Dropdown</option>
											<option value="checkbox">Checkbox</option>
										</select>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Placeholder</Text>
										<input
											type="text"
											value={field.placeholder || ""}
											onChange={(e) => updateField(index, "placeholder", e.target.value)}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									{(field.type === "dropdown") && (
										<div>
											<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Options (one per line)</Text>
											<textarea
												value={(field.options || []).join("\n")}
												onChange={(e) => updateField(index, "options", e.target.value.split("\n").filter(Boolean))}
												rows={3}
												placeholder="Option 1\nOption 2\nOption 3"
												style={{
													width: "100%",
													padding: "6px 8px",
													border: "1px solid var(--gray-7)",
													borderRadius: "4px",
													fontSize: "13px",
													resize: "vertical",
												}}
											/>
										</div>
									)}
									<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
										<input
											type="checkbox"
											id={`required-${index}`}
											checked={field.required || false}
											onChange={(e) => updateField(index, "required", e.target.checked)}
											style={{ width: "16px", height: "16px" }}
										/>
										<label htmlFor={`required-${index}`} style={{ fontSize: "13px" }}>
											Required field
										</label>
									</div>
									<Button size="1" color="red" variant="soft" onClick={() => deleteField(index)}>
										Delete Field
									</Button>
								</div>
							)}
						</div>
					))}
				</div>
			</div>
		);
	};

	const FeaturesEditor = ({
		features,
		handlePropertyChange,
	}: {
		features: Array<{
			title: string;
			description: string;
			icon?: string;
			iconUrl?: string;
			ctaText?: string;
			ctaUrl?: string;
		}>;
		handlePropertyChange: (key: string, value: unknown) => void;
	}) => {
		const [expandedFeature, setExpandedFeature] = React.useState<number | null>(0);

		const addFeature = () => {
			handlePropertyChange("features", [
				...features,
				{
					title: "New Feature",
					description: "Feature description",
					icon: "⭐",
					iconUrl: "",
					ctaText: "",
					ctaUrl: "",
				},
			]);
			setExpandedFeature(features.length);
		};

		const updateFeature = (index: number, field: string, value: unknown) => {
			const newFeatures = [...features];
			newFeatures[index] = { ...newFeatures[index], [field]: value };
			handlePropertyChange("features", newFeatures);
		};

		const deleteFeature = (index: number) => {
			handlePropertyChange("features", features.filter((_, i) => i !== index));
			setExpandedFeature(null);
		};

		return (
			<div key="features" style={{ marginBottom: "16px" }}>
				<div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "12px" }}>
					<label style={{ fontSize: "12px", fontWeight: "600", textTransform: "capitalize" }}>
						Features
					</label>
					<Button size="1" onClick={addFeature}>+ Add Feature</Button>
				</div>
				<div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
					{features.map((feature, index) => (
						<div
							key={index}
							style={{
								border: "1px solid var(--gray-6)",
								borderRadius: "8px",
								overflow: "hidden",
								background: "var(--gray-1)",
							}}
						>
							<div
								style={{
									padding: "12px",
									background: "var(--gray-2)",
									cursor: "pointer",
									display: "flex",
									justifyContent: "space-between",
									alignItems: "center",
								}}
								onClick={() => setExpandedFeature(expandedFeature === index ? null : index)}
							>
								<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
									<span style={{ fontSize: "20px" }}>{feature.icon || "⭐"}</span>
									<Text size="2" weight="bold">{feature.title}</Text>
								</div>
								<svg
									width="16"
									height="16"
									viewBox="0 0 24 24"
									style={{
										transform: expandedFeature === index ? "rotate(180deg)" : "rotate(0deg)",
										transition: "transform 0.2s",
									}}
								>
									<path
										d="M6 9l6 6 6-6"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
										fill="none"
									/>
								</svg>
							</div>
							{expandedFeature === index && (
								<div style={{ padding: "12px", display: "flex", flexDirection: "column", gap: "12px" }}>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Feature Title</Text>
										<input
											type="text"
											value={feature.title}
											onChange={(e) => updateFeature(index, "title", e.target.value)}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Description</Text>
										<textarea
											value={feature.description}
											onChange={(e) => updateFeature(index, "description", e.target.value)}
											rows={3}
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
												resize: "vertical",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Icon (emoji or text)</Text>
										<input
											type="text"
											value={feature.icon || ""}
											onChange={(e) => updateFeature(index, "icon", e.target.value)}
											placeholder="⭐"
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div>
										<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>Icon URL (optional)</Text>
										<input
											type="text"
											value={feature.iconUrl || ""}
											onChange={(e) => updateFeature(index, "iconUrl", e.target.value)}
											placeholder="https://example.com/icon.svg"
											style={{
												width: "100%",
												padding: "6px 8px",
												border: "1px solid var(--gray-7)",
												borderRadius: "4px",
												fontSize: "13px",
											}}
										/>
									</div>
									<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
										<div>
											<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>CTA Text</Text>
											<input
												type="text"
												value={feature.ctaText || ""}
												onChange={(e) => updateFeature(index, "ctaText", e.target.value)}
												placeholder="Learn more"
												style={{
													width: "100%",
													padding: "6px 8px",
													border: "1px solid var(--gray-7)",
													borderRadius: "4px",
													fontSize: "13px",
												}}
											/>
										</div>
										<div>
											<Text size="1" weight="medium" style={{ marginBottom: "4px", display: "block" }}>CTA URL</Text>
											<input
												type="text"
												value={feature.ctaUrl || ""}
												onChange={(e) => updateFeature(index, "ctaUrl", e.target.value)}
												placeholder="#"
												style={{
													width: "100%",
													padding: "6px 8px",
													border: "1px solid var(--gray-7)",
													borderRadius: "4px",
													fontSize: "13px",
												}}
											/>
										</div>
									</div>
									<Button size="1" color="red" variant="soft" onClick={() => deleteFeature(index)}>
										Delete Feature
									</Button>
								</div>
							)}
						</div>
					))}
				</div>
			</div>
		);
	};

	const renderPropertyInput = (key: string, value: unknown) => {
		const inputStyle = {
			width: "100%",
			padding: "8px 12px",
			border: "1px solid var(--gray-7)",
			borderRadius: "6px",
			fontSize: "14px",
			background: "var(--gray-1)",
		};

		// Special handling for pricing plans
		if (key === "plans" && Array.isArray(value) && component.type === "pricing_section") {
			return (
				<PricingPlansEditor
					key={key}
					plans={value as Array<{
						name: string;
						price: string;
						period?: string;
						description?: string;
						features: string[];
						highlighted?: boolean;
						ctaText?: string;
						ctaUrl?: string;
					}>}
					handlePropertyChange={handlePropertyChange}
				/>
			);
		}

		// Special handling for testimonials
		if (key === "testimonials" && Array.isArray(value) && component.type === "testimonials_section") {
			return (
				<TestimonialsEditor
					key={key}
					testimonials={value as Array<{
						quote: string;
						author: string;
						role?: string;
						avatar?: string;
						rating?: number;
					}>}
					handlePropertyChange={handlePropertyChange}
				/>
			);
		}

		// Special handling for form fields
		if (key === "fields" && Array.isArray(value) && component.type === "form_section") {
			return (
				<FormFieldsEditor
					key={key}
					fields={value as Array<{
						type: "text" | "email" | "textarea" | "tel" | "number";
						label: string;
						placeholder?: string;
						required?: boolean;
					}>}
					handlePropertyChange={handlePropertyChange}
				/>
			);
		}

		// Special handling for features
		if (key === "features" && Array.isArray(value) && component.type === "features_section") {
			return (
				<FeaturesEditor
					key={key}
					features={value as Array<{
						title: string;
						description: string;
						icon?: string;
					}>}
					handlePropertyChange={handlePropertyChange}
				/>
			);
		}

		// Array properties (special handling)
		if (Array.isArray(value)) {
			// Simple string arrays can be edited as comma-separated
			if (value.every(item => typeof item === "string")) {
				return (
					<div key={key} style={{ marginBottom: "16px" }}>
						<label
							style={{
								fontSize: "12px",
								fontWeight: "600",
								marginBottom: "8px",
								display: "block",
								textTransform: "capitalize",
							}}
						>
							{key.replace(/_/g, " ")}
						</label>
						<textarea
							value={(value as string[]).join("\n")}
							onChange={(e) => handlePropertyChange(key, e.target.value.split("\n").filter(Boolean))}
							rows={Math.min(value.length + 1, 6)}
							placeholder="One item per line"
							style={{
								...inputStyle,
								resize: "vertical",
								fontFamily: "inherit",
							}}
						/>
						<Text size="1" color="gray" style={{ marginTop: "4px" }}>
							{value.length} items
						</Text>
					</div>
				);
			}
			
			// Complex arrays - show count with JSON editor
			return (
				<div key={key} style={{ marginBottom: "16px" }}>
					<label
						style={{
							fontSize: "12px",
							fontWeight: "600",
							marginBottom: "8px",
							display: "block",
							textTransform: "capitalize",
						}}
					>
						{key.replace(/_/g, " ")}
					</label>
					<textarea
						value={JSON.stringify(value, null, 2)}
						onChange={(e) => {
							try {
								const parsed = JSON.parse(e.target.value);
								handlePropertyChange(key, parsed);
							} catch {
								// Invalid JSON, ignore
							}
						}}
						rows={6}
						placeholder="JSON array"
						style={{
							...inputStyle,
							resize: "vertical",
							fontFamily: "monospace",
							fontSize: "12px",
						}}
					/>
					<Text size="1" color="gray" style={{ marginTop: "4px" }}>
						{value.length} items (JSON format)
					</Text>
				</div>
			);
		}

		// Object properties (special handling)
		if (typeof value === "object" && value !== null) {
			return (
				<div key={key} style={{ marginBottom: "16px" }}>
					<label
						style={{
							fontSize: "12px",
							fontWeight: "600",
							marginBottom: "8px",
							display: "block",
							textTransform: "capitalize",
						}}
					>
						{key.replace(/_/g, " ")}
					</label>
					<textarea
						value={JSON.stringify(value, null, 2)}
						onChange={(e) => {
							try {
								const parsed = JSON.parse(e.target.value);
								handlePropertyChange(key, parsed);
							} catch {
								// Invalid JSON, ignore
							}
						}}
						rows={4}
						placeholder="JSON object"
						style={{
							...inputStyle,
							resize: "vertical",
							fontFamily: "monospace",
							fontSize: "12px",
						}}
					/>
					<Text size="1" color="gray" style={{ marginTop: "4px" }}>
						Object (JSON format)
					</Text>
				</div>
			);
		}

		// Boolean properties
		if (typeof value === "boolean") {
			return (
				<div
					key={key}
					style={{
						marginBottom: "16px",
						padding: "12px",
						background: "var(--gray-2)",
						borderRadius: "8px",
						display: "flex",
						alignItems: "center",
						justifyContent: "space-between",
					}}
				>
					<label
						style={{
							fontSize: "12px",
							fontWeight: "600",
							textTransform: "capitalize",
							cursor: "pointer",
						}}
						htmlFor={`prop-${key}`}
					>
						{key.replace(/_/g, " ")}
					</label>
					<input
						id={`prop-${key}`}
						type="checkbox"
						checked={value}
						onChange={(e) => handlePropertyChange(key, e.target.checked)}
						style={{
							width: "20px",
							height: "20px",
							cursor: "pointer",
							accentColor: "var(--accent-9)",
						}}
					/>
				</div>
			);
		}

		// Number properties
		if (typeof value === "number") {
			return (
				<div key={key} style={{ marginBottom: "16px" }}>
					<label
						style={{
							fontSize: "12px",
							fontWeight: "600",
							marginBottom: "8px",
							display: "block",
							textTransform: "capitalize",
						}}
					>
						{key.replace(/_/g, " ")}
					</label>
					<input
						type="number"
						value={value}
						onChange={(e) =>
							handlePropertyChange(key, Number.parseFloat(e.target.value))
						}
						style={inputStyle}
					/>
				</div>
			);
		}

		// Color properties (detect by key name)
		if (typeof value === "string" && (key.includes("color") || key.includes("Color"))) {
			return (
				<div key={key} style={{ marginBottom: "16px" }}>
					<label
						style={{
							fontSize: "12px",
							fontWeight: "600",
							marginBottom: "8px",
							display: "block",
							textTransform: "capitalize",
						}}
					>
						{key.replace(/_/g, " ")}
					</label>
					<div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
						<input
							type="color"
							value={value}
							onChange={(e) => handlePropertyChange(key, e.target.value)}
							style={{
								width: "48px",
								height: "36px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								cursor: "pointer",
							}}
						/>
						<input
							type="text"
							value={value}
							onChange={(e) => handlePropertyChange(key, e.target.value)}
							style={{
								...inputStyle,
								flex: 1,
								fontFamily: "monospace",
							}}
						/>
					</div>
				</div>
			);
		}

		// Select dropdown for specific properties
		if (key === "alignment" || key === "level" || key === "variant" || key === "size" || key === "layout" || key === "animationType" || key === "containerStyle") {
			let options: string[] = [];
			
			if (key === "alignment") {
				options = ["left", "center", "right"];
			} else if (key === "level") {
				options = ["h1", "h2", "h3", "h4", "h5", "h6"];
			} else if (key === "variant") {
				options = ["solid", "soft", "outline", "ghost"];
			} else if (key === "size") {
				options = ["sm", "md", "lg"];
			} else if (key === "layout") {
				// Different layout options based on component type
				if (component.type === "hero_section") {
					options = ["centered", "left", "right", "split"];
				} else if (component.type === "features_section") {
					options = ["grid", "cards", "carousel", "columns"];
				} else if (component.type === "testimonials_section") {
					options = ["carousel", "grid", "single"];
				} else if (component.type === "footer_section") {
					options = ["columns", "centered", "minimal"];
				} else {
					options = ["default", "grid", "cards"];
				}
			} else if (key === "animationType") {
				options = ["fade", "slide", "zoom", "none"];
			} else if (key === "containerStyle") {
				options = ["default", "card", "minimal"];
			}

			return (
				<div key={key} style={{ marginBottom: "16px" }}>
					<label
						style={{
							fontSize: "12px",
							fontWeight: "600",
							marginBottom: "8px",
							display: "block",
							textTransform: "capitalize",
						}}
					>
						{key.replace(/_/g, " ")}
					</label>
					<select
						value={String(value)}
						onChange={(e) => handlePropertyChange(key, e.target.value)}
						style={{
							...inputStyle,
							cursor: "pointer",
						}}
					>
						{options.map((option) => (
							<option key={option} value={option}>
								{option}
							</option>
						))}
					</select>
				</div>
			);
		}

		// String properties (could be textarea for long text)
		const isLongText =
			key === "text" || key === "content" || key === "html" || key === "code" || key === "quote" || key === "description";
		if (isLongText && typeof value === "string") {
			return (
				<div key={key} style={{ marginBottom: "16px" }}>
					<label
						style={{
							fontSize: "12px",
							fontWeight: "600",
							marginBottom: "8px",
							display: "block",
							textTransform: "capitalize",
						}}
					>
						{key.replace(/_/g, " ")}
					</label>
					<textarea
						value={value}
						onChange={(e) => handlePropertyChange(key, e.target.value)}
						rows={Math.min(Math.max(4, Math.ceil(value.length / 50)), 10)}
						style={{
							...inputStyle,
							resize: "vertical",
							fontFamily: key === "html" || key === "code" ? "monospace" : "inherit",
						}}
					/>
				</div>
			);
		}

		// Video URL properties - special video upload component
		if (key === "videoUrl") {
			return (
				<div key={key} style={{ marginBottom: "16px" }}>
					<label
						style={{
							fontSize: "12px",
							fontWeight: "600",
							marginBottom: "8px",
							display: "block",
							textTransform: "capitalize",
						}}
					>
						Video
					</label>
					<VideoUpload
						currentVideoUrl={String(value ?? '')}
						onVideoUploaded={(videoUrl) => handlePropertyChange(key, videoUrl)}
					/>
				</div>
			);
		}

		// URL properties
		if (typeof value === "string" && (key.includes("url") || key.includes("Url") || key.includes("src") || key === "action")) {
			return (
				<div key={key} style={{ marginBottom: "16px" }}>
					<label
						style={{
							fontSize: "12px",
							fontWeight: "600",
							marginBottom: "8px",
							display: "block",
							textTransform: "capitalize",
						}}
					>
						{key.replace(/_/g, " ")}
					</label>
					<input
						type="url"
						value={String(value ?? '')}
						onChange={(e) => handlePropertyChange(key, e.target.value)}
						placeholder="https://"
						style={inputStyle}
					/>
				</div>
			);
		}

		// Default: text input
		return (
			<div key={key} style={{ marginBottom: "16px" }}>
				<label
					style={{
						fontSize: "12px",
						fontWeight: "600",
						marginBottom: "8px",
						display: "block",
						textTransform: "capitalize",
					}}
				>
					{key.replace(/_/g, " ")}
				</label>
			<input
				type="text"
				value={String(value ?? '')}
				onChange={(e) => handlePropertyChange(key, e.target.value)}
				style={inputStyle}
			/>
			</div>
		);
	};

	return (
		<div
			className="whop-scrollbar"
			style={{
				width: "340px",
				height: "100%",
				background: "var(--color-panel-solid)",
				borderLeft: "1px solid var(--gray-5)",
				display: "flex",
				flexDirection: "column",
				overflow: "hidden",
				boxShadow: "-2px 0 12px rgba(0, 0, 0, 0.04)",
			}}
		>
			{/* Header */}
			<div style={{ padding: "16px", borderBottom: "1px solid var(--gray-6)" }}>
				<div className="flex items-center justify-between mb-2">
					<Heading size="4">Properties</Heading>
					<Button size="1" color="red" variant="ghost" onClick={onDelete}>
						Delete
					</Button>
				</div>
				<div className="flex items-center gap-2">
					<Badge color="blue" size="1">
						{component.type}
					</Badge>
					<Text size="1" color="gray">
						{component.name}
					</Text>
				</div>
			</div>

			{/* Properties */}
			<div
				style={{
					flex: 1,
					overflowY: "auto",
					padding: "16px",
				}}
			>
				{/* Special handling for video elements */}
				{component.type === "video" && (
					<div style={{ marginBottom: "24px" }}>
						<Text size="2" weight="medium" style={{ marginBottom: "12px", display: "block" }}>
							Video Settings
						</Text>
						<div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
							{renderPropertyInput("src", component.properties.src)}
							{renderPropertyInput("autoplay", component.properties.autoplay)}
							{renderPropertyInput("controls", component.properties.controls)}
							{renderPropertyInput("loop", component.properties.loop)}
						</div>
					</div>
				)}


				{/* Render all other properties */}
				{Object.entries(component.properties)
					.filter(([key]) => {
						// Filter out video properties for video elements
						if (component.type === "video" && ["videoUrl", "autoplay", "controls", "loop", "muted"].includes(key)) {
							return false;
						}
						return true;
					})
					.map(([key, value]) => renderPropertyInput(key, value))
				}
			</div>

			{/* Footer */}
			<div
				style={{
					padding: "12px 16px",
					borderTop: "1px solid var(--gray-6)",
					background: "var(--gray-1)",
				}}
			>
				<Text size="1" color="gray">
					Component ID: {component.id.split("-").slice(-1)[0]}
				</Text>
			</div>
		</div>
	);
}
