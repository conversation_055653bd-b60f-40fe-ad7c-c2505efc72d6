"use client";

import { createDefaultComponent } from "@/lib/editor/component-definitions";
import type {
	ComponentType,
	EditorComponent,
	EditorPage,
} from "@/lib/types/editor";
import type { FunnelSchema } from "@/lib/types/funnel";
import {
	DndContext,
	DragEndEvent,
	DragOverlay,
	DragStartEvent,
	MouseSensor,
	TouchSensor,
	closestCenter,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	arrayMove,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Badge, Button, Heading, Text } from "frosted-ui";
import React, { useState } from "react";
import { ComponentPalette } from "./ComponentPalette";
import { ComponentRenderer } from "./ComponentRenderer";
import { InlineTooltip } from "./InlineTooltip";

interface FunnelEditorProps {
	funnel: FunnelSchema;
	onSave: (funnel: FunnelSchema) => void;
	onClose: () => void;
	initialPageIndex?: number;
}

interface SortableComponentProps {
	component: EditorComponent;
	isSelected: boolean;
	onSelect: () => void;
	onEdit: (component: EditorComponent, event: React.MouseEvent) => void;
}

function SortableComponent({
	component,
	isSelected,
	onSelect,
	onEdit,
}: SortableComponentProps) {
	const { attributes, listeners, setNodeRef, transform, transition } =
		useSortable({ id: component.id });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		// No extra styling - components should flow naturally like a real website
		display: "block",
		width: "100%",
	};

	return (
		<div ref={setNodeRef} style={style} {...attributes}>
			<ComponentRenderer
				component={component}
				isSelected={isSelected}
				onSelect={onSelect}
			/>
		</div>
	);
}

export function FunnelEditor({ funnel, onSave, onClose, initialPageIndex = 0 }: FunnelEditorProps) {
	// Convert funnel pages to editor format
	const initialPages: EditorPage[] = funnel.pages.map((page) => ({
		id: page.id,
		name: page.name,
		type: page.type,
		components: [], // Start with empty components for drag-and-drop
		order: page.order,
		whopPlanIds: page.whopPlanIds,
	}));

	const [pages, setPages] = useState<EditorPage[]>(initialPages);
	const [currentPageIndex, setCurrentPageIndex] = useState(initialPageIndex);
	const [selectedComponentId, setSelectedComponentId] = useState<string | null>(
		null,
	);
	const [activeId, setActiveId] = useState<string | null>(null);
	const [tooltipState, setTooltipState] = useState<{
		component: EditorComponent;
		position: { x: number; y: number };
	} | null>(null);

	const currentPage = pages[currentPageIndex];
	const selectedComponent = currentPage.components.find(
		(c) => c.id === selectedComponentId,
	);
	

	const sensors = useSensors(
		useSensor(MouseSensor, {
			activationConstraint: {
				distance: 20,
			},
		}),
		useSensor(TouchSensor, {
			activationConstraint: {
				delay: 500,
				tolerance: 20,
			},
		}),
	);

	const handleAddComponent = (componentType: ComponentType) => {
		const newComponent = createDefaultComponent(componentType);
		if (!newComponent) return;

		const updatedPages = [...pages];
		updatedPages[currentPageIndex].components.push(newComponent);
		setPages(updatedPages);
		setSelectedComponentId(newComponent.id);
	};

	const handleDragStart = (event: DragStartEvent) => {
		setActiveId(event.active.id as string);
	};

	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (over && active.id !== over.id) {
			const updatedPages = [...pages];
			const components = updatedPages[currentPageIndex].components;
			const oldIndex = components.findIndex((c) => c.id === active.id);
			const newIndex = components.findIndex((c) => c.id === over.id);

			updatedPages[currentPageIndex].components = arrayMove(
				components,
				oldIndex,
				newIndex,
			);
			setPages(updatedPages);
		}

		setActiveId(null);
	};


	const handleDeleteComponent = (componentId?: string) => {
		const idToDelete = componentId || selectedComponentId;
		if (!idToDelete) return;

		const updatedPages = [...pages];
		updatedPages[currentPageIndex].components = updatedPages[
			currentPageIndex
		].components.filter((c) => c.id !== idToDelete);
		setPages(updatedPages);
		setSelectedComponentId(null);
		setTooltipState(null);
	};

	const handleEditComponent = (component: EditorComponent, event: React.MouseEvent) => {
		setTooltipState({
			component,
			position: { x: event.clientX, y: event.clientY },
		});
		setSelectedComponentId(component.id);
	};

	const handleUpdateComponent = (componentId: string, properties: Record<string, unknown>) => {
		const updatedPages = [...pages];
		const componentIndex = updatedPages[currentPageIndex].components.findIndex(
			(c) => c.id === componentId,
		);

		if (componentIndex !== -1) {
			updatedPages[currentPageIndex].components[componentIndex] = {
				...updatedPages[currentPageIndex].components[componentIndex],
				properties: {
					...updatedPages[currentPageIndex].components[componentIndex].properties,
					...properties,
				},
			} as EditorComponent;
			setPages(updatedPages);
		}
	};

	const handleSave = () => {
		// Convert editor pages back to funnel schema
		const updatedFunnel: FunnelSchema = {
			...funnel,
			pages: pages.map((page) => ({
				id: page.id,
				name: page.name,
				type: page.type,
				sections: [], // Components will be rendered differently
				order: page.order,
				whopPlanIds: page.whopPlanIds,
			})),
		};

		onSave(updatedFunnel);
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		const componentType = e.dataTransfer.getData("componentType");
		if (componentType) {
			handleAddComponent(componentType as ComponentType);
		}
	};

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
	};

	
	return (
		<div
			style={{
				height: "100vh",
				display: "flex",
				flexDirection: "column",
				background: "#f8fafc", // Light gray editor background
			}}
		>
			{/* Floating Header */}
			<div
				style={{
					position: "fixed",
					top: "20px",
					left: "20px",
					right: "20px",
					height: "64px",
					background: "rgba(255, 255, 255, 0.95)",
					backdropFilter: "blur(12px)",
					WebkitBackdropFilter: "blur(12px)",
					border: "1px solid rgba(0, 0, 0, 0.1)",
					borderRadius: "12px",
					display: "flex",
					alignItems: "center",
					justifyContent: "space-between",
					padding: "0 24px",
					zIndex: 1000,
					boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
				}}
			>
				<div className="flex items-center gap-4">
					<Button size="2" variant="ghost" onClick={onClose}>
						← Back
					</Button>
					<div>
						<Heading size="5">{funnel.title}</Heading>
						<Text size="1" color="gray">
							Editing {currentPage.name}
						</Text>
					</div>
				</div>
				<div className="flex items-center gap-2">
					<Badge color="blue" size="2">
						{currentPage.components.length} components
					</Badge>
					<Button size="2" variant="soft" onClick={handleSave}>
						Save Changes
					</Button>
					<Button size="2" variant="solid" onClick={handleSave}>
						Save & Close
					</Button>
				</div>
			</div>

			{/* Main Content */}
			<div style={{ display: "flex", flex: 1, overflow: "hidden", paddingTop: "104px" }}>
				{/* Floating Component Palette */}
				<div
					style={{
						position: "fixed",
						left: "20px",
						top: "104px",
						bottom: "20px",
						width: "280px",
						background: "rgba(255, 255, 255, 0.95)",
						backdropFilter: "blur(12px)",
						WebkitBackdropFilter: "blur(12px)",
						border: "1px solid rgba(0, 0, 0, 0.1)",
						borderRadius: "12px",
						zIndex: 999,
						boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
					}}
				>
					<ComponentPalette onAddComponent={handleAddComponent} />
				</div>

				{/* Floating Page Tabs */}
				<div
					style={{
						position: "fixed",
						left: "320px",
						top: "104px",
						right: "20px",
						height: "48px",
						background: "rgba(255, 255, 255, 0.95)",
						backdropFilter: "blur(12px)",
						WebkitBackdropFilter: "blur(12px)",
						border: "1px solid rgba(0, 0, 0, 0.1)",
						borderRadius: "12px",
						padding: "8px 16px",
						display: "flex",
						gap: "8px",
						overflowX: "auto",
						alignItems: "center",
						zIndex: 998,
						boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
					}}
				>
					{pages.map((page, index) => (
						<button
							key={page.id}
							type="button"
							onClick={() => {
								setCurrentPageIndex(index);
								setSelectedComponentId(null);
							}}
							style={{
								padding: "6px 12px",
								borderRadius: "8px",
								border:
									currentPageIndex === index
										? "2px solid var(--accent-9)"
										: "1px solid rgba(0, 0, 0, 0.1)",
								background:
									currentPageIndex === index
										? "var(--accent-2)"
										: "transparent",
								cursor: "pointer",
								whiteSpace: "nowrap",
								fontSize: "14px",
								fontWeight: currentPageIndex === index ? "600" : "400",
							}}
						>
							{page.name}
							{page.components.length > 0 && (
								<Badge
									color={currentPageIndex === index ? "blue" : "gray"}
									size="1"
									style={{ marginLeft: "8px" }}
								>
									{page.components.length}
								</Badge>
							)}
						</button>
					))}
				</div>

				{/* Website Canvas - Pure White Like Real Site */}
				<div
					style={{
						flex: 1,
						display: "flex",
						justifyContent: "center",
						alignItems: "flex-start",
						paddingTop: "68px", // Space for page tabs
						paddingLeft: "320px", // Space for palette
						paddingRight: "20px", // Minimal right padding
						background: "transparent",
						overflowY: "auto",
					}}
				>
					{/* Actual Website Canvas - Full Width */}
					<div
						onDrop={handleDrop}
						onDragOver={handleDragOver}
						style={{
							width: "100%",
							minHeight: "100vh",
							background: "white", // Pure white website background
							position: "relative",
							// No max-width constraint - let components take full available space
						}}
					>
						{currentPage.components.length === 0 ? (
							<div
								style={{
									minHeight: "60vh",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									border: "2px dashed rgba(0, 0, 0, 0.1)",
									borderRadius: "12px",
									background: "rgba(248, 250, 252, 0.3)",
									margin: "40px",
								}}
							>
								<div style={{ textAlign: "center" }}>
									<svg
										width="64"
										height="64"
										viewBox="0 0 24 24"
										fill="none"
										stroke="rgba(0, 0, 0, 0.3)"
										strokeWidth="2"
										style={{ margin: "0 auto 16px" }}
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											d="M12 4v16m8-8H4"
										/>
									</svg>
									<Heading size="4" style={{ color: "rgba(0, 0, 0, 0.6)" }}>
										Start building your page
									</Heading>
									<Text size="2" style={{ color: "rgba(0, 0, 0, 0.4)" }}>
										Drag components from the left panel to create your site
									</Text>
								</div>
							</div>
						) : (
							<div>
								{/* Temporarily disable drag and drop to test click events */}
								{currentPage.components.map((component) => {
									return (
										<div key={component.id} style={{ position: "relative" }}>
											<ComponentRenderer
												component={component}
												isSelected={selectedComponentId === component.id}
												onSelect={() => setSelectedComponentId(component.id)}
												onEdit={(event) => handleEditComponent(component, event)}
											/>
										</div>
									);
								})}
							</div>
						)}
						</div>
					</div>
				</div>

			{/* Inline Tooltip */}
			{tooltipState && (
				<InlineTooltip
					component={tooltipState.component}
					onUpdate={(properties) => handleUpdateComponent(tooltipState.component.id, properties)}
					onDelete={() => handleDeleteComponent(tooltipState.component.id)}
					position={tooltipState.position}
					onClose={() => setTooltipState(null)}
				/>
			)}
		</div>
	);
}
