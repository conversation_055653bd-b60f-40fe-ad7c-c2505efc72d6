import React, { useState } from 'react';

interface Element {
  id: string;
  type: string;
  name: string;
  properties: Record<string, any>;
  children?: Element[];
  visible?: boolean;
  text?: string;
  content?: string;
}

interface LayerPanelProps {
  elements: Element[];
  selectedElementId: string | null;
  onSelectElement: (id: string) => void;
  onUpdateElement: (id: string, updates: Partial<Element>) => void;
}

interface LayerItemProps {
  element: Element;
  level: number;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onUpdate: (id: string, updates: Partial<Element>) => void;
}

const LayerItem: React.FC<LayerItemProps> = ({ element, level, isSelected, onSelect, onUpdate }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(element.text || element.content || '');

  const handleTextEdit = () => {
    setIsEditing(true);
    setEditValue(element.text || element.content || '');
  };

  const handleSaveEdit = () => {
    if (editValue !== (element.text || element.content)) {
      onUpdate(element.id, { text: editValue, content: editValue });
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditValue(element.text || element.content || '');
    }
  };

  const getElementIcon = (type: string) => {
    switch (type) {
      case 'h1': return '📝';
      case 'h2': return '📝';
      case 'h3': return '📝';
      case 'p': return '📄';
      case 'button': return '🔘';
      case 'hero': return '🎯';
      case 'pricing': return '💰';
      case 'features': return '✨';
      case 'testimonials': return '💬';
      case 'form': return '📋';
      case 'footer': return '🔗';
      default: return '📦';
    }
  };

  const getElementName = (element: Element) => {
    if (element.text) return element.text.substring(0, 30) + (element.text.length > 30 ? '...' : '');
    if (element.content) return element.content.substring(0, 30) + (element.content.length > 30 ? '...' : '');
    return `${element.type} ${element.id.slice(-4)}`;
  };

  const hasChildren = element.children && element.children.length > 0;

  return (
    <div className="select-none">
      <div
        className={`
          flex items-center gap-2 p-2 rounded cursor-pointer transition-colors
          ${isSelected ? 'bg-blue-100 border border-blue-300' : 'hover:bg-gray-100'}
          ${level > 0 ? 'ml-4' : ''}
        `}
        onClick={() => onSelect(element.id)}
      >
        {hasChildren && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
            className="text-gray-500 hover:text-gray-700"
          >
            {isExpanded ? '▼' : '▶'}
          </button>
        )}
        
        <span className="text-lg">{getElementIcon(element.type)}</span>
        
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <input
              type="text"
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onBlur={handleSaveEdit}
              onKeyDown={handleKeyDown}
              className="w-full px-1 py-0.5 text-sm border border-gray-300 rounded"
              autoFocus
            />
          ) : (
            <div
              className="text-sm truncate cursor-pointer"
              onDoubleClick={handleTextEdit}
              title={element.text || element.content || ''}
            >
              {getElementName(element)}
            </div>
          )}
        </div>

        {/* Quick edit buttons */}
        <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {(element.text || element.content) && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleTextEdit();
              }}
              className="p-1 text-gray-500 hover:text-blue-600"
              title="Edit text"
            >
              ✏️
            </button>
          )}
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onUpdate(element.id, { visible: !element.visible });
            }}
            className="p-1 text-gray-500 hover:text-gray-700"
            title={element.visible ? "Hide" : "Show"}
          >
            {element.visible ? '👁️' : '🙈'}
          </button>
        </div>
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="ml-2">
          {element.children!.map((child) => (
            <LayerItem
              key={child.id}
              element={child}
              level={level + 1}
              isSelected={isSelected}
              onSelect={onSelect}
              onUpdate={onUpdate}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const LayerPanel: React.FC<LayerPanelProps> = ({
  elements,
  selectedElementId,
  onSelectElement,
  onUpdateElement,
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredElements = elements.filter(element => 
    element.text?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    element.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    element.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="w-80 bg-white border-r border-gray-200 h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Layers</h3>
        
        <div className="relative">
          <input
            type="text"
            placeholder="Search layers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 pl-8 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-gray-400 text-sm">🔍</span>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-2">
          {filteredElements.map((element) => (
            <LayerItem
              key={element.id}
              element={element}
              level={0}
              isSelected={selectedElementId === element.id}
              onSelect={onSelectElement}
              onUpdate={onUpdateElement}
            />
          ))}
        </div>
      </div>

      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500">
          <p>💡 Double-click text to edit</p>
          <p>👁️ Click eye to show/hide</p>
          <p>🔍 Search to find elements</p>
        </div>
      </div>
    </div>
  );
};
