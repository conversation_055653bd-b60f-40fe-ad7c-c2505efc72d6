"use client";

import type { EditorComponent } from "@/lib/types/editor";
import { <PERSON><PERSON>, <PERSON><PERSON>, Heading, Text } from "frosted-ui";
import React from "react";
import Image from "next/image";

interface ComponentRendererProps {
	component: EditorComponent;
	isSelected?: boolean;
	onSelect?: () => void;
	onEdit?: (event: React.MouseEvent) => void;
}

export function ComponentRenderer({
	component,
	isSelected = false,
	onSelect,
	onEdit,
}: ComponentRendererProps) {
	const [isHovered, setIsHovered] = React.useState(false);

	const baseStyle: React.CSSProperties = {
		position: "relative",
		cursor: "pointer",
		// Blue border with glow when selected
		outline: isSelected ? "2px solid #3b82f6" : "none",
		outlineOffset: isSelected ? "2px" : "0",
		boxShadow: isSelected ? "0 0 0 4px rgba(59, 130, 246, 0.1), 0 0 20px rgba(59, 130, 246, 0.3)" : "none",
		transition: "all 0.2s ease",
	};

	const renderContent = () => {
		switch (component.type) {
			case "hero_section":
				return (
					<div
							style={{
								position: "relative",
								height: component.properties.height || "700px",
								background: component.properties.backgroundColor || "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
								backgroundImage: component.properties.backgroundImage
									? `url(${component.properties.backgroundImage})`
									: undefined,
								backgroundSize: "cover",
								backgroundPosition: "center",
								// No border radius or box shadow - natural website appearance
								overflow: "hidden",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
							}}
					>
						{/* Overlay */}
						{component.properties.overlay && (
							<div
											style={{
												position: "absolute",
												inset: 0,
												background: `linear-gradient(135deg, rgba(0, 0, 0, ${component.properties.overlayOpacity || 0.4}) 0%, rgba(0, 0, 0, ${(component.properties.overlayOpacity || 0.4) * 0.7}) 100%)`,
											}}
							/>
						)}
						
						{/* Content */}
						<div
											style={{
												position: "relative",
												zIndex: 1,
												textAlign: "center",
												padding: "60px 40px",
												maxWidth: "1000px",
												color: component.properties.textColor || "#ffffff",
											}}
						>
												{/* Tagline */}
												{component.properties.tagline && (
													<div
														style={{
															display: "inline-flex",
															alignItems: "center",
															padding: "8px 20px",
									background: "rgba(255, 255, 255, 0.1)",
									borderRadius: "50px",
									fontSize: "14px",
									fontWeight: "500",
									marginBottom: "32px",
									border: "1px solid rgba(255, 255, 255, 0.2)",
														}}
													>
														{component.properties.tagline}
													</div>
												)}
												
												{/* Title */}
												<h1
													style={{
														fontSize: "clamp(42px, 5vw, 64px)",
														fontWeight: "800",
														lineHeight: "1.1",
														marginBottom: "28px",
														letterSpacing: "-0.03em",
								color: component.properties.textColor || "#ffffff",
													}}
												>
													{component.properties.title || "Build, Launch & Scale Your SaaS in Days, Not Months"}
												</h1>
							
												{/* Subtitle */}
												<p
													style={{
														fontSize: "clamp(18px, 2.5vw, 22px)",
														lineHeight: "1.7",
														opacity: 0.95,
														marginBottom: "48px",
														fontWeight: "400",
														maxWidth: "800px",
														margin: "0 auto 48px auto",
													}}
												>
													{component.properties.subtitle || "The complete platform that helps you create, market, and grow your software business with AI-powered tools, automated funnels, and proven conversion strategies."}
												</p>
							
												{/* CTAs */}
												<div
													style={{
														display: "flex",
														gap: "20px",
														justifyContent: "center",
														flexWrap: "wrap",
														alignItems: "center",
													}}
												>
													<a
														href={component.properties.ctaUrl || "#"}
														style={{
															display: "inline-flex",
															alignItems: "center",
															justifyContent: "center",
															padding: "18px 48px",
															background: "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
															color: "#1e293b",
															fontSize: "17px",
															fontWeight: "700",
															borderRadius: "16px",
															textDecoration: "none",
															boxShadow: "0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)",
															transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
															transform: "translateY(0)",
															border: "none",
															cursor: "pointer",
														}}
														onMouseEnter={(e) => {
															e.currentTarget.style.transform = "translateY(-2px)";
															e.currentTarget.style.boxShadow = "0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2)";
														}}
														onMouseLeave={(e) => {
															e.currentTarget.style.transform = "translateY(0)";
															e.currentTarget.style.boxShadow = "0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)";
														}}
													>
														{component.properties.ctaText || "Start Building Free"}
													</a>
													{component.properties.secondaryCtaText && (
														<a
															href={component.properties.secondaryCtaUrl || "#"}
															style={{
																display: "inline-flex",
																alignItems: "center",
																justifyContent: "center",
																padding: "18px 48px",
									background: "rgba(255, 255, 255, 0.1)",
									color: "white",
									fontSize: "17px",
									fontWeight: "600",
									borderRadius: "16px",
									border: "2px solid rgba(255, 255, 255, 0.3)",
																textDecoration: "none",
																transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
																transform: "translateY(0)",
																cursor: "pointer",
															}}
															onMouseEnter={(e) => {
																e.currentTarget.style.transform = "translateY(-2px)";
																e.currentTarget.style.background = "rgba(255, 255, 255, 0.2)";
																e.currentTarget.style.borderColor = "rgba(255, 255, 255, 0.5)";
															}}
															onMouseLeave={(e) => {
																e.currentTarget.style.transform = "translateY(0)";
																e.currentTarget.style.background = "rgba(255, 255, 255, 0.1)";
																e.currentTarget.style.borderColor = "rgba(255, 255, 255, 0.3)";
															}}
														>
															{component.properties.secondaryCtaText}
														</a>
													)}
							</div>
						</div>
					</div>
				);

			case "features_section":
				return (
					<div
						style={{
							padding: "80px 40px",
							background: component.properties.backgroundColor || "#ffffff",
							// No border radius - natural website section
						}}
					>
						{/* Section Header */}
						<div style={{ textAlign: "center", marginBottom: "60px" }}>
							<h2
								style={{
									fontSize: "48px",
									fontWeight: "800",
									color: component.properties.textColor || "#111827",
									marginBottom: "16px",
									letterSpacing: "-0.02em",
								}}
							>
								{component.properties.heading || "Powerful Features"}
							</h2>
							{component.properties.subheading && (
								<p
									style={{
										fontSize: "20px",
										color: "#6b7280",
										maxWidth: "700px",
										margin: "0 auto",
									}}
								>
									{component.properties.subheading}
								</p>
							)}
						</div>

						{/* Features Grid */}
						<div
							style={{
								display: "grid",
								gridTemplateColumns: `repeat(${component.properties.columns || 3}, 1fr)`,
								gap: "40px",
								maxWidth: "1200px",
								margin: "0 auto",
							}}
						>
							{component.properties.features.map((feature: { title: string; description: string; icon?: string }, idx: number) => (
								<div
									key={idx}
									style={{
										padding: "32px",
										background: "#f9fafb",
										borderRadius: "16px",
										border: "1px solid #e5e7eb",
										transition: "all 0.3s ease",
									}}
								>
									{feature.icon && (
										<div
											style={{
												fontSize: "48px",
												marginBottom: "20px",
											}}
										>
											{feature.icon}
										</div>
									)}
									<h3
										style={{
											fontSize: "24px",
											fontWeight: "700",
											color: "#111827",
											marginBottom: "12px",
										}}
									>
										{feature.title}
									</h3>
									<p
										style={{
											fontSize: "16px",
											color: "#6b7280",
											lineHeight: "1.6",
										}}
									>
										{feature.description}
									</p>
								</div>
							))}
						</div>
					</div>
				);

			case "pricing_section":
				return (
					<div
						style={{
							padding: "80px 40px",
							background: component.properties.backgroundColor || "#f9fafb",
							// No border radius - natural website section
						}}
					>
						{/* Section Header */}
						<div style={{ textAlign: "center", marginBottom: "60px" }}>
							<h2
								style={{
									fontSize: "48px",
									fontWeight: "800",
									color: component.properties.textColor || "#111827",
									marginBottom: "16px",
									letterSpacing: "-0.02em",
								}}
							>
								{component.properties.heading || "Simple, Transparent Pricing"}
							</h2>
							{component.properties.subheading && (
								<p
									style={{
										fontSize: "20px",
										color: "#6b7280",
										maxWidth: "700px",
										margin: "0 auto",
									}}
								>
									{component.properties.subheading}
								</p>
							)}
						</div>

						{/* Pricing Cards */}
						<div
							style={{
								display: "grid",
								gridTemplateColumns: `repeat(${component.properties.plans.length}, 1fr)`,
								gap: "32px",
								maxWidth: "1200px",
								margin: "0 auto",
							}}
						>
							{component.properties.plans.map((plan: {
								name: string;
								price: string;
								period?: string;
								description?: string;
								features: string[];
								highlighted?: boolean;
								ctaText?: string;
								ctaUrl?: string;
							}, idx: number) => (
								<div
									key={idx}
									style={{
										padding: "40px",
										background: "#ffffff",
										borderRadius: "20px",
										border: plan.highlighted ? "3px solid #6366f1" : "1px solid #e5e7eb",
										boxShadow: plan.highlighted 
											? "0 20px 60px rgba(99, 102, 241, 0.2)"
											: "0 4px 20px rgba(0, 0, 0, 0.08)",
										position: "relative",
										transform: plan.highlighted ? "scale(1.05)" : "none",
										transition: "all 0.3s ease",
									}}
								>
									{plan.highlighted && (
										<div
											style={{
												position: "absolute",
												top: "-12px",
												left: "50%",
												transform: "translateX(-50%)",
												background: "linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)",
												color: "white",
												padding: "6px 20px",
												borderRadius: "20px",
												fontSize: "12px",
												fontWeight: "700",
												textTransform: "uppercase",
											}}
										>
											Popular
										</div>
									)}
									<h3
										style={{
											fontSize: "24px",
											fontWeight: "700",
											color: "#111827",
											marginBottom: "8px",
										}}
									>
										{plan.name}
									</h3>
									{plan.description && (
										<p
											style={{
												fontSize: "14px",
												color: "#6b7280",
												marginBottom: "24px",
											}}
										>
											{plan.description}
										</p>
									)}
									<div style={{ marginBottom: "32px" }}>
										<span
											style={{
												fontSize: "56px",
												fontWeight: "800",
												color: "#111827",
												letterSpacing: "-0.02em",
											}}
										>
											{plan.price}
										</span>
										{plan.period && (
											<span
												style={{
													fontSize: "16px",
													color: "#6b7280",
													fontWeight: "500",
												}}
											>
												{plan.period}
											</span>
										)}
									</div>
									<ul
										style={{
											listStyle: "none",
											padding: 0,
											margin: "0 0 32px 0",
										}}
									>
										{plan.features.map((feature, featureIdx) => (
											<li
												key={featureIdx}
												style={{
													display: "flex",
													alignItems: "center",
													gap: "12px",
													marginBottom: "12px",
													color: "#374151",
													fontSize: "15px",
												}}
											>
												<div
													style={{
														minWidth: "20px",
														height: "20px",
														borderRadius: "50%",
														background: "#6366f1",
														display: "flex",
														alignItems: "center",
														justifyContent: "center",
													}}
												>
													<svg
														width="12"
														height="12"
														viewBox="0 0 24 24"
														fill="none"
														stroke="white"
														strokeWidth="3"
													>
														<path
															strokeLinecap="round"
															strokeLinejoin="round"
															d="M5 13l4 4L19 7"
														/>
													</svg>
												</div>
												{feature}
											</li>
										))}
									</ul>
									<a
										href={plan.ctaUrl || "#"}
										style={{
											display: "block",
											width: "100%",
											padding: "16px",
											background: plan.highlighted ? "#6366f1" : "transparent",
											color: plan.highlighted ? "white" : "#6366f1",
											fontSize: "16px",
											fontWeight: "600",
											borderRadius: "12px",
											textAlign: "center",
											textDecoration: "none",
											border: plan.highlighted ? "none" : "2px solid #6366f1",
											transition: "all 0.2s ease",
										}}
									>
										{plan.ctaText || "Get Started"}
									</a>
								</div>
							))}
						</div>
					</div>
				);

			case "testimonials_section":
				return (
					<div
						style={{
							padding: "80px 40px",
							background: component.properties.backgroundColor || "#ffffff",
							borderRadius: "16px",
						}}
					>
						{/* Section Header */}
						<div style={{ textAlign: "center", marginBottom: "60px" }}>
							<h2
								style={{
									fontSize: "48px",
									fontWeight: "800",
									color: component.properties.textColor || "#111827",
									marginBottom: "16px",
									letterSpacing: "-0.02em",
								}}
							>
								{component.properties.heading || "What Our Customers Say"}
							</h2>
							{component.properties.subheading && (
								<p
									style={{
										fontSize: "20px",
										color: "#6b7280",
										maxWidth: "700px",
										margin: "0 auto",
									}}
								>
									{component.properties.subheading}
								</p>
							)}
						</div>

						{/* Testimonials Grid */}
						<div
							style={{
								display: "grid",
								gridTemplateColumns: `repeat(${component.properties.columns || 3}, 1fr)`,
								gap: "32px",
								maxWidth: "1200px",
								margin: "0 auto",
							}}
						>
							{component.properties.testimonials.map((testimonial: {
								quote: string;
								author: string;
								role?: string;
								avatar?: string;
								rating?: number;
							}, idx: number) => (
								<div
									key={idx}
									style={{
										background: "#ffffff",
										padding: "32px",
										borderRadius: "16px",
										border: "1px solid #e5e7eb",
										boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
									}}
								>
									{/* Rating Stars */}
									{testimonial.rating && (
										<div style={{ display: "flex", gap: "4px", marginBottom: "16px" }}>
											{[...Array(5)].map((_, i) => (
												<svg
													key={i}
													width="20"
													height="20"
													viewBox="0 0 24 24"
													fill={i < (testimonial.rating || 5) ? "#fbbf24" : "#e5e7eb"}
												>
													<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
												</svg>
											))}
										</div>
									)}
									<p style={{ fontSize: "18px", fontStyle: "italic", marginBottom: "24px", lineHeight: "1.6", color: "#374151" }}>
										&ldquo;{testimonial.quote}&rdquo;
									</p>
									<div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
										{testimonial.avatar && (
											<Image
												src={testimonial.avatar}
												alt={testimonial.author}
												width={48}
												height={48}
												style={{
													borderRadius: "50%",
													objectFit: "cover",
												}}
											/>
										)}
										<div>
											<div style={{ fontSize: "16px", fontWeight: "600", color: "#111827" }}>
												{testimonial.author}
											</div>
											{testimonial.role && (
												<div style={{ fontSize: "14px", color: "#6b7280" }}>
													{testimonial.role}
												</div>
											)}
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				);

			case "form_section":
				return (
					<div
						style={{
							padding: "80px 40px",
							background: component.properties.backgroundColor || "#f9fafb",
							// No border radius - natural website section
						}}
					>
						{/* Section Header */}
						<div style={{ textAlign: "center", marginBottom: "48px" }}>
							<h2
								style={{
									fontSize: "48px",
									fontWeight: "800",
									color: component.properties.textColor || "#111827",
									marginBottom: "16px",
									letterSpacing: "-0.02em",
								}}
							>
								{component.properties.heading || "Get In Touch"}
							</h2>
							{component.properties.subheading && (
								<p
									style={{
										fontSize: "20px",
										color: "#6b7280",
										maxWidth: "700px",
										margin: "0 auto",
									}}
								>
									{component.properties.subheading}
								</p>
							)}
						</div>

						{/* Form */}
						<form
							style={{
								maxWidth: "600px",
								margin: "0 auto",
								background: "#ffffff",
								padding: "40px",
								borderRadius: "16px",
								boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
							}}
						>
							<div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
								{component.properties.fields.map((field: {
									type: string;
									label: string;
									placeholder?: string;
									required?: boolean;
								}, idx: number) => (
									<div key={idx} style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
										<label
											style={{
												fontSize: "14px",
												fontWeight: "600",
												color: "#374151",
											}}
										>
											{field.label}
											{field.required && (
												<span style={{ color: "#ef4444" }}> *</span>
											)}
										</label>
										{field.type === "textarea" ? (
											<textarea
												placeholder={field.placeholder}
												required={field.required}
												rows={4}
												style={{
													padding: "12px 16px",
													border: "2px solid #e5e7eb",
													borderRadius: "8px",
													fontSize: "15px",
													background: "#ffffff",
													transition: "border-color 0.2s ease",
													outline: "none",
													fontFamily: "inherit",
													resize: "vertical",
												}}
											/>
										) : (
											<input
												type={field.type}
												placeholder={field.placeholder}
												required={field.required}
												style={{
													padding: "12px 16px",
													border: "2px solid #e5e7eb",
													borderRadius: "8px",
													fontSize: "15px",
													background: "#ffffff",
													transition: "border-color 0.2s ease",
													outline: "none",
												}}
											/>
										)}
									</div>
								))}
								<button
									type="submit"
									style={{
										padding: "16px 32px",
										background: "#6366f1",
										color: "white",
										fontSize: "16px",
										fontWeight: "600",
										borderRadius: "12px",
										border: "none",
										cursor: "pointer",
										transition: "all 0.2s ease",
										boxShadow: "0 4px 14px rgba(99, 102, 241, 0.25)",
									}}
								>
									{component.properties.submitText || "Send Message"}
								</button>
							</div>
						</form>
					</div>
				);

			case "footer_section":
				return (
					<div
						style={{
							padding: "60px 40px 40px",
							background: component.properties.backgroundColor || "#111827",
							borderRadius: "16px",
							color: component.properties.textColor || "#ffffff",
						}}
					>
						<div style={{ maxWidth: "1200px", margin: "0 auto" }}>
							{/* Footer Top */}
							<div
								style={{
									display: "grid",
									gridTemplateColumns: `1fr repeat(${component.properties.columns.length}, 1fr)`,
									gap: "48px",
									marginBottom: "48px",
									paddingBottom: "48px",
									borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
								}}
							>
								{/* Company Info */}
								<div>
									<h3
										style={{
											fontSize: "24px",
											fontWeight: "800",
											marginBottom: "12px",
										}}
									>
										{component.properties.companyName || "Your Company"}
									</h3>
									{component.properties.tagline && (
										<p
											style={{
												fontSize: "14px",
												color: "rgba(255, 255, 255, 0.7)",
												marginBottom: "24px",
											}}
										>
											{component.properties.tagline}
										</p>
									)}
									{/* Social Links */}
									{component.properties.socialLinks && component.properties.socialLinks.length > 0 && (
										<div style={{ display: "flex", gap: "12px", marginTop: "16px" }}>
											{component.properties.socialLinks.map((social: { platform: string; url: string }, idx: number) => (
												<a
													key={idx}
													href={social.url}
													style={{
														width: "40px",
														height: "40px",
														borderRadius: "50%",
														background: "rgba(255, 255, 255, 0.1)",
														display: "flex",
														alignItems: "center",
														justifyContent: "center",
														color: "white",
														textDecoration: "none",
														transition: "all 0.2s ease",
														fontSize: "14px",
														fontWeight: "600",
													}}
												>
													{social.platform.charAt(0).toUpperCase()}
												</a>
											))}
										</div>
									)}
								</div>

								{/* Footer Columns */}
								{component.properties.columns.map((column: {
									title: string;
									links: Array<{ label: string; url: string }>;
								}, idx: number) => (
									<div key={idx}>
										<h4
											style={{
												fontSize: "16px",
												fontWeight: "700",
												marginBottom: "16px",
												color: "white",
											}}
										>
											{column.title}
										</h4>
										<ul
											style={{
												listStyle: "none",
												padding: 0,
												margin: 0,
												display: "flex",
												flexDirection: "column",
												gap: "12px",
											}}
										>
											{column.links.map((link, linkIdx) => (
												<li key={linkIdx}>
													<a
														href={link.url}
														style={{
															fontSize: "14px",
															color: "rgba(255, 255, 255, 0.7)",
															textDecoration: "none",
															transition: "color 0.2s ease",
														}}
													>
														{link.label}
													</a>
												</li>
											))}
										</ul>
									</div>
								))}
							</div>

							{/* Footer Bottom */}
							<div style={{ textAlign: "center" }}>
								<p
									style={{
										fontSize: "14px",
										color: "rgba(255, 255, 255, 0.6)",
										margin: 0,
									}}
								>
									{component.properties.copyright || `© ${new Date().getFullYear()} Your Company. All rights reserved.`}
								</p>
							</div>
						</div>
					</div>
				);

			case "headline":
				return (
					<div
						style={{
							textAlign: component.properties.alignment as "left" | "center" | "right" | undefined,
							marginBottom: "16px",
						}}
					>
						<h2
							style={{
								color: component.properties.color || "#111827",
								fontSize: component.properties.fontSize || "48px",
								fontWeight: component.properties.fontWeight || "bold",
								letterSpacing: component.properties.letterSpacing || "-0.02em",
								lineHeight: component.properties.lineHeight || "1.2",
								margin: 0,
							}}
						>
							{component.properties.text}
						</h2>
					</div>
				);

			case "sub_headline":
				return (
					<div
						style={{
							textAlign: component.properties.alignment as "left" | "center" | "right" | undefined,
							display: "flex",
							justifyContent: component.properties.alignment === "center" 
								? "center" 
								: component.properties.alignment === "right"
									? "flex-end"
									: "flex-start",
						}}
					>
						<h3
							style={{
								color: component.properties.color || "#6b7280",
								fontSize: component.properties.fontSize || "20px",
								fontWeight: component.properties.fontWeight || "500",
								lineHeight: component.properties.lineHeight || "1.5",
								maxWidth: component.properties.maxWidth || "700px",
								margin: 0,
							}}
						>
							{component.properties.text}
						</h3>
					</div>
				);

			case "paragraph":
				return (
					<p
						style={{
							textAlign: component.properties.alignment as "left" | "center" | "right" | undefined,
							color: component.properties.color || "#374151",
							fontSize: component.properties.fontSize || "16px",
							lineHeight: component.properties.lineHeight || "1.75",
							margin: 0,
							maxWidth: "65ch",
						}}
					>
						{component.properties.text}
					</p>
				);

			case "button":
				return (
					<div
						style={{
							display: "flex",
							justifyContent:
								component.properties.alignment === "center"
									? "center"
									: component.properties.alignment === "right"
										? "flex-end"
										: "flex-start",
						}}
					>
						<a
							href={component.properties.url || "#"}
							style={{
								display: "inline-flex",
								alignItems: "center",
								justifyContent: "center",
								padding: component.properties.padding || (
									component.properties.size === "sm" ? "10px 24px" :
									component.properties.size === "lg" ? "14px 32px" :
									"12px 28px"
								),
								background: component.properties.variant === "outline" 
									? "transparent" 
									: component.properties.variant === "ghost"
										? "transparent"
										: component.properties.color || "#6366f1",
								color: component.properties.variant === "outline" || component.properties.variant === "ghost"
									? component.properties.color || "#6366f1"
									: "white",
								fontSize: component.properties.size === "sm" ? "14px" : component.properties.size === "lg" ? "16px" : "15px",
								fontWeight: component.properties.fontWeight || "600",
								borderRadius: component.properties.borderRadius || "8px",
								textDecoration: "none",
								border: component.properties.variant === "outline"
									? `2px solid ${component.properties.color || "#6366f1"}`
									: "none",
								// No box shadow or transitions - natural website button
								width: component.properties.fullWidth ? "100%" : "auto",
							}}
						>
							{component.properties.text}
						</a>
					</div>
				);

			case "image":
				return (
					<div
						style={{
							display: "flex",
							justifyContent:
								component.properties.alignment === "center"
									? "center"
									: component.properties.alignment === "right"
										? "flex-end"
										: "flex-start",
						}}
					>
						<Image
							src={component.properties.src}
							alt={component.properties.alt}
							width={parseInt(component.properties.width || "300") || 300}
							height={parseInt(component.properties.height || "200") || 200}
							style={{
								objectFit: component.properties.objectFit || "cover",
								borderRadius: "12px",
								boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
							}}
						/>
					</div>
				);

			case "divider":
				return (
					<hr
						style={{
							border: "none",
							borderTop: `${component.properties.thickness || "2px"} ${component.properties.style || "solid"} ${component.properties.color || "#e5e7eb"}`,
							margin: `${component.properties.spacing || "32px"} 0`,
							opacity: 0.8,
						}}
					/>
				);

			case "spacer":
				return (
					<div
						style={{
							height: component.properties.height,
							background: "repeating-linear-gradient(90deg, var(--gray-4) 0px, var(--gray-4) 8px, transparent 8px, transparent 16px)",
							borderRadius: "4px",
						}}
					/>
				);

			case "input_field":
				return (
					<div className="flex flex-col gap-2">
						<label style={{ fontSize: "14px", fontWeight: "600", color: "#374151" }}>
							{component.properties.label}
							{component.properties.required && (
								<span style={{ color: "#ef4444" }}> *</span>
							)}
						</label>
						<input
							type={component.properties.fieldType}
							placeholder={component.properties.placeholder}
							style={{
								padding: "12px 16px",
								border: "2px solid #e5e7eb",
								borderRadius: "8px",
								fontSize: "15px",
								background: "#ffffff",
								transition: "border-color 0.2s ease",
								outline: "none",
							}}
						/>
					</div>
				);

			case "text_area":
				return (
					<div className="flex flex-col gap-2">
						<label style={{ fontSize: "14px", fontWeight: "600", color: "#374151" }}>
							{component.properties.label}
							{component.properties.required && (
								<span style={{ color: "#ef4444" }}> *</span>
							)}
						</label>
						<textarea
							rows={component.properties.rows}
							placeholder={component.properties.placeholder}
							style={{
								padding: "12px 16px",
								border: "2px solid #e5e7eb",
								borderRadius: "8px",
								fontSize: "15px",
								resize: "vertical",
								background: "#ffffff",
								transition: "border-color 0.2s ease",
								outline: "none",
								fontFamily: "inherit",
							}}
						/>
					</div>
				);

			case "bullet_list":
				return (
					<ul
						style={{
							listStyle: "none",
							padding: 0,
							margin: 0,
						}}
					>
						{component.properties.items.map((item: string, i: number) => (
							<li
								key={i}
								style={{
									display: "flex",
									alignItems: "flex-start",
									gap: "12px",
									marginBottom: "12px",
									color: "#374151",
								}}
							>
								{component.properties.style === "check" ? (
									<div
										style={{
											minWidth: "24px",
											height: "24px",
											borderRadius: "50%",
											background: component.properties.color || "#6366f1",
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
										}}
									>
										<svg
											width="14"
											height="14"
											viewBox="0 0 24 24"
											fill="none"
											stroke="white"
											strokeWidth="3"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												d="M5 13l4 4L19 7"
											/>
										</svg>
									</div>
								) : (
									<span style={{ color: component.properties.color || "#6366f1", fontSize: "20px", lineHeight: "1" }}>•</span>
								)}
								<span style={{ fontSize: "16px", lineHeight: "1.6", flex: 1 }}>{item}</span>
							</li>
						))}
					</ul>
				);

			case "testimonial":
				return (
					<div
						style={{
							background: "#ffffff",
							padding: "32px",
							borderRadius: "16px",
							border: "1px solid #e5e7eb",
							boxShadow: "0 4px 20px rgba(0, 0, 0, 0.08)",
						}}
					>
						{/* Rating Stars */}
						{component.properties.rating && (
							<div style={{ display: "flex", gap: "4px", marginBottom: "16px" }}>
								{[...Array(5)].map((_, i) => (
									<svg
										key={i}
										width="20"
										height="20"
										viewBox="0 0 24 24"
										fill={i < (component.properties.rating || 5) ? "#fbbf24" : "#e5e7eb"}
									>
										<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
									</svg>
								))}
							</div>
						)}
						<p style={{ fontSize: "18px", fontStyle: "italic", marginBottom: "24px", lineHeight: "1.6", color: "#374151" }}>
							&ldquo;{component.properties.quote}&rdquo;
						</p>
						<div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
							{component.properties.avatar && (
								<Image
									src={component.properties.avatar}
									alt={component.properties.author}
									width={48}
									height={48}
									style={{
										borderRadius: "50%",
										objectFit: "cover",
									}}
								/>
							)}
							<div>
								<div style={{ fontSize: "16px", fontWeight: "600", color: "#111827" }}>
									{component.properties.author}
								</div>
								{component.properties.role && (
									<div style={{ fontSize: "14px", color: "#6b7280" }}>
										{component.properties.role}
									</div>
								)}
							</div>
						</div>
					</div>
				);

			case "countdown_timer":
				return (
					<div
						style={{
							display: "flex",
							gap: "16px",
							justifyContent:
								component.properties.alignment === "center"
									? "center"
									: component.properties.alignment === "right"
										? "flex-end"
										: "flex-start",
							fontSize: component.properties.size,
						}}
					>
						{["00", "00", "00", "00"].map((unit, i) => (
							<div
								key={i}
								style={{
									background: "var(--gray-3)",
									padding: "12px 16px",
									borderRadius: "8px",
									textAlign: "center",
								}}
							>
								<div style={{ fontSize: "24px", fontWeight: "bold" }}>
									{unit}
								</div>
								<Text size="1" color="gray">
									{["D", "H", "M", "S"][i]}
								</Text>
							</div>
						))}
					</div>
				);

			case "faq_accordion":
				return (
					<div className="flex flex-col gap-3">
						{component.properties.items.map((item: { question: string; answer: string }, i: number) => (
							<details
								key={i}
								style={{
									background: "#ffffff",
									padding: "20px 24px",
									borderRadius: "12px",
									border: "2px solid #e5e7eb",
									boxShadow: "0 2px 8px rgba(0, 0, 0, 0.04)",
									transition: "all 0.2s ease",
								}}
							>
								<summary
									style={{
										fontWeight: "600",
										cursor: "pointer",
										fontSize: "16px",
										color: "#111827",
										listStyle: "none",
										display: "flex",
										alignItems: "center",
										justifyContent: "space-between",
									}}
								>
									<span>{item.question}</span>
									<span style={{ marginLeft: "12px", fontSize: "20px", color: "#6366f1" }}>+</span>
								</summary>
								<div style={{ marginTop: "16px", fontSize: "15px", color: "#6b7280", lineHeight: "1.6" }}>
									{item.answer}
								</div>
							</details>
						))}
					</div>
				);

			case "progress_bar":
				return (
					<div>
						<div
							style={{
								width: "100%",
								height: component.properties.height,
								background: "var(--gray-4)",
								borderRadius: "999px",
								overflow: "hidden",
							}}
						>
							<div
								style={{
									width: `${(component.properties.value / component.properties.max) * 100}%`,
									height: "100%",
									background: component.properties.color,
									transition: "width 0.3s",
								}}
							/>
						</div>
						{component.properties.showLabel && (
							<Text size="1" color="gray" style={{ marginTop: "4px" }}>
								{component.properties.value} / {component.properties.max}
							</Text>
						)}
					</div>
				);

			case "video":
				return (
					<video
						controls={component.properties.controls}
						autoPlay={component.properties.autoplay}
						loop={component.properties.loop}
						poster={component.properties.poster}
						style={{
							width: component.properties.width,
							height: component.properties.height,
							borderRadius: "8px",
						}}
					>
						<source src={component.properties.src} />
						Your browser does not support the video tag.
					</video>
				);

			case "table":
				return (
					<table
						style={{
							width: "100%",
							borderCollapse: "collapse",
							fontSize: "14px",
						}}
					>
						<thead>
							<tr>
								{component.properties.headers.map((header: string, i: number) => (
									<th
										key={i}
										style={{
											padding: "12px",
											textAlign: "left",
											background: "var(--gray-3)",
											border: component.properties.bordered
												? "1px solid var(--gray-6)"
												: "none",
											fontWeight: "600",
										}}
									>
										{header}
									</th>
								))}
							</tr>
						</thead>
						<tbody>
							{component.properties.rows.map((row: string[], i: number) => (
								<tr
									key={i}
									style={{
										background:
											component.properties.striped && i % 2 === 1
												? "var(--gray-2)"
												: "transparent",
									}}
								>
									{row.map((cell, j) => (
										<td
											key={j}
											style={{
												padding: "12px",
												border: component.properties.bordered
													? "1px solid var(--gray-6)"
													: "none",
											}}
										>
											{cell}
										</td>
									))}
								</tr>
							))}
						</tbody>
					</table>
				);

			case "section":
				return (
					<div
						style={{
							minHeight: "120px",
							background: component.properties.background || "#ffffff",
							padding: component.properties.padding || "80px 40px",
							maxWidth: component.properties.maxWidth || "1200px",
							margin: "0 auto",
							borderRadius: "16px",
							border: "2px solid #e5e7eb",
							position: "relative",
							display: "flex",
							flexDirection: "column",
							gap: "16px",
							boxShadow: "0 4px 16px rgba(0, 0, 0, 0.04)",
						}}
					>
						{/* Section Header */}
						<div
							style={{
								position: "absolute",
								top: "12px",
								left: "12px",
								background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
								color: "white",
								padding: "6px 14px",
								borderRadius: "8px",
								fontSize: "11px",
								fontWeight: "700",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
								boxShadow: "0 2px 8px rgba(102, 126, 234, 0.3)",
							}}
						>
							📐 Section Container
						</div>
						
						{/* Content Area */}
						<div
							style={{
								flex: 1,
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								minHeight: "80px",
								background: "repeating-linear-gradient(45deg, #f9fafb, #f9fafb 10px, #f3f4f6 10px, #f3f4f6 20px)",
								borderRadius: "10px",
								border: "2px dashed #cbd5e1",
								marginTop: "40px",
							}}
						>
							{("children" in component && Array.isArray(component.children) && component.children.length > 0) ? (
								<div style={{ textAlign: "center", padding: "16px" }}>
									<div style={{ fontSize: "14px", fontWeight: "600", color: "#475569", marginBottom: "4px" }}>
										{component.children.length} Element{component.children.length !== 1 ? "s" : ""} Inside
									</div>
									<div style={{ fontSize: "12px", color: "#94a3b8" }}>
										Drag elements here to add them to this section
									</div>
								</div>
							) : (
								<div style={{ textAlign: "center", padding: "24px" }}>
									<div style={{ fontSize: "48px", marginBottom: "8px", opacity: 0.3 }}>📦</div>
									<div style={{ fontSize: "14px", fontWeight: "600", color: "#64748b", marginBottom: "4px" }}>
										Empty Section
									</div>
									<div style={{ fontSize: "12px", color: "#94a3b8" }}>
										Drop components here
									</div>
								</div>
							)}
						</div>
					</div>
				);

			case "row":
				return (
					<div
						style={{
							minHeight: "100px",
							background: "linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)",
							padding: "24px",
							borderRadius: "16px",
							border: "2px solid #e2e8f0",
							position: "relative",
							boxShadow: "0 4px 16px rgba(0, 0, 0, 0.04)",
						}}
					>
						{/* Row Header */}
						<div
							style={{
								position: "absolute",
								top: "12px",
								left: "12px",
								background: "linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)",
								color: "white",
								padding: "6px 14px",
								borderRadius: "8px",
								fontSize: "11px",
								fontWeight: "700",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
								boxShadow: "0 2px 8px rgba(59, 130, 246, 0.3)",
							}}
						>
							▭ Row - {component.properties.columns} Column{component.properties.columns !== 1 ? "s" : ""}
						</div>
						
						{/* Columns Grid */}
						<div
							style={{
								display: "grid",
								gridTemplateColumns: `repeat(${component.properties.columns || 2}, 1fr)`,
								gap: component.properties.gap || "20px",
								marginTop: "40px",
							}}
						>
							{Array.from({ length: component.properties.columns || 2 }).map((_, idx) => (
								<div
									key={idx}
									style={{
										minHeight: "60px",
										background: "#ffffff",
										border: "2px dashed #cbd5e1",
										borderRadius: "10px",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										padding: "16px",
									}}
								>
									<div style={{ textAlign: "center" }}>
										<div style={{ fontSize: "12px", fontWeight: "600", color: "#64748b" }}>
											Column {idx + 1}
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				);

			case "column":
				return (
					<div
						style={{
							minHeight: "80px",
							background: "#ffffff",
							padding: component.properties.padding || "16px",
							width: component.properties.width || "100%",
							borderRadius: "12px",
							border: "2px solid #e0e7ff",
							position: "relative",
							boxShadow: "0 2px 12px rgba(99, 102, 241, 0.08)",
						}}
					>
						{/* Column Header */}
						<div
							style={{
								position: "absolute",
								top: "8px",
								left: "8px",
								background: "linear-gradient(135deg, #818cf8 0%, #6366f1 100%)",
								color: "white",
								padding: "4px 10px",
								borderRadius: "6px",
								fontSize: "10px",
								fontWeight: "700",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
								boxShadow: "0 2px 6px rgba(99, 102, 241, 0.25)",
							}}
						>
							▯ Column
						</div>
						
						{/* Content Area */}
						<div
							style={{
								marginTop: "32px",
								minHeight: "40px",
								background: "repeating-linear-gradient(45deg, #f8faff, #f8faff 8px, #eff6ff 8px, #eff6ff 16px)",
								borderRadius: "8px",
								border: "2px dashed #c7d2fe",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								padding: "12px",
							}}
						>
							{("children" in component && Array.isArray(component.children) && component.children.length > 0) ? (
								<div style={{ textAlign: "center" }}>
									<div style={{ fontSize: "12px", fontWeight: "600", color: "#6366f1" }}>
										{component.children.length} Element{component.children.length !== 1 ? "s" : ""}
									</div>
								</div>
							) : (
								<div style={{ textAlign: "center" }}>
									<div style={{ fontSize: "32px", marginBottom: "4px", opacity: 0.3 }}>📄</div>
									<div style={{ fontSize: "11px", color: "#a5b4fc" }}>
										Drop here
									</div>
								</div>
							)}
						</div>
					</div>
				);

			case "flex_container":
				return (
					<div
						style={{
							minHeight: "100px",
							background: "linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)",
							padding: "24px",
							borderRadius: "16px",
							border: "2px solid #fbbf24",
							position: "relative",
							boxShadow: "0 4px 16px rgba(251, 191, 36, 0.15)",
						}}
					>
						{/* Flex Container Header */}
						<div
							style={{
								position: "absolute",
								top: "12px",
								left: "12px",
								background: "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",
								color: "white",
								padding: "6px 14px",
								borderRadius: "8px",
								fontSize: "11px",
								fontWeight: "700",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
								boxShadow: "0 2px 8px rgba(245, 158, 11, 0.3)",
							}}
						>
							⬚ Flex - {component.properties.direction || "row"}
						</div>
						
						{/* Flex Content */}
						<div
							style={{
								display: "flex",
								flexDirection: component.properties.direction || "row",
								justifyContent: component.properties.justifyContent || "start",
								alignItems: component.properties.alignItems || "start",
								gap: component.properties.gap || "16px",
								marginTop: "40px",
								minHeight: "60px",
								background: "#fffbeb",
								borderRadius: "10px",
								border: "2px dashed #fbbf24",
								padding: "16px",
							}}
						>
							{("children" in component && Array.isArray(component.children) && component.children.length > 0) ? (
								<div style={{ textAlign: "center", width: "100%", padding: "8px" }}>
									<div style={{ fontSize: "14px", fontWeight: "600", color: "#92400e" }}>
										{component.children.length} Flex Item{component.children.length !== 1 ? "s" : ""}
									</div>
									<div style={{ fontSize: "11px", color: "#b45309", marginTop: "4px" }}>
										Direction: {component.properties.direction || "row"} | Justify: {component.properties.justifyContent || "start"}
									</div>
								</div>
							) : (
								<div style={{ textAlign: "center", width: "100%", padding: "16px" }}>
									<div style={{ fontSize: "40px", marginBottom: "8px", opacity: 0.3 }}>⚡</div>
									<div style={{ fontSize: "14px", fontWeight: "600", color: "#92400e", marginBottom: "4px" }}>
										Empty Flex Container
									</div>
									<div style={{ fontSize: "12px", color: "#b45309" }}>
										Drop elements to arrange flexibly
									</div>
								</div>
							)}
						</div>
					</div>
				);

			case "universal_block":
				return (
					<div
						style={{
							minHeight: "80px",
							background: "linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)",
							padding: "20px",
							borderRadius: "14px",
							border: "2px solid #9ca3af",
							position: "relative",
							boxShadow: "0 4px 14px rgba(0, 0, 0, 0.06)",
						}}
					>
						{/* Universal Block Header */}
						<div
							style={{
								position: "absolute",
								top: "10px",
								left: "10px",
								background: "linear-gradient(135deg, #6b7280 0%, #4b5563 100%)",
								color: "white",
								padding: "6px 14px",
								borderRadius: "8px",
								fontSize: "11px",
								fontWeight: "700",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
								boxShadow: "0 2px 8px rgba(107, 114, 128, 0.3)",
							}}
						>
							▢ Universal Block
						</div>
						
						{/* Content */}
						<div
							style={{
								marginTop: "36px",
								minHeight: "60px",
								background: "#ffffff",
								borderRadius: "10px",
								border: "2px dashed #9ca3af",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								padding: "16px",
							}}
						>
							{("children" in component && Array.isArray(component.children) && component.children.length > 0) ? (
								<div style={{ textAlign: "center" }}>
									<div style={{ fontSize: "14px", fontWeight: "600", color: "#4b5563" }}>
										{component.children.length} Element{component.children.length !== 1 ? "s" : ""}
									</div>
									<div style={{ fontSize: "11px", color: "#6b7280", marginTop: "4px" }}>
										{component.properties.className && `Class: ${component.properties.className}`}
									</div>
								</div>
							) : (
								<div style={{ textAlign: "center" }}>
									<div style={{ fontSize: "36px", marginBottom: "6px", opacity: 0.3 }}>🔲</div>
									<div style={{ fontSize: "14px", fontWeight: "600", color: "#4b5563", marginBottom: "4px" }}>
										Custom Block
									</div>
									<div style={{ fontSize: "11px", color: "#6b7280" }}>
										Add custom elements with classes
									</div>
								</div>
							)}
						</div>
					</div>
				);

			case "popup":
				return (
					<div
						style={{
							minHeight: "140px",
							background: "linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%)",
							padding: "24px",
							borderRadius: "18px",
							border: "3px solid #8b5cf6",
							position: "relative",
							boxShadow: "0 8px 24px rgba(139, 92, 246, 0.2)",
						}}
					>
						{/* Popup Header */}
						<div
							style={{
								position: "absolute",
								top: "12px",
								left: "12px",
								background: "linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",
								color: "white",
								padding: "6px 14px",
								borderRadius: "8px",
								fontSize: "11px",
								fontWeight: "700",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
								boxShadow: "0 2px 8px rgba(139, 92, 246, 0.4)",
							}}
						>
							🗖 Popup Modal
						</div>
						
						{/* Modal Preview */}
						<div
							style={{
								marginTop: "40px",
								background: "#ffffff",
								borderRadius: "12px",
								padding: "20px",
								border: "2px solid #a78bfa",
								boxShadow: "0 4px 16px rgba(139, 92, 246, 0.15)",
							}}
						>
							{/* Modal Title Bar */}
							<div
								style={{
									display: "flex",
									justifyContent: "space-between",
									alignItems: "center",
									marginBottom: "12px",
									paddingBottom: "12px",
									borderBottom: "2px solid #ede9fe",
								}}
							>
								<div style={{ fontSize: "16px", fontWeight: "700", color: "#5b21b6" }}>
									{component.properties.title || "Modal Title"}
								</div>
								<div
									style={{
										width: "20px",
										height: "20px",
										borderRadius: "4px",
										background: "#ede9fe",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										fontSize: "12px",
										color: "#7c3aed",
										fontWeight: "700",
									}}
								>
									×
								</div>
							</div>
							
							{/* Modal Content */}
							<div
								style={{
									minHeight: "60px",
									background: "#faf5ff",
									borderRadius: "8px",
									border: "2px dashed #c4b5fd",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									padding: "12px",
								}}
							>
								{("children" in component && Array.isArray(component.children) && component.children.length > 0) ? (
									<div style={{ textAlign: "center" }}>
										<div style={{ fontSize: "13px", fontWeight: "600", color: "#6d28d9" }}>
											{component.children.length} Modal Element{component.children.length !== 1 ? "s" : ""}
										</div>
										<div style={{ fontSize: "10px", color: "#8b5cf6", marginTop: "4px" }}>
											Trigger: {component.properties.trigger || "click"}
										</div>
									</div>
								) : (
									<div style={{ textAlign: "center" }}>
										<div style={{ fontSize: "28px", marginBottom: "4px", opacity: 0.4 }}>💬</div>
										<div style={{ fontSize: "12px", fontWeight: "600", color: "#6d28d9" }}>
											Modal Content Area
										</div>
										<div style={{ fontSize: "10px", color: "#8b5cf6", marginTop: "2px" }}>
											Width: {component.properties.width || "600px"}
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
				);

			case "two_step_order":
				return (
					<div
						style={{
							minHeight: "160px",
							background: "linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%)",
							padding: "24px",
							borderRadius: "18px",
							border: "3px solid #10b981",
							position: "relative",
							boxShadow: "0 8px 24px rgba(16, 185, 129, 0.2)",
						}}
					>
						{/* Two-Step Header */}
						<div
							style={{
								position: "absolute",
								top: "12px",
								left: "12px",
								background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
								color: "white",
								padding: "6px 14px",
								borderRadius: "8px",
								fontSize: "11px",
								fontWeight: "700",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
								boxShadow: "0 2px 8px rgba(16, 185, 129, 0.4)",
							}}
						>
							①② Two-Step Order
						</div>
						
						{/* Steps Container */}
						<div
							style={{
								marginTop: "40px",
								display: "grid",
								gridTemplateColumns: "1fr 1fr",
								gap: "16px",
							}}
						>
							{/* Step 1 */}
							<div
								style={{
									background: "#ffffff",
									borderRadius: "12px",
									padding: "16px",
									border: "2px solid #6ee7b7",
									boxShadow: "0 2px 12px rgba(16, 185, 129, 0.1)",
								}}
							>
								<div
									style={{
										display: "flex",
										alignItems: "center",
										gap: "8px",
										marginBottom: "10px",
									}}
								>
									<div
										style={{
											width: "24px",
											height: "24px",
											borderRadius: "50%",
											background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
											color: "white",
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
											fontSize: "12px",
											fontWeight: "700",
										}}
									>
										1
									</div>
									<div style={{ fontSize: "13px", fontWeight: "700", color: "#047857" }}>
										{component.properties.step1Title || "Contact Info"}
									</div>
								</div>
								<div
									style={{
										minHeight: "40px",
										background: "#ecfdf5",
										borderRadius: "6px",
										border: "1px dashed #6ee7b7",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										padding: "8px",
									}}
								>
									<div style={{ fontSize: "10px", color: "#059669", textAlign: "center" }}>
										Step 1 Content
									</div>
								</div>
							</div>
							
							{/* Step 2 */}
							<div
								style={{
									background: "#ffffff",
									borderRadius: "12px",
									padding: "16px",
									border: "2px solid #6ee7b7",
									boxShadow: "0 2px 12px rgba(16, 185, 129, 0.1)",
									opacity: 0.7,
								}}
							>
								<div
									style={{
										display: "flex",
										alignItems: "center",
										gap: "8px",
										marginBottom: "10px",
									}}
								>
									<div
										style={{
											width: "24px",
											height: "24px",
											borderRadius: "50%",
											background: "#d1d5db",
											color: "white",
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
											fontSize: "12px",
											fontWeight: "700",
										}}
									>
										2
									</div>
									<div style={{ fontSize: "13px", fontWeight: "700", color: "#6b7280" }}>
										{component.properties.step2Title || "Payment"}
									</div>
								</div>
								<div
									style={{
										minHeight: "40px",
										background: "#f9fafb",
										borderRadius: "6px",
										border: "1px dashed #d1d5db",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										padding: "8px",
									}}
								>
									<div style={{ fontSize: "10px", color: "#9ca3af", textAlign: "center" }}>
										Step 2 Content
									</div>
								</div>
							</div>
						</div>
						
						{/* Progress Indicator */}
						<div style={{ marginTop: "12px", display: "flex", gap: "8px", justifyContent: "center" }}>
							<div style={{ width: "40px", height: "4px", background: "#10b981", borderRadius: "2px" }} />
							<div style={{ width: "40px", height: "4px", background: "#d1d5db", borderRadius: "2px" }} />
						</div>
					</div>
				);

			case "custom_form":
				return (
					<div
						style={{
							minHeight: "120px",
							background: "linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%)",
							padding: "24px",
							borderRadius: "16px",
							border: "3px solid #ec4899",
							position: "relative",
							boxShadow: "0 6px 20px rgba(236, 72, 153, 0.2)",
						}}
					>
						{/* Custom Form Header */}
						<div
							style={{
								position: "absolute",
								top: "12px",
								left: "12px",
								background: "linear-gradient(135deg, #ec4899 0%, #db2777 100%)",
								color: "white",
								padding: "6px 14px",
								borderRadius: "8px",
								fontSize: "11px",
								fontWeight: "700",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
								boxShadow: "0 2px 8px rgba(236, 72, 153, 0.4)",
							}}
						>
							📋 Custom Form
						</div>
						
						{/* Form Preview */}
						<div
							style={{
								marginTop: "40px",
								background: "#ffffff",
								borderRadius: "12px",
								padding: "20px",
								border: "2px solid #f9a8d4",
								boxShadow: "0 4px 14px rgba(236, 72, 153, 0.12)",
							}}
						>
							{/* Form Info */}
							<div
								style={{
									marginBottom: "12px",
									paddingBottom: "12px",
									borderBottom: "2px solid #fce7f3",
								}}
							>
								<div style={{ fontSize: "12px", color: "#9f1239", marginBottom: "4px" }}>
									<strong>Method:</strong> {component.properties.method || "POST"}
								</div>
								{component.properties.action && (
									<div style={{ fontSize: "10px", color: "#be123c", fontFamily: "monospace" }}>
										Action: {component.properties.action}
									</div>
								)}
							</div>
							
							{/* Form Fields Area */}
							<div
								style={{
									minHeight: "60px",
									background: "#fef5fa",
									borderRadius: "8px",
									border: "2px dashed #f9a8d4",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									padding: "14px",
								}}
							>
								{("children" in component && Array.isArray(component.children) && component.children.length > 0) ? (
									<div style={{ textAlign: "center" }}>
										<div style={{ fontSize: "14px", fontWeight: "600", color: "#9f1239" }}>
											{component.children.length} Form Field{component.children.length !== 1 ? "s" : ""}
										</div>
										<div style={{ fontSize: "11px", color: "#be123c", marginTop: "4px" }}>
											Add inputs, textareas, and submit buttons
										</div>
									</div>
								) : (
									<div style={{ textAlign: "center" }}>
										<div style={{ fontSize: "36px", marginBottom: "6px", opacity: 0.3 }}>📝</div>
										<div style={{ fontSize: "14px", fontWeight: "600", color: "#9f1239", marginBottom: "4px" }}>
											Empty Form
										</div>
										<div style={{ fontSize: "12px", color: "#be123c" }}>
											Drop form elements here
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
				);

			// Form Elements
			case "select_box":
				return (
					<div className="flex flex-col gap-2">
						<label style={{ fontSize: "14px", fontWeight: "600", color: "#374151" }}>
							{component.properties.label}
							{component.properties.required && (
								<span style={{ color: "#ef4444" }}> *</span>
							)}
						</label>
						<select
							style={{
								padding: "12px 16px",
								border: "2px solid #e5e7eb",
								borderRadius: "8px",
								fontSize: "15px",
								background: "#ffffff",
								cursor: "pointer",
								outline: "none",
							}}
						>
							{component.properties.options.map((option: { value: string; label: string }, i: number) => (
								<option key={i} value={option.value}>
									{option.label}
								</option>
							))}
						</select>
					</div>
				);

			case "radio_buttons":
				return (
					<div className="flex flex-col gap-2">
						<label style={{ fontSize: "14px", fontWeight: "500" }}>
							{component.properties.label}
							{component.properties.required && (
								<span style={{ color: "var(--red-9)" }}> *</span>
							)}
						</label>
						<div className="flex flex-col gap-2">
							{component.properties.options.map((option: { value: string; label: string }, i: number) => (
								<label key={i} style={{ display: "flex", alignItems: "center", gap: "8px", fontSize: "14px" }}>
									<input type="radio" name={component.properties.name} value={option.value} />
									{option.label}
								</label>
							))}
						</div>
					</div>
				);

			case "checkbox":
				return (
					<label style={{ display: "flex", alignItems: "center", gap: "8px", fontSize: "14px" }}>
						<input type="checkbox" required={component.properties.required} />
						{component.properties.label}
						{component.properties.required && (
							<span style={{ color: "var(--red-9)" }}> *</span>
						)}
					</label>
				);

			case "submit_button":
				return (
					<Button
						type="submit"
						size={component.properties.size === "sm" ? "1" : component.properties.size === "lg" ? "3" : "2"}
						variant={(component.properties.variant === "outline" ? "soft" : component.properties.variant) as "solid" | "soft" | "ghost" | undefined}
						style={{
							width: component.properties.fullWidth ? "100%" : "auto",
						}}
					>
						{component.properties.text}
					</Button>
				);

			case "search_bar":
				return (
					<div style={{ display: "flex", gap: "8px" }}>
						<input
							type="search"
							placeholder={component.properties.placeholder}
							style={{
								flex: 1,
								padding: "8px 12px",
								border: "1px solid var(--gray-7)",
								borderRadius: "6px",
								fontSize: "14px",
							}}
						/>
						<Button size="2" variant="solid">
							{component.properties.buttonText || "Search"}
						</Button>
					</div>
				);

			// Payment Elements
			case "product_selector":
				return (
					<div
						style={{
							display: "grid",
							gridTemplateColumns: component.properties.layout === "grid" ? "repeat(auto-fit, minmax(250px, 1fr))" : "1fr",
							gap: "20px",
						}}
					>
						{component.properties.products.map((product: { id: string; name: string; price: number; description?: string }, i: number) => (
							<div
								key={i}
								style={{
									padding: "24px",
									border: "2px solid #e5e7eb",
									borderRadius: "16px",
									background: "#ffffff",
									cursor: "pointer",
									transition: "all 0.3s ease",
									boxShadow: "0 4px 12px rgba(0, 0, 0, 0.05)",
								}}
							>
								<div style={{ fontSize: "18px", fontWeight: "600", color: "#111827", marginBottom: "8px" }}>
									{product.name}
								</div>
								{product.description && (
									<div style={{ fontSize: "14px", color: "#6b7280", marginBottom: "16px", lineHeight: "1.5" }}>
										{product.description}
									</div>
								)}
								<div style={{ fontSize: "32px", fontWeight: "700", color: "#6366f1", marginBottom: "16px" }}>
									${product.price}
								</div>
								<button
									type="button"
									style={{
										width: "100%",
										padding: "12px",
										background: "#6366f1",
										color: "white",
										border: "none",
										borderRadius: "8px",
										fontWeight: "600",
										cursor: "pointer",
										fontSize: "15px",
									}}
								>
									Select
								</button>
							</div>
						))}
					</div>
				);

			case "order_bump":
				return (
					<div
						style={{
							padding: "24px",
							border: "3px dashed #fbbf24",
							borderRadius: "16px",
							background: "linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)",
							display: "flex",
							gap: "16px",
							alignItems: "flex-start",
							boxShadow: "0 4px 16px rgba(251, 191, 36, 0.2)",
						}}
					>
						<input 
							type="checkbox" 
							style={{ 
								width: "24px", 
								height: "24px",
								marginTop: "2px",
								cursor: "pointer",
								accentColor: "#f59e0b",
							}} 
						/>
						<div style={{ flex: 1 }}>
							<div style={{ display: "inline-block", background: "#f59e0b", color: "white", padding: "4px 12px", borderRadius: "6px", fontSize: "12px", fontWeight: "700", marginBottom: "12px" }}>
								✨ SPECIAL OFFER
							</div>
							<div style={{ fontSize: "18px", fontWeight: "700", color: "#78350f", marginBottom: "8px" }}>
								{component.properties.productName}
							</div>
							<div style={{ fontSize: "14px", color: "#92400e", marginBottom: "12px", lineHeight: "1.5" }}>
								{component.properties.description}
							</div>
							<div style={{ fontSize: "24px", fontWeight: "800", color: "#b45309" }}>
								Only +${component.properties.price}
							</div>
						</div>
					</div>
				);

			case "order_summary":
				return (
					<div
						style={{
							padding: "20px",
							border: "1px solid var(--gray-6)",
							borderRadius: "12px",
							background: "var(--gray-2)",
						}}
					>
						<Heading size="4" style={{ marginBottom: "16px" }}>
							Order Summary
						</Heading>
						<div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
							<div style={{ display: "flex", justifyContent: "space-between" }}>
								<Text size="2">Subtotal</Text>
								<Text size="2" weight="medium">$99.00</Text>
							</div>
							{component.properties.showTax && (
								<div style={{ display: "flex", justifyContent: "space-between" }}>
									<Text size="2" color="gray">Tax</Text>
									<Text size="2" color="gray">$9.90</Text>
								</div>
							)}
							{component.properties.showShipping && (
								<div style={{ display: "flex", justifyContent: "space-between" }}>
									<Text size="2" color="gray">Shipping</Text>
									<Text size="2" color="gray">$5.00</Text>
								</div>
							)}
							<hr style={{ border: "none", borderTop: "1px solid var(--gray-6)" }} />
							<div style={{ display: "flex", justifyContent: "space-between" }}>
								<Text size="3" weight="bold">Total</Text>
								<Text size="3" weight="bold" style={{ color: "var(--accent-9)" }}>$113.90</Text>
							</div>
						</div>
					</div>
				);

			case "credit_card_form":
				return (
					<div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
						<div>
							<label style={{ fontSize: "14px", fontWeight: "500", display: "block", marginBottom: "8px" }}>
								Card Number
							</label>
							<input
								type="text"
								placeholder="1234 5678 9012 3456"
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									fontSize: "14px",
								}}
							/>
						</div>
						<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "12px" }}>
							<div>
								<label style={{ fontSize: "14px", fontWeight: "500", display: "block", marginBottom: "8px" }}>
									Expiry
								</label>
								<input
									type="text"
									placeholder="MM/YY"
									style={{
										width: "100%",
										padding: "8px 12px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										fontSize: "14px",
									}}
								/>
							</div>
							<div>
								<label style={{ fontSize: "14px", fontWeight: "500", display: "block", marginBottom: "8px" }}>
									CVC
								</label>
								<input
									type="text"
									placeholder="123"
									style={{
										width: "100%",
										padding: "8px 12px",
										border: "1px solid var(--gray-7)",
										borderRadius: "6px",
										fontSize: "14px",
									}}
								/>
							</div>
						</div>
					</div>
				);

			case "shipping_info":
				return (
					<div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
						<Heading size="4">Shipping Information</Heading>
						{component.properties.requiredFields.includes("address") && (
							<div>
								<label style={{ fontSize: "14px", fontWeight: "500", display: "block", marginBottom: "8px" }}>
									Address
								</label>
								<input type="text" style={{ width: "100%", padding: "8px 12px", border: "1px solid var(--gray-7)", borderRadius: "6px", fontSize: "14px" }} />
							</div>
						)}
						<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "12px" }}>
							{component.properties.requiredFields.includes("city") && (
								<div>
									<label style={{ fontSize: "14px", fontWeight: "500", display: "block", marginBottom: "8px" }}>
										City
									</label>
									<input type="text" style={{ width: "100%", padding: "8px 12px", border: "1px solid var(--gray-7)", borderRadius: "6px", fontSize: "14px" }} />
								</div>
							)}
							{component.properties.requiredFields.includes("zip") && (
								<div>
									<label style={{ fontSize: "14px", fontWeight: "500", display: "block", marginBottom: "8px" }}>
										ZIP Code
									</label>
									<input type="text" style={{ width: "100%", padding: "8px 12px", border: "1px solid var(--gray-7)", borderRadius: "6px", fontSize: "14px" }} />
								</div>
							)}
						</div>
					</div>
				);

			// Webinar Elements
			case "auto_webinar_date":
			case "auto_webinar_time":
			case "local_time_display":
				return (
					<div
						style={{
							padding: "12px 20px",
							background: "var(--accent-3)",
							border: "2px solid var(--accent-6)",
							borderRadius: "8px",
							textAlign: "center",
						}}
					>
						<Text size="3" weight="bold" style={{ color: "var(--accent-11)" }}>
							{component.type === "auto_webinar_date" && "December 15, 2024"}
							{component.type === "auto_webinar_time" && "7:00 PM EST"}
							{component.type === "local_time_display" && "7:00 PM (Your Local Time)"}
						</Text>
					</div>
				);

			case "webinar_countdown":
				return (
					<div>
						<Text size="2" align="center" style={{ marginBottom: "12px" }}>
							Webinar starts in:
						</Text>
						<div
							style={{
								display: "flex",
								gap: "12px",
								justifyContent: "center",
							}}
						>
							{["23", "14", "59", "32"].map((unit, i) => (
								<div
									key={i}
									style={{
										background: "var(--accent-3)",
										padding: "16px 20px",
										borderRadius: "8px",
										border: "2px solid var(--accent-6)",
										textAlign: "center",
									}}
								>
									<div style={{ fontSize: "28px", fontWeight: "bold", color: "var(--accent-11)" }}>
										{unit}
									</div>
									<Text size="1" color="gray">
										{["Days", "Hours", "Mins", "Secs"][i]}
									</Text>
								</div>
							))}
						</div>
					</div>
				);

			case "sms_signup":
				return (
					<div style={{ display: "flex", gap: "12px", alignItems: "flex-end" }}>
						<div style={{ flex: 1 }}>
							<label style={{ fontSize: "14px", fontWeight: "500", display: "block", marginBottom: "8px" }}>
								{component.properties.label}
							</label>
							<input
								type="tel"
								placeholder={component.properties.placeholder}
								style={{
									width: "100%",
									padding: "8px 12px",
									border: "1px solid var(--gray-7)",
									borderRadius: "6px",
									fontSize: "14px",
								}}
							/>
						</div>
						<Button size="2" variant="solid">
							{component.properties.buttonText}
						</Button>
					</div>
				);

			// Enhancement Elements
			case "card":
				return (
					<div
						style={{
							background: "#ffffff",
							// No border radius, box shadow, or transitions - natural website card
							overflow: "hidden",
							border: component.properties.variant === "outlined" ? "1px solid #e5e7eb" : "none",
						}}
					>
						{component.properties.imageUrl && (
							<div style={{ position: "relative", width: "100%", height: "200px", overflow: "hidden" }}>
								<Image
									src={component.properties.imageUrl}
									alt={component.properties.title || "Card image"}
									width={800}
									height={400}
									style={{
										width: "100%",
										height: "100%",
										objectFit: "cover",
									}}
								/>
							</div>
						)}
						<div style={{ padding: "24px" }}>
							{component.properties.title && (
								<h3
									style={{
										fontSize: "24px",
										fontWeight: "700",
										color: "#111827",
										marginBottom: "12px",
										lineHeight: "1.3",
									}}
								>
									{component.properties.title}
								</h3>
							)}
							{component.properties.description && (
								<p
									style={{
										fontSize: "15px",
										color: "#6b7280",
										lineHeight: "1.6",
										marginBottom: "20px",
									}}
								>
									{component.properties.description}
								</p>
							)}
							{component.properties.buttonText && (
								<a
									href={component.properties.buttonUrl || "#"}
									style={{
										display: "inline-flex",
										alignItems: "center",
										padding: "12px 24px",
										background: "#6366f1",
										color: "white",
										fontSize: "15px",
										fontWeight: "600",
										borderRadius: "8px",
										textDecoration: "none",
										// No transitions or box shadows - natural website button
									}}
								>
									{component.properties.buttonText}
								</a>
							)}
						</div>
					</div>
				);

			case "menu":
				return (
					<nav
						style={{
							display: "flex",
							flexDirection: component.properties.orientation === "vertical" ? "column" : "row",
							gap: "16px",
							justifyContent:
								component.properties.alignment === "center"
									? "center"
									: component.properties.alignment === "right"
										? "flex-end"
										: "flex-start",
						}}
					>
						{component.properties.items.map((item: { label: string; url: string }, i: number) => (
							<a
								key={i}
								href={item.url}
								style={{
									padding: "8px 16px",
									fontSize: "14px",
									fontWeight: "500",
									color: "var(--gray-12)",
									textDecoration: "none",
									borderRadius: "6px",
									transition: "background 0.2s",
								}}
							>
								{item.label}
							</a>
						))}
					</nav>
				);

			case "link_list":
				return (
					<div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
						{component.properties.items.map((item: { label: string; url: string; description?: string }, i: number) => (
							<a
								key={i}
								href={item.url}
								style={{
									padding: "12px",
									border: "1px solid var(--gray-6)",
									borderRadius: "8px",
									background: "var(--gray-1)",
									textDecoration: "none",
									transition: "all 0.2s",
								}}
							>
								<Text size="2" weight="bold" style={{ color: "var(--accent-9)" }}>
									{item.label}
								</Text>
								{item.description && (
									<Text size="1" color="gray" style={{ marginTop: "4px" }}>
										{item.description}
									</Text>
								)}
							</a>
						))}
					</div>
				);

			case "image_carousel":
				return (
					<div
						style={{
							position: "relative",
							borderRadius: "12px",
							overflow: "hidden",
							background: "var(--gray-3)",
						}}
					>
						<Image
							src={component.properties.images[0]?.src || "https://via.placeholder.com/800x400"}
							alt={component.properties.images[0]?.alt || "Carousel image"}
							width={800}
							height={400}
							style={{ width: "100%", height: "auto" }}
						/>
						<div
							style={{
								position: "absolute",
								bottom: "12px",
								left: "50%",
								transform: "translateX(-50%)",
								display: "flex",
								gap: "8px",
							}}
						>
							{component.properties.images.map((_: unknown, i: number) => (
								<div
									key={i}
									style={{
										width: "8px",
										height: "8px",
										borderRadius: "50%",
										background: i === 0 ? "white" : "rgba(255, 255, 255, 0.5)",
									}}
								/>
							))}
						</div>
					</div>
				);

			case "map":
				return (
					<div
						style={{
							height: component.properties.height,
							background: "var(--gray-3)",
							borderRadius: "12px",
							border: "1px solid var(--gray-6)",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							flexDirection: "column",
							gap: "8px",
						}}
					>
						<svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="var(--gray-9)" strokeWidth="2">
							<path strokeLinecap="round" strokeLinejoin="round" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
						</svg>
						<Text size="2" color="gray">
							{component.properties.address || "Map Location"}
						</Text>
					</div>
				);

			case "custom_html":
			case "embed_code":
				return (
					<div
						style={{
							padding: "16px",
							background: "var(--gray-3)",
							borderRadius: "8px",
							border: "2px dashed var(--gray-6)",
							fontFamily: "monospace",
							fontSize: "12px",
							color: "var(--gray-11)",
							overflow: "auto",
						}}
					>
						{component.type === "custom_html" ? component.properties.html : component.properties.code}
					</div>
				);

			case "audio":
				return (
					<audio
						controls={component.properties.controls}
						autoPlay={component.properties.autoplay}
						loop={component.properties.loop}
						style={{ width: "100%", borderRadius: "8px" }}
					>
						<source src={component.properties.src} />
						Your browser does not support the audio element.
					</audio>
				);

			case "icon":
				return (
					<div
						style={{
							display: "flex",
							justifyContent:
								component.properties.alignment === "center"
									? "center"
									: component.properties.alignment === "right"
										? "flex-end"
										: "flex-start",
						}}
					>
					<svg
						width={parseInt(component.properties.size ?? '32') || 32}
						height={parseInt(component.properties.size ?? '32') || 32}
							viewBox="0 0 24 24"
							fill="none"
							stroke={component.properties.color}
							strokeWidth="2"
						>
							<path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
						</svg>
					</div>
				);

			case "social_share":
				return (
					<div
						style={{
							display: "flex",
							gap: "12px",
							justifyContent:
								component.properties.alignment === "center"
									? "center"
									: component.properties.alignment === "right"
										? "flex-end"
										: "flex-start",
						}}
					>
						{component.properties.platforms.map((platform: string, i: number) => (
							<button
								key={i}
								type="button"
								style={{
									width: "40px",
									height: "40px",
									borderRadius: "50%",
									border: "none",
									background: "var(--accent-9)",
									color: "white",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									cursor: "pointer",
								}}
							>
								{platform.charAt(0).toUpperCase()}
							</button>
						))}
					</div>
				);

			case "cart_total":
				return (
					<div
						style={{
							padding: "20px",
							border: "2px solid var(--accent-6)",
							borderRadius: "12px",
							background: "var(--accent-2)",
						}}
					>
						<div style={{ display: "flex", justifyContent: "space-between", marginBottom: "12px" }}>
							<Text size="4" weight="bold">Total</Text>
							<Text size="4" weight="bold" style={{ color: "var(--accent-11)" }}>$99.00</Text>
						</div>
						{component.properties.showBreakdown && (
							<div style={{ fontSize: "12px", color: "var(--gray-11)" }}>
								<div style={{ display: "flex", justifyContent: "space-between", marginTop: "8px" }}>
									<span>Subtotal:</span>
									<span>$89.00</span>
								</div>
								<div style={{ display: "flex", justifyContent: "space-between", marginTop: "4px" }}>
									<span>Tax:</span>
									<span>$10.00</span>
								</div>
							</div>
						)}
					</div>
				);

			case "hidden_field":
				return (
					<div
						style={{
							padding: "12px",
							background: "var(--gray-3)",
							border: "1px dashed var(--gray-6)",
							borderRadius: "6px",
							textAlign: "center",
						}}
					>
						<Text size="1" color="gray">
							Hidden field: {component.properties.name}
						</Text>
					</div>
				);

		default:
			return (
				<div
					style={{
						padding: "16px",
						background: "var(--gray-3)",
						borderRadius: "8px",
						border: "1px solid var(--gray-4)",
					}}
				>
					<Badge color="gray" size="1">
						{(component as EditorComponent).type}
					</Badge>
					<Text size="2" color="gray" style={{ marginTop: "8px" }}>
						{(component as EditorComponent).name}
					</Text>
				</div>
			);
		}
	};

	return (
		<div
			style={baseStyle}
			onClick={onSelect}
			onDoubleClick={onEdit}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
		>
			{/* Component Type Badge */}
			{(isHovered || isSelected) && (
				<div
					style={{
						position: "absolute",
						top: "-8px",
						left: "12px",
						background: "var(--gray-12)",
						color: "white",
						padding: "2px 8px",
						borderRadius: "4px",
						fontSize: "10px",
						fontWeight: "600",
						textTransform: "uppercase",
						letterSpacing: "0.5px",
						zIndex: 10,
					}}
				>
					{component.type.replace(/_/g, " ")}
				</div>
			)}
			
			{renderContent()}
			
			{/* Selection Indicator */}
			{isSelected && (
				<div
					style={{
						position: "absolute",
						top: "-12px",
						right: "-12px",
						background: "var(--accent-9)",
						color: "white",
						borderRadius: "50%",
						width: "28px",
						height: "28px",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						fontSize: "14px",
						fontWeight: "bold",
						boxShadow: "0 2px 8px rgba(99, 102, 241, 0.3)",
						border: "2px solid white",
					}}
				>
					✓
				</div>
			)}

			{/* Hover Actions */}
			{isHovered && !isSelected && (
				<div
					style={{
						position: "absolute",
						top: "8px",
						right: "8px",
						background: "var(--gray-12)",
						color: "white",
						padding: "4px 8px",
						borderRadius: "6px",
						fontSize: "11px",
						fontWeight: "500",
						opacity: 0.8,
					}}
				>
					Click to select
				</div>
			)}
		</div>
	);
}
