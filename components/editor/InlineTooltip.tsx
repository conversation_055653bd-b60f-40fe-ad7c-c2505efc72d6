"use client";

import type { EditorComponent } from "@/lib/types/editor";
import { Button, Text } from "frosted-ui";
import React, { useState, useRef, useEffect, useMemo } from "react";

interface InlineTooltipProps {
	component: EditorComponent;
	onUpdate: (properties: Record<string, unknown>) => void;
	onDelete: () => void;
	position: { x: number; y: number };
	onClose: () => void;
}

export function InlineTooltip({
	component,
	onUpdate,
	onDelete,
	position,
	onClose,
}: InlineTooltipProps) {
	const [activeTab, setActiveTab] = useState<"text" | "element">("text");
	const [tooltipHeight, setTooltipHeight] = useState<number>(400); // Default height
	const tooltipRef = useRef<HTMLDivElement>(null);

	// Measure tooltip height after render
	useEffect(() => {
		if (tooltipRef.current) {
			const height = tooltipRef.current.offsetHeight;
			setTooltipHeight(height);
		}
	}, [activeTab, component]); // Re-measure when content changes

	// Memoize the tooltip position to avoid recalculating on every render
	const tooltipStyle = useMemo(() => {
		const maxWidth = 320;
		const maxHeight = Math.max(tooltipHeight, 300); // Use actual height or minimum
		const margin = 20; // Minimum margin from screen edges
		
		// Calculate horizontal position
		let left = position.x;
		if (left + maxWidth > window.innerWidth - margin) {
			left = window.innerWidth - maxWidth - margin;
		}
		if (left < margin) {
			left = margin;
		}
		
		// Calculate vertical position
		let top = position.y + 10; // Default: position below click
		
		// Check if tooltip would overflow at bottom
		if (top + maxHeight > window.innerHeight - margin) {
			// Position above click instead
			top = position.y - maxHeight - 10;
			
			// If it still doesn't fit above, position at top of screen
			if (top < margin) {
				top = margin;
			}
		}
		
		// Ensure tooltip doesn't go above screen
		if (top < margin) {
			top = margin;
		}
		
		return {
			position: "fixed" as const,
			zIndex: 1000,
			background: "white",
			border: "1px solid #e5e7eb",
			borderRadius: "8px",
			boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
			padding: "16px",
			minWidth: "280px",
			maxWidth: `${maxWidth}px`,
			maxHeight: `${maxHeight}px`,
			overflowY: "auto" as const,
			left: `${left}px`,
			top: `${top}px`,
		};
	}, [position.x, position.y, tooltipHeight]);

	// Close tooltip when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
				onClose();
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, [onClose]);

	const handlePropertyChange = (key: string, value: unknown) => {
		onUpdate({
			...component.properties,
			[key]: value,
		});
	};

	const isTextComponent = [
		"headline", "sub_headline", "paragraph", "button", "hero_section", 
		"features_section", "pricing_section", "testimonials_section", "form_section"
	].includes(component.type);

	// Type assertion to access properties
	const props = component.properties as Record<string, unknown>;

	const renderTextControls = () => {
		if (!isTextComponent) return null;

		return (
			<div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
				{/* Text Content */}
				<div>
					<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Content</Text>
					<textarea
						value={(props.text as string) || ""}
						onChange={(e) => handlePropertyChange("text", e.target.value)}
						placeholder="Enter your text..."
						style={{
							width: "100%",
							minHeight: "60px",
							padding: "8px 12px",
							fontSize: "12px",
							border: "1px solid #d1d5db",
							borderRadius: "4px",
							background: "white",
							resize: "vertical",
							fontFamily: "inherit",
						}}
					/>
				</div>

				{/* Font Size */}
				<div>
					<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Font Size</Text>
					<select
						value={(props.fontSize as string) || "16px"}
						onChange={(e) => handlePropertyChange("fontSize", e.target.value)}
						style={{
							width: "100%",
							padding: "4px 8px",
							fontSize: "12px",
							border: "1px solid #d1d5db",
							borderRadius: "4px",
							background: "white",
						}}
					>
						<option value="12px">12px</option>
						<option value="14px">14px</option>
						<option value="16px">16px</option>
						<option value="18px">18px</option>
						<option value="20px">20px</option>
						<option value="24px">24px</option>
						<option value="32px">32px</option>
						<option value="48px">48px</option>
						<option value="64px">64px</option>
					</select>
				</div>

				{/* Text Color */}
				<div>
					<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Text Color</Text>
					<div style={{ display: "flex", gap: "8px" }}>
						<input
							type="color"
							value={(props.color as string) || (props.textColor as string) || "#000000"}
							onChange={(e) => {
								const colorKey = props.textColor ? "textColor" : "color";
								handlePropertyChange(colorKey, e.target.value);
							}}
							style={{
								width: "32px",
								height: "24px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								cursor: "pointer",
							}}
						/>
						<input
							type="text"
							value={(props.color as string) || (props.textColor as string) || "#000000"}
							onChange={(e) => {
								const colorKey = props.textColor ? "textColor" : "color";
								handlePropertyChange(colorKey, e.target.value);
							}}
							style={{
								flex: 1,
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								fontFamily: "monospace",
								background: "white",
							}}
						/>
					</div>
				</div>

				{/* Font Weight */}
				<div>
					<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Font Weight</Text>
					<select
						value={(props.fontWeight as string) || "normal"}
						onChange={(e) => handlePropertyChange("fontWeight", e.target.value)}
						style={{
							width: "100%",
							padding: "4px 8px",
							fontSize: "12px",
							border: "1px solid #d1d5db",
							borderRadius: "4px",
							background: "white",
						}}
					>
						<option value="normal">Normal</option>
						<option value="500">Medium</option>
						<option value="600">Semi Bold</option>
						<option value="700">Bold</option>
						<option value="800">Extra Bold</option>
					</select>
				</div>

				{/* Text Formatting */}
				<div>
					<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Formatting</Text>
					<div style={{ display: "flex", gap: "4px" }}>
						<button
						onClick={() => {
							const currentWeight = (props.fontWeight as string) || "normal";
							const newWeight = currentWeight === "700" ? "normal" : "700";
							handlePropertyChange("fontWeight", newWeight);
						}}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: ((props.fontWeight as string) || "normal") === "700" 
									? "#dbeafe" 
									: "white",
								borderColor: ((props.fontWeight as string) || "normal") === "700" 
									? "#93c5fd" 
									: "#d1d5db",
								cursor: "pointer",
							}}
						>
							B
						</button>
						<button
						onClick={() => {
							const currentStyle = (props.fontStyle as string) || "normal";
							const newStyle = currentStyle === "italic" ? "normal" : "italic";
							handlePropertyChange("fontStyle", newStyle);
						}}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: ((props.fontStyle as string) || "normal") === "italic" 
									? "#dbeafe" 
									: "white",
								borderColor: ((props.fontStyle as string) || "normal") === "italic" 
									? "#93c5fd" 
									: "#d1d5db",
								cursor: "pointer",
							}}
						>
							I
						</button>
						<button
						onClick={() => {
							const currentDecoration = (props.textDecoration as string) || "none";
							const newDecoration = currentDecoration === "underline" ? "none" : "underline";
							handlePropertyChange("textDecoration", newDecoration);
						}}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: ((props.textDecoration as string) || "none") === "underline" 
									? "#dbeafe" 
									: "white",
								borderColor: ((props.textDecoration as string) || "none") === "underline" 
									? "#93c5fd" 
									: "#d1d5db",
								cursor: "pointer",
							}}
						>
							U
						</button>
					</div>
				</div>

				{/* Heading Level (for headline components) */}
				{component.type === "headline" && (
					<div>
						<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Heading Level</Text>
						<select
							value={(props.level as string) || "h2"}
							onChange={(e) => handlePropertyChange("level", e.target.value)}
							style={{
								width: "100%",
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						>
							<option value="h1">H1</option>
							<option value="h2">H2</option>
							<option value="h3">H3</option>
							<option value="h4">H4</option>
							<option value="h5">H5</option>
							<option value="h6">H6</option>
						</select>
					</div>
				)}
			</div>
		);
	};

	const renderElementControls = () => {
		return (
			<div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
				{/* Padding */}
				<div>
					<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Padding</Text>
					<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "4px" }}>
						<input
							type="number"
							placeholder="Top"
							value={(props.paddingTop as string) || ""}
							onChange={(e) => handlePropertyChange("paddingTop", e.target.value ? `${e.target.value}px` : "")}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
						<input
							type="number"
							placeholder="Right"
							value={(props.paddingRight as string) || ""}
							onChange={(e) => handlePropertyChange("paddingRight", e.target.value ? `${e.target.value}px` : "")}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
						<input
							type="number"
							placeholder="Bottom"
							value={(props.paddingBottom as string) || ""}
							onChange={(e) => handlePropertyChange("paddingBottom", e.target.value ? `${e.target.value}px` : "")}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
						<input
							type="number"
							placeholder="Left"
							value={(props.paddingLeft as string) || ""}
							onChange={(e) => handlePropertyChange("paddingLeft", e.target.value ? `${e.target.value}px` : "")}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
					</div>
				</div>

				{/* Margin */}
				<div>
					<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Margin</Text>
					<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "4px" }}>
						<input
							type="number"
							placeholder="Top"
							value={(props.marginTop as string) || ""}
							onChange={(e) => handlePropertyChange("marginTop", e.target.value ? `${e.target.value}px` : "")}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
						<input
							type="number"
							placeholder="Right"
							value={(props.marginRight as string) || ""}
							onChange={(e) => handlePropertyChange("marginRight", e.target.value ? `${e.target.value}px` : "")}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
						<input
							type="number"
							placeholder="Bottom"
							value={(props.marginBottom as string) || ""}
							onChange={(e) => handlePropertyChange("marginBottom", e.target.value ? `${e.target.value}px` : "")}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
						<input
							type="number"
							placeholder="Left"
							value={(props.marginLeft as string) || ""}
							onChange={(e) => handlePropertyChange("marginLeft", e.target.value ? `${e.target.value}px` : "")}
							style={{
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
					</div>
				</div>

				{/* Border Radius */}
				<div>
					<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Border Radius</Text>
					<input
						type="number"
						placeholder="0"
						value={props.borderRadius ? parseInt(props.borderRadius as string) : ""}
						onChange={(e) => handlePropertyChange("borderRadius", e.target.value ? `${e.target.value}px` : "0px")}
						style={{
							width: "100%",
							padding: "4px 8px",
							fontSize: "12px",
							border: "1px solid #d1d5db",
							borderRadius: "4px",
							background: "white",
						}}
					/>
				</div>

				{/* Width & Height */}
				<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
					<div>
						<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Width</Text>
						<input
							type="text"
							placeholder="auto"
							value={(props.width as string) || ""}
							onChange={(e) => handlePropertyChange("width", e.target.value)}
							style={{
								width: "100%",
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
					</div>
					<div>
						<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Height</Text>
						<input
							type="text"
							placeholder="auto"
							value={(props.height as string) || ""}
							onChange={(e) => handlePropertyChange("height", e.target.value)}
							style={{
								width: "100%",
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								background: "white",
							}}
						/>
					</div>
				</div>

				{/* Background Color */}
				<div>
					<Text size="1" weight="medium" style={{ display: "block", marginBottom: "4px" }}>Background</Text>
					<div style={{ display: "flex", gap: "8px" }}>
						<input
							type="color"
							value={(props.backgroundColor as string) || "#ffffff"}
							onChange={(e) => handlePropertyChange("backgroundColor", e.target.value)}
							style={{
								width: "32px",
								height: "24px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								cursor: "pointer",
							}}
						/>
						<input
							type="text"
							value={(props.backgroundColor as string) || "#ffffff"}
							onChange={(e) => handlePropertyChange("backgroundColor", e.target.value)}
							style={{
								flex: 1,
								padding: "4px 8px",
								fontSize: "12px",
								border: "1px solid #d1d5db",
								borderRadius: "4px",
								fontFamily: "monospace",
								background: "white",
							}}
						/>
					</div>
				</div>

				{/* Delete Button */}
				<Button
					size="1"
					color="red"
					variant="soft"
					onClick={onDelete}
					style={{ width: "100%" }}
				>
					Delete Element
				</Button>
			</div>
		);
	};

	return (
		<div
			ref={tooltipRef}
			style={tooltipStyle}
		>
			{/* Header */}
			<div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: "12px" }}>
				<Text size="2" weight="bold" style={{ textTransform: "capitalize" }}>
					{component.type.replace(/_/g, " ")}
				</Text>
				<button
					onClick={onClose}
					style={{
						background: "none",
						border: "none",
						color: "#9ca3af",
						fontSize: "18px",
						cursor: "pointer",
						padding: "0",
						lineHeight: 1,
					}}
				>
					×
				</button>
			</div>

			{/* Tabs */}
			<div style={{ display: "flex", gap: "4px", marginBottom: "12px", background: "#f3f4f6", padding: "4px", borderRadius: "6px" }}>
				<button
					onClick={() => setActiveTab("text")}
					style={{
						flex: 1,
						padding: "4px 8px",
						fontSize: "12px",
						borderRadius: "4px",
						border: "none",
						background: activeTab === "text" ? "white" : "transparent",
						boxShadow: activeTab === "text" ? "0 1px 2px rgba(0, 0, 0, 0.1)" : "none",
						color: activeTab === "text" ? "#111827" : "#6b7280",
						cursor: "pointer",
					}}
				>
					Text
				</button>
				<button
					onClick={() => setActiveTab("element")}
					style={{
						flex: 1,
						padding: "4px 8px",
						fontSize: "12px",
						borderRadius: "4px",
						border: "none",
						background: activeTab === "element" ? "white" : "transparent",
						boxShadow: activeTab === "element" ? "0 1px 2px rgba(0, 0, 0, 0.1)" : "none",
						color: activeTab === "element" ? "#111827" : "#6b7280",
						cursor: "pointer",
					}}
				>
					Element
				</button>
			</div>

			{/* Content */}
			{activeTab === "text" ? renderTextControls() : renderElementControls()}
		</div>
	);
}