"use client";

import {
	componentCategories,
} from "@/lib/editor/component-definitions";
import type { ComponentType } from "@/lib/types/editor";
import { Heading, Text } from "frosted-ui";
import React, { useState } from "react";

interface ComponentPaletteProps {
	onAddComponent: (componentType: ComponentType) => void;
}

export function ComponentPalette({ onAddComponent }: ComponentPaletteProps) {
	const [activeCategory, setActiveCategory] = useState("layout");
	const [searchQuery, setSearchQuery] = useState("");

	const activeComponents =
		componentCategories.find((cat) => cat.id === activeCategory)?.components ||
		[];

	const filteredComponents = activeComponents.filter((comp) =>
		comp.name.toLowerCase().includes(searchQuery.toLowerCase()),
	);

	return (
		<div
			style={{
				width: "280px",
				height: "100%",
				background: "var(--gray-2)",
				borderRight: "1px solid var(--gray-6)",
				display: "flex",
				flexDirection: "column",
				overflow: "hidden",
			}}
		>
			{/* Header */}
			<div style={{ padding: "16px", borderBottom: "1px solid var(--gray-6)" }}>
				<Heading size="4">Components</Heading>
				<Text size="1" color="gray">
					Drag to add to page
				</Text>
			</div>

			{/* Search */}
			<div style={{ padding: "12px" }}>
				<input
					type="text"
					placeholder="Search components..."
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
					style={{
						width: "100%",
						padding: "8px 12px",
						border: "1px solid var(--gray-7)",
						borderRadius: "6px",
						fontSize: "14px",
						background: "var(--gray-1)",
					}}
				/>
			</div>

			{/* Categories */}
			<div
				style={{
					display: "flex",
					gap: "4px",
					padding: "0 12px 12px",
					overflowX: "auto",
					flexWrap: "wrap",
				}}
			>
				{componentCategories.map((category) => (
					<button
						key={category.id}
						type="button"
						onClick={() => setActiveCategory(category.id)}
						style={{
							padding: "6px 10px",
							borderRadius: "6px",
							border: "1px solid var(--gray-6)",
							background:
								activeCategory === category.id
									? "var(--accent-9)"
									: "var(--gray-3)",
							color:
								activeCategory === category.id
									? "white"
									: "var(--gray-11)",
							fontSize: "12px",
							cursor: "pointer",
							whiteSpace: "nowrap",
							fontWeight: activeCategory === category.id ? "600" : "400",
						}}
					>
						{category.icon} {category.name}
					</button>
				))}
			</div>

			{/* Components List */}
			<div
				style={{
					flex: 1,
					overflowY: "auto",
					padding: "12px",
				}}
			>
				<div className="flex flex-col gap-2">
					{filteredComponents.map((component) => (
						<div
							key={component.type}
							draggable
							onDragStart={(e) => {
								e.dataTransfer.setData("componentType", component.type);
								e.currentTarget.style.opacity = "0.5";
							}}
							onDragEnd={(e) => {
								e.currentTarget.style.opacity = "1";
							}}
							onClick={() => onAddComponent(component.type)}
							style={{
								padding: "12px",
								background: "var(--gray-1)",
								border: "1px solid var(--gray-6)",
								borderRadius: "10px",
								cursor: "grab",
								transition: "all 0.2s ease",
							}}
							onMouseEnter={(e) => {
								e.currentTarget.style.background = "var(--accent-2)";
								e.currentTarget.style.borderColor = "var(--accent-7)";
								e.currentTarget.style.transform = "translateX(4px)";
								e.currentTarget.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.08)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.background = "var(--gray-1)";
								e.currentTarget.style.borderColor = "var(--gray-6)";
								e.currentTarget.style.transform = "translateX(0)";
								e.currentTarget.style.boxShadow = "none";
							}}
						>
							<div className="flex items-center gap-3">
								<div
									style={{
										width: "36px",
										height: "36px",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										background: "var(--gray-3)",
										borderRadius: "8px",
										fontSize: "18px",
										flexShrink: 0,
									}}
								>
									{component.icon}
								</div>
								<div style={{ flex: 1, minWidth: 0 }}>
									<Text size="2" weight="medium" style={{ display: "block" }}>
										{component.name}
									</Text>
								</div>
							</div>
						</div>
					))}
				</div>

				{filteredComponents.length === 0 && (
					<div
						style={{
							padding: "24px",
							textAlign: "center",
						}}
					>
						<Text size="2" color="gray">
							No components found
						</Text>
					</div>
				)}
			</div>

			{/* Footer Info */}
			<div
				style={{
					padding: "12px",
					borderTop: "1px solid var(--gray-6)",
					background: "var(--gray-1)",
				}}
			>
				<Text size="1" color="gray">
					{filteredComponents.length} component
					{filteredComponents.length !== 1 ? "s" : ""} available
				</Text>
			</div>
		</div>
	);
}
