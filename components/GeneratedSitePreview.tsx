"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, Heading, Text } from "frosted-ui";
import type { FunnelSchema } from "@/lib/types/funnel";
import Image from "next/image";

interface GeneratedSitePreviewProps {
	funnel: FunnelSchema;
	onEdit: () => void;
	onCreateNew: () => void;
	companyId: string;
	userName: string;
	username: string;
	companyTitle: string;
}

export function GeneratedSitePreview({
	funnel,
	onEdit,
	onCreateNew,
}: GeneratedSitePreviewProps) {
	return (
		<div
			style={{
				height: "100vh",
				display: "flex",
				flexDirection: "column",
				background: "var(--gray-1)",
			}}
		>
			{/* Fixed Top Bar */}
			<div
				style={{
					height: "56px",
					background: "var(--color-panel-solid)",
					borderBottom: "1px solid var(--gray-6)",
					display: "flex",
					alignItems: "center",
					justifyContent: "space-between",
					padding: "0 20px",
					position: "fixed",
					top: 0,
					left: 0,
					right: 0,
					zIndex: 50,
					backdropFilter: "blur(12px)",
				}}
			>
				{/* Left: Logo */}
				<div className="flex items-center">
					<div
						style={{
							width: "40px",
							height: "40px",
							borderRadius: "13px",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							overflow: "visible",
							cursor: "pointer",
							transition: "transform 0.2s ease",
						}}
						onClick={() => window.location.reload()}
						onMouseEnter={(e) => {
							e.currentTarget.style.transform = "scale(1.1)";
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.transform = "scale(1)";
						}}
					>
						<Image
							src="/sidebar-logo.png"
							alt="FunnelFlow Logo"
							width={32}
							height={32}
							style={{
								objectFit: "contain",
							}}
						/>
					</div>
				</div>

				{/* Center: Page Title */}
				<div className="flex items-center gap-2">
					<Badge size="2" color="green">
						Generated
					</Badge>
					<Text size="2" weight="medium" color="gray">
						{funnel.title}
					</Text>
				</div>

				{/* Right: Actions */}
				<div className="flex items-center gap-2">
					<Button size="2" variant="ghost" onClick={onCreateNew}>
						<span className="flex items-center gap-2">
							<svg
								width="16"
								height="16"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								role="img"
								aria-label="Plus icon"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									d="M12 4v16m8-8H4"
								/>
							</svg>
							Create New
						</span>
					</Button>
					<Button size="2" variant="solid" onClick={onEdit} style={{ background: "var(--accent-9)" }}>
						<span className="flex items-center gap-2">
							<svg
								width="16"
								height="16"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								role="img"
								aria-label="Edit icon"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
								/>
							</svg>
							Edit Page
						</span>
					</Button>
				</div>
			</div>

			{/* Preview Content */}
			<div
				style={{
					flex: 1,
					marginTop: "56px", // Account for fixed top bar
					overflow: "auto",
					display: "flex",
					justifyContent: "center",
					alignItems: "flex-start",
					padding: "20px",
					background: "var(--gray-2)",
				}}
			>
				{/* Generated Site Preview */}
				<div
					style={{
						width: "100%",
						maxWidth: "800px",
						background: "white",
						borderRadius: "12px",
						boxShadow: "0 8px 32px rgba(0, 0, 0, 0.08)",
						overflow: "hidden",
					}}
				>
					{/* Preview Header */}
					<div
						style={{
							padding: "16px 24px",
							borderBottom: "1px solid var(--gray-6)",
							background: "var(--gray-1)",
							display: "flex",
							alignItems: "center",
							justifyContent: "space-between",
						}}
					>
						<div>
							<Text size="2" weight="medium">Generated Site Preview</Text>
							<Text size="1" color="gray">{funnel.title}</Text>
						</div>
						<Badge size="1" color="blue">
							{funnel.pages.length} page{funnel.pages.length !== 1 ? "s" : ""}
						</Badge>
					</div>

					{/* Site Content - Render actual funnel as it appears in editor */}
					<div style={{
						padding: "0",
						background: "white",
						minHeight: "400px"
					}}>
						{/* Render the first page as the main preview */}
						{funnel.pages && funnel.pages.length > 0 && funnel.pages[0].sections ? (
							<div style={{
								width: "100%",
								background: "white"
							}}>
								{funnel.pages[0].sections.map((section) => {
									// Convert section to element format for rendering
									const element = {
										id: section.id,
										type: section.type,
										content: section.content || section.heading,
										properties: {
											...section.properties,
											textColor: section.properties?.textColor || funnel.theme?.textColor || "#000000",
											backgroundColor: section.properties?.backgroundColor || funnel.theme?.backgroundColor || "#ffffff"
										}
									};

									// Render based on section type
									switch (section.type) {
										case "hero":
											return (
												<div key={section.id} style={{
													padding: "80px 40px",
													textAlign: "center",
													background: element.properties.backgroundColor || "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
													color: element.properties.textColor || "white",
													minHeight: "500px",
													display: "flex",
													flexDirection: "column",
													justifyContent: "center",
													alignItems: "center"
												}}>
													<Heading size="8" style={{
														marginBottom: "16px",
														color: "inherit",
														maxWidth: "600px"
													}}>
														{section.heading || "Hero Section"}
													</Heading>
													<Text size="4" style={{
														marginBottom: "32px",
														opacity: 0.9,
														maxWidth: "500px",
														lineHeight: "1.6"
													}}>
														{typeof section.content === 'string' ? section.content : (section.content as any)?.description || "Your compelling hero message goes here"}
													</Text>
													{section.cta && (
														<button style={{
															background: "#ffffff",
															color: "#667eea",
															border: "none",
															borderRadius: "8px",
															padding: "16px 32px",
															fontSize: "16px",
															fontWeight: "600",
															cursor: "pointer",
															boxShadow: "0 4px 12px rgba(0,0,0,0.15)"
														}}>
															{section.cta.text || "Get Started"}
														</button>
													)}
												</div>
											);

										case "features":
											return (
												<div key={section.id} style={{
													padding: "80px 40px",
													background: element.properties.backgroundColor || "#f8fafc",
													color: element.properties.textColor || "#1f2937"
												}}>
													<div style={{ textAlign: "center", marginBottom: "48px" }}>
														<Heading size="6" style={{ marginBottom: "16px" }}>
															{section.heading || "Features"}
														</Heading>
														<Text size="3" style={{ opacity: 0.8, maxWidth: "600px", margin: "0 auto" }}>
															{typeof section.content === 'string' ? section.content : (section.content as any)?.description || "Discover what makes us different"}
														</Text>
													</div>
													<div style={{
														display: "grid",
														gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
														gap: "24px",
														maxWidth: "800px",
														margin: "0 auto"
													}}>
														{section.items?.slice(0, 3).map((item, index) => (
															<div key={index} style={{
																background: "white",
																padding: "24px",
																borderRadius: "8px",
																boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
																textAlign: "center"
															}}>
																<Text size="2" weight="bold" style={{ marginBottom: "8px", display: "block" }}>
																	{item.title}
																</Text>
																<Text size="1" style={{ opacity: 0.8 }}>
																	{item.description}
																</Text>
															</div>
														)) || (
															<div style={{ gridColumn: "1 / -1", textAlign: "center", padding: "40px" }}>
																<Text size="2" color="gray">Features will appear here</Text>
															</div>
														)}
													</div>
												</div>
											);

										case "pricing":
											return (
												<div key={section.id} style={{
													padding: "80px 40px",
													background: element.properties.backgroundColor || "white",
													color: element.properties.textColor || "#1f2937"
												}}>
													<div style={{ textAlign: "center", marginBottom: "48px" }}>
														<Heading size="6" style={{ marginBottom: "16px" }}>
															{section.heading || "Pricing"}
														</Heading>
														<Text size="3" style={{ opacity: 0.8 }}>
															{typeof section.content === 'string' ? section.content : (section.content as any)?.description || "Choose the perfect plan for you"}
														</Text>
													</div>
													<div style={{
														maxWidth: "400px",
														margin: "0 auto",
														background: "#f8fafc",
														padding: "32px",
														borderRadius: "12px",
														textAlign: "center",
														border: "2px solid #667eea"
													}}>
														<Text size="1" weight="bold" style={{ color: "#667eea", marginBottom: "8px", display: "block" }}>
															BEST VALUE
														</Text>
														<Heading size="7" style={{ marginBottom: "8px" }}>
															$97
														</Heading>
														<Text size="2" style={{ opacity: 0.8, marginBottom: "24px", display: "block" }}>
															One-time payment
														</Text>
														<button style={{
															background: "#667eea",
															color: "white",
															border: "none",
															borderRadius: "8px",
															padding: "16px 32px",
															fontSize: "16px",
															fontWeight: "600",
															cursor: "pointer",
															width: "100%"
														}}>
															Get Started Now
														</button>
													</div>
												</div>
											);

										case "testimonials":
											return (
												<div key={section.id} style={{
													padding: "80px 40px",
													background: element.properties.backgroundColor || "#f8fafc",
													color: element.properties.textColor || "#1f2937"
												}}>
													<div style={{ textAlign: "center", marginBottom: "48px" }}>
														<Heading size="6" style={{ marginBottom: "16px" }}>
															{section.heading || "What Our Customers Say"}
														</Heading>
													</div>
													<div style={{
														maxWidth: "600px",
														margin: "0 auto",
														background: "white",
														padding: "32px",
														borderRadius: "12px",
														boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
														textAlign: "center"
													}}>
														<Text size="3" style={{
															fontStyle: "italic",
															marginBottom: "24px",
															display: "block",
															lineHeight: "1.6"
														}}>
															"{typeof section.content === 'string' ? section.content : (section.content as any)?.description || 'This product completely transformed my business. Highly recommended!'}"
														</Text>
														<Text size="2" weight="bold">
															— Sarah Johnson
														</Text>
													</div>
												</div>
											);

										default:
											return (
												<div key={section.id} style={{
													padding: "40px",
													background: element.properties.backgroundColor || "white",
													color: element.properties.textColor || "#1f2937",
													textAlign: "center"
												}}>
													<Badge size="1" color="blue" style={{ marginBottom: "16px" }}>
														{section.type}
													</Badge>
													<Heading size="4" style={{ marginBottom: "16px" }}>
														{section.heading || `${section.type.charAt(0).toUpperCase() + section.type.slice(1)} Section`}
													</Heading>
													<Text size="2" style={{ lineHeight: "1.6", maxWidth: "600px", margin: "0 auto" }}>
														{typeof section.content === 'string' ? section.content : (section.content as any)?.description || "Content will appear here"}
													</Text>
												</div>
											);
									}
								})}
							</div>
						) : (
							<div style={{
								padding: "80px 40px",
								textAlign: "center",
								background: "var(--gray-1)",
								minHeight: "400px",
								display: "flex",
								flexDirection: "column",
								justifyContent: "center",
								alignItems: "center"
							}}>
								<svg
									width="48"
									height="48"
									viewBox="0 0 24 24"
									fill="none"
									stroke="var(--gray-8)"
									strokeWidth="1.5"
									style={{ margin: "0 auto 16px" }}
								>
									<rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
									<circle cx="8.5" cy="8.5" r="1.5" />
									<polyline points="21,15 16,10 5,21" />
								</svg>
								<Text size="2" color="gray">
									Generated content will appear here
								</Text>
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
