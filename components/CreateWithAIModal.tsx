"use client";

import type {
	FunnelSchema,
	GenerateFunnelRequest,
	GenerateFunnelResponse,
} from "@/lib/types/funnel";
import { Button, Callout, Dialog, Text, TextArea } from "frosted-ui";
import { useState } from "react";

interface CreateWithAIModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSuccess: (funnel: FunnelSchema) => void;
	userId: string;
	companyId: string;
}

export function CreateWithAIModal({
	isOpen,
	onClose,
	onSuccess,
	userId,
	companyId,
}: CreateWithAIModalProps) {
	const [prompt, setPrompt] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleGenerate = async () => {
		if (!prompt.trim()) {
			setError("Please enter a prompt");
			return;
		}

		setIsLoading(true);
		setError(null);

		try {
			const requestBody: GenerateFunnelRequest = {
				prompt,
				userId,
				companyId,
			};

			const response = await fetch("/api/generate-funnel", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(requestBody),
			});

			const data: GenerateFunnelResponse = await response.json();

			if (!response.ok || !data.success) {
				throw new Error(data.error || "Failed to generate funnel");
			}

			if (data.funnel) {
				onSuccess(data.funnel);
				setPrompt("");
				onClose();
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : "An error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
			handleGenerate();
		}
	};

	return (
		<Dialog.Root
			open={isOpen}
			onOpenChange={(open: boolean) => !open && onClose()}
		>
			<Dialog.Content style={{ maxWidth: "640px", padding: "32px" }}>
				<div className="flex items-center gap-3 mb-2">
					<div
						style={{
							width: "40px",
							height: "40px",
							borderRadius: "10px",
							background: "linear-gradient(135deg, var(--accent-5), var(--accent-4))",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							boxShadow: "0 4px 12px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
						}}
					>
						<svg
							width="20"
							height="20"
							viewBox="0 0 24 24"
							fill="none"
							stroke="var(--accent-11)"
							strokeWidth="2"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								d="M13 10V3L4 14h7v7l9-11h-7z"
							/>
						</svg>
					</div>
					<Dialog.Title style={{ marginBottom: 0 }}>Create with AI</Dialog.Title>
				</div>
				<Dialog.Description style={{ marginTop: "8px" }}>
					Describe your funnel and let AI build it for you in seconds
				</Dialog.Description>

				<div className="flex flex-col gap-4 mt-4">
					<div>
						<Text
							as="label"
							size="2"
							weight="medium"
							style={{ display: "block", marginBottom: "8px" }}
						>
							What kind of funnel do you want to create?
						</Text>
						<TextArea
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
							onKeyDown={handleKeyDown}
							placeholder="E.g., Create a sales funnel for my fitness coaching program. Include a hero section with my value proposition, a features section highlighting the benefits, pricing tiers, and a strong call-to-action."
							disabled={isLoading}
							rows={6}
							style={{ width: "100%" }}
						/>
						<Text
							size="1"
							color="gray"
							style={{ display: "block", marginTop: "4px" }}
						>
							Tip: Press Cmd/Ctrl + Enter to generate
						</Text>
					</div>

					{error && (
						<Callout.Root
							color="red"
							size="2"
							style={{
								padding: "12px 16px",
								borderRadius: "6px",
								border: "1px solid var(--red-6)",
								background: "var(--red-2)",
							}}
						>
							<Callout.Icon>
								<svg
									width="18"
									height="18"
									viewBox="0 0 20 20"
									fill="currentColor"
									role="img"
									aria-label="Error icon"
								>
									<path
										fillRule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
										clipRule="evenodd"
									/>
								</svg>
							</Callout.Icon>
							<Callout.Text style={{ fontSize: "14px", fontWeight: "500" }}>
								{error}
							</Callout.Text>
						</Callout.Root>
					)}
				</div>

				<div className="flex gap-3 mt-4 justify-end">
					<Dialog.Close>
						<Button variant="soft" color="gray" disabled={isLoading}>
							Cancel
						</Button>
					</Dialog.Close>
					<Button
						onClick={handleGenerate}
						disabled={isLoading || !prompt.trim()}
						loading={isLoading}
					>
						{isLoading ? (
							"Generating..."
						) : (
							<span className="flex items-center gap-2">
								<svg
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									role="img"
									aria-label="Lightning bolt icon"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										d="M13 10V3L4 14h7v7l9-11h-7z"
									/>
								</svg>
								Generate Funnel
							</span>
						)}
					</Button>
				</div>

				<div
					className="mt-4 pt-4"
					style={{ borderTop: "1px solid var(--gray-6)" }}
				>
					<Text size="1" color="gray">
						Powered by Google Gemini AI
					</Text>
				</div>
			</Dialog.Content>
		</Dialog.Root>
	);
}
