"use client";

import React, { useState, useRef, useEffect } from "react";
import { HugeiconsIcon } from "@hugeicons/react";
import {
	TextBoldIcon,
	TextItalicIcon,
	TextUnderlineIcon,
	TextAlignLeftIcon,
	TextAlignCenterIcon,
	TextAlignRightIcon,
	SparklesIcon,
	Link01Icon,
	MoreHorizontalIcon,
	Delete01Icon
} from "@hugeicons/core-free-icons";

interface SmallElementTooltipProps {
	position: { x: number; y: number };
	visible: boolean;
	isBold?: boolean;
	isItalic?: boolean;
	isUnderline?: boolean;
	textAlign?: string;
	fontSize?: string;
	fontFamily?: string;
	textContent?: string;
	textColor?: string;
	onBold?: () => void;
	onItalic?: () => void;
	onUnderline?: () => void;
	onTextAlign?: (align: string) => void;
	onFontSize?: (size: string) => void;
	onFontFamily?: (family: string) => void;
	onTextContent?: (content: string) => void;
	onTextColor?: (color: string) => void;
	onAIEdit?: () => void;
	onLink?: () => void;
	onMore?: () => void;
	onDelete?: () => void;
	onClose?: () => void;
}

export function SmallElementTooltip({
	position,
	visible,
	isBold = false,
	isItalic = false,
	isUnderline = false,
	textAlign = "left",
	fontSize = "16px",
	fontFamily = "Inter",
	textContent = "",
	textColor = "#000000",
	onBold,
	onItalic,
	onUnderline,
	onTextAlign,
	onFontSize,
	onFontFamily,
	onTextContent,
	onTextColor,
	onAIEdit,
	onLink,
	onMore,
	onDelete,
	onClose,
}: SmallElementTooltipProps) {
	const tooltipRef = useRef<HTMLDivElement>(null);

	// Close tooltip when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (visible && tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
				// Don't close if clicking on dropdowns or form elements
				const target = event.target as HTMLElement;
				if (target.tagName === 'SELECT' || target.tagName === 'INPUT' || target.tagName === 'BUTTON') {
					return;
				}
				onClose?.();
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [visible, onClose]);

	if (!visible) return null;

	// Ensure tooltip stays within viewport
	const tooltipWidth = 600;
	const tooltipHeight = 80;
	const leftPosition = Math.max(10, Math.min(position.x - tooltipWidth / 2, window.innerWidth - tooltipWidth - 10));
	const topPosition = Math.max(10, position.y - tooltipHeight - 20); // Increased offset to appear higher

	const tooltipStyle: React.CSSProperties = {
		position: "fixed",
		left: leftPosition,
		top: topPosition,
		background: "white",
		border: "1px solid #e5e7eb",
		borderRadius: "8px",
		boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
		padding: "6px 8px",
		zIndex: 9999,
		display: "flex",
		alignItems: "center",
		gap: "6px",
		minWidth: "auto",
		fontSize: "12px",
		pointerEvents: "auto",
	};

	const buttonStyle: React.CSSProperties = {
		background: "transparent",
		border: "none",
		borderRadius: "3px",
		padding: "4px",
		cursor: "pointer",
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		transition: "all 0.2s ease",
		color: "#374151",
		minWidth: "24px",
		height: "24px",
	};

	const activeButtonStyle: React.CSSProperties = {
		...buttonStyle,
		background: "#3b82f6",
		color: "white",
	};

	const fonts = [
		"Inter",
		"Neue Haas Grotesk",
		"Satoshi",
		"General Sans",
		"Manrope",
		"Everett",
		"Degular",
		"Messina Sans"
	];

	const fontSizes = ["12px", "14px", "16px", "18px", "20px", "24px", "28px", "32px", "36px", "48px", "64px"];

	return (
		<div ref={tooltipRef} style={tooltipStyle}>
			{/* AI Edit Button */}
			<button
				onClick={onAIEdit}
				style={{
					background: "white",
					color: "#3b82f6",
					border: "1px solid #3b82f6",
					borderRadius: "4px",
					padding: "4px 8px",
					cursor: "pointer",
					display: "flex",
					alignItems: "center",
					gap: "4px",
					fontSize: "11px",
					fontWeight: "500",
					height: "28px",
					whiteSpace: "nowrap",
					boxShadow: "inset 0 1px 0 rgba(59, 130, 246, 0.1)",
					textTransform: "uppercase",
					letterSpacing: "0.5px",
				}}
			>
				<HugeiconsIcon icon={SparklesIcon} size={12} />
				AI EDIT
			</button>

			{/* Divider */}
			<div style={{ width: "1px", height: "20px", background: "#e5e7eb" }} />

			{/* Font Family */}
			<select
				value={fontFamily}
				onChange={(e) => onFontFamily?.(e.target.value)}
				style={{
					padding: "4px 8px",
					border: "1px solid #d1d5db",
					borderRadius: "4px",
					fontSize: "11px",
					background: "white",
					minWidth: "100px",
					height: "28px",
				}}
			>
				{fonts.map(font => (
					<option key={font} value={font}>{font}</option>
				))}
			</select>

			{/* Font Size */}
			<select
				value={fontSize}
				onChange={(e) => onFontSize?.(e.target.value)}
				style={{
					padding: "4px 8px",
					border: "1px solid #d1d5db",
					borderRadius: "4px",
					fontSize: "11px",
					background: "white",
					minWidth: "60px",
					height: "28px",
				}}
			>
				{fontSizes.map(size => (
					<option key={size} value={size}>{size}</option>
				))}
			</select>

			{/* Text Color */}
			<input
				type="color"
				value={textColor}
				onChange={(e) => onTextColor?.(e.target.value)}
				style={{
					width: "28px",
					height: "28px",
					border: "1px solid #d1d5db",
					borderRadius: "4px",
					cursor: "pointer",
					background: "white",
				}}
			/>

			{/* Divider */}
			<div style={{ width: "1px", height: "20px", background: "#e5e7eb" }} />

			{/* Text Formatting */}
			<button
				onClick={onBold}
				style={isBold ? activeButtonStyle : buttonStyle}
			>
				<HugeiconsIcon icon={TextBoldIcon} size={14} />
			</button>

			<button
				onClick={onItalic}
				style={isItalic ? activeButtonStyle : buttonStyle}
			>
				<HugeiconsIcon icon={TextItalicIcon} size={14} />
			</button>

			<button
				onClick={onUnderline}
				style={isUnderline ? activeButtonStyle : buttonStyle}
			>
				<HugeiconsIcon icon={TextUnderlineIcon} size={14} />
			</button>

			{/* Divider */}
			<div style={{ width: "1px", height: "20px", background: "#e5e7eb" }} />

			{/* More Options */}
			<button
				onClick={onMore}
				style={buttonStyle}
			>
				<HugeiconsIcon icon={MoreHorizontalIcon} size={14} />
			</button>
		</div>
	);
}
