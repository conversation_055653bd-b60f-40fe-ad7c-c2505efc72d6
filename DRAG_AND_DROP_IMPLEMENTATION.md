# Drag and Drop Funnel Editor Implementation

## Overview

This implementation adds a comprehensive drag-and-drop editor to FunnelFlow AI, allowing users to visually edit their generated funnels using 50 different component types.

## Features

### 🎨 Visual Editor
- **Drag and Drop Interface**: Intuitive component placement using `@dnd-kit`
- **Live Preview**: Real-time rendering of components as they're added
- **Multi-Page Support**: Edit multiple funnel pages with tab navigation
- **Component Reordering**: Sortable components with smooth animations

### 🧱 50 Component Types

#### Layout / Container Components (6)
1. Section - Full-width container for grouping content
2. Row - Multi-column layout container
3. Column - Individual column within a row
4. Flex Container - Flexible layout with direction control
5. Popup (Modal) - Modal overlay with triggers
6. Universal Block - Generic container with custom styling

#### Content & Functional Elements (16)
7. Headline - H1-H6 headings with alignment
8. Sub-Headline - Secondary headings
9. Paragraph / Text Block - Rich text content
10. Image - Responsive images with object-fit
11. Video - HTML5 video player with controls
12. Audio Player - Audio playback component
13. Button - Customizable CTA buttons
14. Divider / Line Break - Section separators
15. Icon - Icon display component
16. Countdown Timer - Dynamic countdown with format options
17. Progress Bar - Visual progress indicator
18. Spacer - Empty space for layout control
19. Social Share - Social media sharing buttons
20. Custom HTML / JavaScript - Embed custom code
21. Menu / Navigation - Horizontal/vertical navigation
22. Link List - List of clickable links

#### Form & Input Elements (8)
23. Input Field - Text, email, tel, number inputs
24. Text Area - Multi-line text input
25. Select Box (Dropdown) - Dropdown selection
26. Radio Buttons - Single selection from options
27. Checkbox - Boolean toggle
28. Submit Button - Form submission button
29. Hidden Field - Hidden form data
30. Search Bar - Search input with button

#### Order & Payment Elements (7)
31. Product Selector - Product selection grid/list
32. Credit Card Form - Payment card input
33. Order Bump - Upsell offer checkbox
34. Order Summary - Cart summary display
35. 2-Step Order Form - Multi-step checkout
36. Cart Total / Billing Info - Price breakdown
37. Shipping Info - Shipping address form

#### Webinar & Automation Elements (5)
38. Auto Webinar Date - Dynamic webinar date
39. Auto Webinar Time - Dynamic webinar time
40. Local Time Display - User's local time
41. Webinar Countdown - Countdown to webinar
42. SMS Sign-Up - Phone number collection

#### Content Enhancements (8)
43. Bullet List - Styled list with icons
44. FAQ Element (Accordion) - Expandable Q&A
45. Testimonial - Customer testimonial card
46. Image Slider / Carousel - Image gallery
47. Custom Form - Custom form container
48. Map - Embedded map component
49. Table - Data table with styling
50. Embed Code - External embed support

## Architecture

### File Structure
```
/workspace
├── lib/
│   ├── types/
│   │   └── editor.ts           # TypeScript types for all components
│   └── editor/
│       ├── component-definitions.ts  # Component metadata and defaults
│       └── index.ts
├── components/
│   ├── editor/
│   │   ├── FunnelEditor.tsx         # Main editor component
│   │   ├── ComponentPalette.tsx     # Component selection sidebar
│   │   ├── ComponentRenderer.tsx    # Component display logic
│   │   ├── PropertyPanel.tsx        # Property editing sidebar
│   │   └── index.ts
│   └── FunnelBuilder.tsx            # Updated with editor integration
```

### Component Architecture

#### FunnelEditor
- Main orchestrator component
- Manages editor state (pages, selected component)
- Handles drag-and-drop context
- Provides save/close functionality

#### ComponentPalette
- Left sidebar with component library
- Categorized components (6 categories)
- Search functionality
- Drag source for new components

#### ComponentRenderer
- Renders components based on type
- Displays component properties visually
- Shows selection state
- Provides click-to-select

#### PropertyPanel
- Right sidebar for editing
- Dynamic property inputs based on component type
- Type-specific inputs (text, number, boolean, etc.)
- Delete component functionality

### Data Flow

1. **Component Creation**
   ```
   User drags/clicks → createDefaultComponent() → Add to page.components
   ```

2. **Component Editing**
   ```
   Select component → Edit in PropertyPanel → Update component.properties
   ```

3. **Component Reordering**
   ```
   Drag component → DndContext handles move → arrayMove() updates order
   ```

4. **Saving**
   ```
   FunnelEditor → Convert to FunnelSchema → onSave callback → Update parent
   ```

## Usage

### Opening the Editor

After generating a funnel, click the "Edit with Drag & Drop" button in the FunnelBuilder header:

```tsx
<FunnelBuilder
  funnel={funnel}
  onUpdateFunnel={(updated) => setFunnel(updated)}
  onSaveDraft={() => saveDraft()}
  onPublish={() => publish()}
/>
```

### Adding Components

1. **Method 1: Drag and Drop**
   - Drag component from palette
   - Drop onto canvas

2. **Method 2: Click**
   - Click component in palette
   - Automatically added to current page

### Editing Components

1. Click component on canvas to select
2. Edit properties in right panel
3. Changes apply immediately
4. Delete via "Delete" button

### Managing Pages

- Switch pages using tabs at top
- Each page maintains its own components
- Components are page-specific

### Saving Changes

- **Save Changes**: Save without closing
- **Save & Close**: Save and return to FunnelBuilder

## Technical Details

### Dependencies

```json
{
  "@dnd-kit/core": "^6.3.1",
  "@dnd-kit/sortable": "^10.0.0",
  "@dnd-kit/utilities": "^3.2.2"
}
```

### Component Type System

Each component has:
- `id`: Unique identifier
- `type`: ComponentType enum
- `name`: Display name
- `properties`: Type-specific properties object
- `children`: (Optional) Array of child components

### Container Components

These components support nesting:
- Section
- Row
- Column
- Flex Container
- Popup
- Universal Block
- Two-Step Order
- Custom Form

### Property Types

Properties support multiple types:
- **String**: Text inputs, URLs, colors
- **Number**: Dimensions, counts, values
- **Boolean**: Toggles, flags
- **Array**: Lists of items, options
- **Object**: Complex nested properties

## Styling

The editor uses Frosted UI design system:
- Consistent with FunnelFlow AI's existing UI
- Glassmorphism effects
- Responsive design
- Accessibility-friendly

## Future Enhancements

Potential improvements:
1. **Undo/Redo**: History management
2. **Templates**: Pre-built component groups
3. **Preview Mode**: Full-screen preview
4. **Responsive Breakpoints**: Mobile/tablet views
5. **Copy/Paste**: Duplicate components
6. **Keyboard Shortcuts**: Power user features
7. **Component Library**: Custom components
8. **Export**: HTML/React export
9. **Collaboration**: Multi-user editing
10. **Version History**: Component snapshots

## Testing

To test the implementation:

1. Generate a funnel using the AI
2. Click "Edit with Drag & Drop"
3. Try adding different component types
4. Edit component properties
5. Reorder components via drag
6. Switch between pages
7. Save changes and verify persistence

## Performance Considerations

- **Lazy Loading**: Components render on-demand
- **Memoization**: Prevent unnecessary re-renders
- **Virtual Scrolling**: For large component lists (future)
- **Debounced Updates**: Property changes batched

## Browser Support

- Chrome/Edge 90+
- Firefox 88+
- Safari 14+
- Mobile browsers with touch support

## Accessibility

- Keyboard navigation support
- ARIA labels on interactive elements
- Focus management
- Screen reader compatible

## Contributing

When adding new components:

1. Add type to `ComponentType` in `editor.ts`
2. Create interface in `editor.ts`
3. Add definition in `component-definitions.ts`
4. Add renderer case in `ComponentRenderer.tsx`
5. Update this documentation

## License

Part of FunnelFlow AI project.
