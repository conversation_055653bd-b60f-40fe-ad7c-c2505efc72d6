# FunnelFlow AI

FunnelFlow AI is a comprehensive Whop-native funnel builder and automation platform that allows creators to build, deploy, and optimize conversion funnels entirely within the Whop ecosystem. It uses AI (Google Gemini) for natural language prompt-based creation and features a modern, full-featured dashboard with analytics, templates, and automation capabilities.

**Key Features:**
- 🤖 AI-powered funnel generation
- 📚 Pre-built template library (5 proven templates)
- 📊 Analytics dashboard with AI insights
- 🧠 Automation builder with visual interface
- 💳 Whop-native checkout integration
- 👥 Member management system
- 🤝 Affiliate program support
- ⚙️ Comprehensive settings panel

Built with Next.js 15, TypeScript, and Frosted UI (Whop's design system).

To run this project: 

1. Install dependencies with: `pnpm i`

2. Create a Whop App on your [whop developer dashboard](https://whop.com/dashboard/developer/), then go to the "Hosting" section and:
	- Ensure the "Base URL" is set to the domain you intend to deploy the site on.
	- Ensure the "App path" is set to `/experiences/[experienceId]`
	- Ensure the "Dashboard path" is set to `/dashboard/[companyId]` 
	- Ensure the "Discover path" is set to `/discover` 

3. Copy the environment variables from the `.env.development` into a `.env.local`. Ensure to use real values from the whop dashboard.

4. Go to a whop created in the same org as the app you created. Navigate to the tools section and add your app.

5. Run `pnpm dev` to start the dev server. Then in the top right of the window find a translucent settings icon. Select "localhost". The default port 3000 should work.

## Deploying

1. Upload your fork / copy of this template to github. 

2. Go to [Vercel](https://vercel.com/new) and link the repository. Deploy your application with the environment variables from your `.env.local`

3. If necessary update you "Base Domain" and webhook callback urls on the app settings page on the whop dashboard.

## Troubleshooting

**App not loading properly?** Make sure to set the "App path" in your Whop developer dashboard. The placeholder text in the UI does not mean it's set - you must explicitly enter `/experiences/[experienceId]` (or your chosen path name)
a

**Make sure to add env.local** Make sure to get the real app environment vairables from your whop dashboard and set them in .env.local


## Features Overview

### Dashboard with Sidebar Navigation
- **8 Main Sections**: Funnels, Templates, Automations, Products, Members, Affiliates, Analytics, Settings
- **Modern UI**: Frosted glass design matching Whop's aesthetic
- **Responsive Layout**: Fixed sidebar with scrollable content areas

### AI-Powered Funnel Generation
- Natural language prompt-based funnel creation
- Multi-page funnel support (Landing, Upsell, Downsell, Thank-You, Checkout)
- Intelligent section generation (Hero, Features, Pricing, Testimonials, CTA, FAQ)
- Rate-limited API (10 requests/hour per user)

### Template Library
- 5 pre-built professional templates:
  - Lead Capture Funnel
  - Webinar Registration Funnel
  - Digital Product Sales Funnel
  - Waitlist / Launch Page
  - Membership Funnel
- Category filtering and search
- One-click template usage

### Analytics Dashboard
- Key metrics: Visits, Conversions, Revenue, AOV, MRR
- AI-powered insights with actionable suggestions
- Funnel performance comparison table
- Date range filtering

### Funnel Management
- Grid/List view toggle
- Status filtering (All, Drafts, Published)
- Quick actions (Edit, Delete)
- Auto-save drafts to localStorage
- Performance metrics per funnel

### Automation Builder
- Visual automation sequence display
- Trigger and action configuration
- Active/Paused status management
- AI assistant for automation creation

### Whop Integration
- Product connection interface
- Checkout page configuration
- Member management
- Affiliate program support

## Documentation

- **[IMPLEMENTATION.md](./IMPLEMENTATION.md)**: Technical implementation details
- **[FEATURE_SUMMARY.md](./FEATURE_SUMMARY.md)**: Comprehensive feature overview and alignment with SPARK-290 specification

## Screenshots

The application features:
- Modern frosted glass UI design
- Color-coded funnel page types
- Interactive navigation with hover effects
- Real-time success notifications
- Empty states with helpful CTAs

## Project Structure

```
/workspace
├── app/
│   ├── api/generate-funnel/        # AI generation endpoint
│   └── dashboard/[companyId]/      # Main dashboard
├── components/
│   ├── DashboardLayout.tsx         # Layout with sidebar
│   ├── DashboardContent.tsx        # State management
│   ├── FunnelBuilder.tsx           # Funnel editor
│   ├── CreateWithAIModal.tsx       # AI modal
│   └── sections/                   # Section components
│       ├── FunnelsSection.tsx
│       ├── TemplatesSection.tsx
│       ├── AnalyticsSection.tsx
│       ├── AutomationsSection.tsx
│       ├── ProductsSection.tsx
│       ├── MembersSection.tsx
│       ├── AffiliatesSection.tsx
│       └── SettingsSection.tsx
└── lib/
    ├── types/funnel.ts             # TypeScript types
    └── utils/rate-limiter.ts       # Rate limiting
```

## Environment Variables

Required environment variables (see `.env.development`):

```bash
# Whop Configuration
WHOP_API_KEY="your_whop_api_key"
WHOP_WEBHOOK_SECRET="your_webhook_secret"
NEXT_PUBLIC_WHOP_AGENT_USER_ID="your_agent_user_id"
NEXT_PUBLIC_WHOP_APP_ID="your_app_id"
NEXT_PUBLIC_WHOP_COMPANY_ID="your_company_id"

# AI Configuration
GEMINI_API_KEY="your_gemini_api_key"
```

Get your Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey).

## Usage

### Creating a Funnel with AI
1. Click "Create with AI" button in the dashboard
2. Describe your funnel in natural language
3. Press Cmd/Ctrl + Enter or click "Generate Funnel"
4. Edit and customize the generated funnel
5. Save as draft or publish

### Using Templates
1. Navigate to Templates section in sidebar
2. Browse or search for templates
3. Click "Use Template" on desired template
4. Customize the template to your needs
5. Save and publish

### Viewing Analytics
1. Navigate to Analytics section
2. Select date range
3. Review key metrics and AI insights
4. Check funnel-specific performance

## Future Enhancements

See [FEATURE_SUMMARY.md](./FEATURE_SUMMARY.md) for detailed roadmap, including:
- Database persistence
- Drag-and-drop visual editor
- Real-time analytics integration
- Advanced automation engine
- A/B testing framework
- Custom domain publishing
- Team collaboration features

## Support & Resources

For more info, see our docs at https://dev.whop.com/introduction
