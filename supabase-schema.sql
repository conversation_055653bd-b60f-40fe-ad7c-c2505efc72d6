-- FunnelFlow AI - Supabase Database Schema
-- This file contains all the SQL commands needed to set up your Supabase database

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- For gen_random_bytes used in verification_token
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table (to track app users from Whop)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    whop_user_id TEXT UNIQUE NOT NULL,
    whop_company_id TEXT NOT NULL,
    user_name TEXT,
    username TEXT,
    company_title TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Funnels table (main funnel data)
CREATE TABLE IF NOT EXISTS funnels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    whop_company_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    -- Slug for first-party hosting (e.g., /f/[slug])
    slug TEXT UNIQUE,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    
    -- Funnel structure (stored as JSONB for flexibility)
    pages JSONB NOT NULL DEFAULT '[]',
    sections JSONB NOT NULL DEFAULT '[]',
    
    -- Theme settings
    theme JSONB NOT NULL DEFAULT '{
        "primaryColor": "#667eea",
        "backgroundColor": "#ffffff",
        "textColor": "#000000"
    }',
    
    -- Layout type
    layout TEXT NOT NULL DEFAULT 'single-page' CHECK (layout IN ('single-page', 'multi-step')),
    
    -- Analytics/Stats
    visits INTEGER DEFAULT 0,
    conversions INTEGER DEFAULT 0,
    revenue DECIMAL(10, 2) DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE
);

-- Feature Templates table (for saved feature section templates)
CREATE TABLE IF NOT EXISTS feature_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    whop_company_id TEXT NOT NULL,
    name TEXT NOT NULL,
    properties JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_whop_user_id ON users(whop_user_id);
CREATE INDEX IF NOT EXISTS idx_users_whop_company_id ON users(whop_company_id);
CREATE INDEX IF NOT EXISTS idx_funnels_user_id ON funnels(user_id);
CREATE INDEX IF NOT EXISTS idx_funnels_whop_company_id ON funnels(whop_company_id);
CREATE INDEX IF NOT EXISTS idx_funnels_status ON funnels(status);
CREATE INDEX IF NOT EXISTS idx_funnels_created_at ON funnels(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_feature_templates_user_id ON feature_templates(user_id);
CREATE INDEX IF NOT EXISTS idx_feature_templates_whop_company_id ON feature_templates(whop_company_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers to auto-update updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_funnels_updated_at BEFORE UPDATE ON funnels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_feature_templates_updated_at BEFORE UPDATE ON feature_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE funnels ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_templates ENABLE ROW LEVEL SECURITY;

-- Allow public read access to users (for now)
CREATE POLICY "Allow public read access to users" ON users
    FOR SELECT USING (true);

-- Allow users to insert their own data
CREATE POLICY "Allow users to insert their own data" ON users
    FOR INSERT WITH CHECK (true);

-- Allow users to update their own data
CREATE POLICY "Allow users to update their own data" ON users
    FOR UPDATE USING (true);

-- Allow public read access to funnels (can be restricted later)
CREATE POLICY "Allow public read access to funnels" ON funnels
    FOR SELECT USING (true);

-- Allow users to create funnels
CREATE POLICY "Allow users to create funnels" ON funnels
    FOR INSERT WITH CHECK (true);

-- Allow users to update their own funnels
CREATE POLICY "Allow users to update their own funnels" ON funnels
    FOR UPDATE USING (true);

-- Allow users to delete their own funnels
CREATE POLICY "Allow users to delete their own funnels" ON funnels
    FOR DELETE USING (true);

-- Allow public read access to feature templates
CREATE POLICY "Allow public read access to feature_templates" ON feature_templates
    FOR SELECT USING (true);

-- Allow users to create templates
CREATE POLICY "Allow users to create templates" ON feature_templates
    FOR INSERT WITH CHECK (true);

-- Allow users to update their own templates
CREATE POLICY "Allow users to update their own templates" ON feature_templates
    FOR UPDATE USING (true);

-- Allow users to delete their own templates
CREATE POLICY "Allow users to delete their own templates" ON feature_templates
    FOR DELETE USING (true);

-- Custom domain support -----------------------------------------------------

-- Table to map custom hostnames to funnels
CREATE TABLE IF NOT EXISTS funnel_domains (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    funnel_id UUID REFERENCES funnels(id) ON DELETE CASCADE,
    whop_company_id TEXT NOT NULL,
    hostname TEXT UNIQUE NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'disabled')),
    verification_token TEXT NOT NULL DEFAULT encode(gen_random_bytes(16), 'hex'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for domains
CREATE INDEX IF NOT EXISTS idx_funnel_domains_funnel_id ON funnel_domains(funnel_id);
CREATE INDEX IF NOT EXISTS idx_funnel_domains_company ON funnel_domains(whop_company_id);
CREATE INDEX IF NOT EXISTS idx_funnel_domains_status ON funnel_domains(status);

-- Trigger for updated_at
CREATE TRIGGER update_funnel_domains_updated_at BEFORE UPDATE ON funnel_domains
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS for funnel_domains
ALTER TABLE funnel_domains ENABLE ROW LEVEL SECURITY;

-- Read access (publicly resolvable)
CREATE POLICY "Public read access to funnel_domains" ON funnel_domains
    FOR SELECT USING (true);

-- Insert/update/delete allowed (to be tightened later)
CREATE POLICY "Allow insert funnel_domains" ON funnel_domains
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow update funnel_domains" ON funnel_domains
    FOR UPDATE USING (true);

CREATE POLICY "Allow delete funnel_domains" ON funnel_domains
    FOR DELETE USING (true);

-- Sample query to verify setup
-- SELECT * FROM funnels WHERE whop_company_id = 'your-company-id' ORDER BY created_at DESC;
