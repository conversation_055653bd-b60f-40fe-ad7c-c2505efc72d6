# SPARK-347 Resolution: Enhanced Section Editing and Layer Management

## Issue Summary
Enable comprehensive editing of component sections (Hero, Features, Pricing, Testimonials, Form, Footer) in the content and design right sidebar, including their color, content, and sizing. Also implement layer reordering functionality in the layers tab.

## Changes Made

### 1. Enhanced RightSidebar (`/workspace/components/RightSidebar.tsx`)

#### New Section Properties
Added comprehensive property support for all section types:
- **Common Section Properties:**
  - `height`: Section height control (auto, px, vh)
  - `maxWidth`: Maximum width constraint
  - `borderRadius`: Rounded corners (0-50px)
  - `useGradient`: Toggle gradient backgrounds
  - `gradientStart` & `gradientEnd`: Gradient color controls
  - Standard padding, margins, colors

- **Hero Section:**
  - `title`: Main hero headline
  - `subtitle`: Supporting text
  - `ctaText` & `ctaUrl`: Primary call-to-action
  - `secondaryCtaText` & `secondaryCtaUrl`: Secondary CTA
  - `backgroundImage`: Hero background image
  - `overlay` & `overlayOpacity`: Image overlay controls

- **Features Section:**
  - Section heading editor
  - Feature count control
  - Individual feature editing support

- **Pricing Section:**
  - Section heading editor
  - Pricing plan management
  - Plan highlighting options

- **Testimonials Section:**
  - Section heading editor
  - Testimonial management (quote, author, role, avatar)

- **Form Section:**
  - Form heading editor
  - Submit button text customization
  - Form action URL configuration
  - Form field management

- **Footer Section:**
  - Copyright text editor
  - Footer link management

#### Design Tab Enhancements
- **Section-specific controls:**
  - Section height with visual examples
  - Max width configuration
  - Background color picker
  - Text color picker
  - Gradient background toggle with dual color pickers
  - Border radius slider (0-50px) with live preview
  - Comprehensive padding controls (top, right, bottom, left)

- **Element controls retained:**
  - Font size slider for headings/text (12-72px)
  - Text alignment buttons (left, center, right)
  - Button color customization

### 2. Enhanced LeftSidebar (`/workspace/components/LeftSidebar.tsx`)

#### Layer Reordering Functionality
Added complete drag-and-drop layer management:

**New Props:**
- `onReorderElements`: Callback for layer reordering

**Drag-and-Drop Features:**
- Visual drag indicators (horizontal lines icon)
- Layer number badges with bold styling
- Opacity change during drag (50%)
- Cursor change (grab → grabbing)
- Visual feedback on drag target
- Hidden element indicator (eye-slash icon)

**Layer Display Enhancements:**
- Instructional text: "Drag layers to reorder"
- Layer numbering (1, 2, 3, etc.)
- Element type badges
- Visibility status icons
- Hover states with accent colors

### 3. Updated NewFunnelEditor (`/workspace/components/NewFunnelEditor.tsx`)

#### New Functionality
Added `handleReorderElements` function:
```typescript
const handleReorderElements = useCallback((fromIndex: number, toIndex: number) => {
  const newElements = [...elements];
  const [movedElement] = newElements.splice(fromIndex, 1);
  newElements.splice(toIndex, 0, movedElement);
  setElements(newElements);
  addToHistory(newElements);
  setHasUnsavedChanges(true);
}, [elements, addToHistory]);
```

**Features:**
- Array manipulation for reordering
- History tracking (undo/redo support)
- Automatic unsaved changes flag
- Integration with LeftSidebar

## User Experience Improvements

### Content Tab
Users can now edit all section-specific content:
- ✅ Hero: title, subtitle, CTAs, background image
- ✅ Features: section heading
- ✅ Pricing: section heading  
- ✅ Testimonials: section heading
- ✅ Form: heading, button text, action URL
- ✅ Footer: copyright text

### Design Tab
Users have full control over section appearance:
- ✅ Section dimensions (height, max width)
- ✅ Colors (background, text, gradients)
- ✅ Spacing (padding on all sides)
- ✅ Border styling (border radius)
- ✅ Visual effects (gradients with start/end colors)

### Layers Tab
Users can now manage layer order:
- ✅ Drag and drop to reorder elements
- ✅ Visual feedback during drag operations
- ✅ Layer numbering for easy reference
- ✅ Visibility indicators
- ✅ Element type identification

## Technical Implementation

### Type Safety
All new properties properly typed in `ElementProperties` interface with optional fields to maintain backward compatibility.

### State Management
- Layer reordering integrates with existing undo/redo system
- Changes properly tracked for unsaved changes detection
- React state updates use functional updates for accuracy

### UI/UX Patterns
- Consistent with Whop/Frost UI design system
- Proper hover states and transitions
- Color pickers for all color properties
- Range sliders with value displays
- Clear labeling and helper text

## Testing Recommendations

1. **Section Editing:**
   - Create each section type (Hero, Features, Pricing, Testimonials, Form, Footer)
   - Verify all content fields update correctly
   - Test gradient backgrounds
   - Verify dimension controls work as expected

2. **Layer Reordering:**
   - Add multiple elements to canvas
   - Drag layers to different positions
   - Verify visual order updates
   - Test undo/redo with reordering
   - Confirm changes trigger unsaved state

3. **Visual Consistency:**
   - Check hover states on all interactive elements
   - Verify color pickers display correctly
   - Test responsive behavior of sidebar
   - Confirm icons render properly

## Files Modified
- `/workspace/components/RightSidebar.tsx` (enhanced with section editing)
- `/workspace/components/LeftSidebar.tsx` (added layer reordering)
- `/workspace/components/NewFunnelEditor.tsx` (integrated reordering handler)

## Breaking Changes
None. All changes are backward compatible with existing element structures.

## Future Enhancements
- Bulk layer operations (select multiple, move together)
- Layer visibility toggle from layers panel
- Layer locking to prevent accidental moves
- Layer grouping/nesting
- Advanced gradient controls (angle, multiple stops)
- Real-time preview updates as properties change
