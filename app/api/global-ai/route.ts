import OpenAI from "openai";
import { NextResponse } from "next/server";

// Initialize OpenAI
const openai = new OpenAI({
	apiKey: process.env.OPENAI_API_KEY || "",
});

interface GlobalAIRequest {
	prompt: string;
	elements: Array<{
		id: string;
		type: string;
		content: string;
		properties: Record<string, string | number | boolean | object>;
	}>;
}

interface GlobalAIResponse {
	success: boolean;
	changes?: Array<{
		elementId: string;
		changes: Record<string, string | number | boolean | object>;
	}>;
	error?: string;
}

export async function POST(request: Request): Promise<Response> {
	try {
		const body: GlobalAIRequest = await request.json();
		const { prompt, elements } = body;

		if (!prompt || !elements) {
			return NextResponse.json(
				{
					success: false,
					error: "Missing required fields: prompt or elements",
				} as GlobalAIResponse,
				{ status: 400 },
			);
		}

		// Check if API key is configured
		if (!process.env.OPENAI_API_KEY) {
			console.error("OPENAI_API_KEY is not configured");
			return NextResponse.json(
				{
					success: false,
					error: "AI service is not configured. Please contact support.",
				} as GlobalAIResponse,
				{ status: 500 },
			);
		}

		// Create system prompt for global editing
		const systemPrompt = `You are an expert web designer and developer. You need to interpret natural language requests for editing a funnel/landing page and return ONLY the specific changes requested.

CRITICAL RULES - FOLLOW THESE EXACTLY:
1. ONLY change what the user explicitly asks for - nothing else
2. NEVER delete, remove, or modify elements unless explicitly requested
3. If user asks to change "a card" or "the pricing", only change that specific element
4. If user asks to change "all buttons", then change all button elements
5. Be SURGICAL - only touch the exact properties mentioned
6. PRESERVE all existing content and styling unless specifically asked to change it
7. If user says "change the price to $99", only change the price text, keep everything else
8. If user says "make the button blue", only change the button color, keep the text and other properties

EXAMPLES:
- "change pricing for a card" → only change price text in ONE pricing element
- "make all buttons red" → only change backgroundColor of ALL button elements
- "update the headline to say 'Welcome'" → only change content of headline elements
- "make the hero section blue" → only change backgroundColor of hero section

Current funnel elements:
${JSON.stringify(elements, null, 2)}

User request: "${prompt}"

ANALYZE the request carefully:
1. What SPECIFIC element(s) is the user referring to?
2. What SPECIFIC property should be changed?
3. What should the new value be?

Return an array of changes, where each change has:
- "elementId": the ID of the element to change
- "changes": object with ONLY the specific properties to update

Available properties for different element types:

For ALL elements:
- "textColor": hex color (e.g., "#3b82f6")
- "backgroundColor": hex color
- "textAlign": "left" | "center" | "right"
- "fontSize": number or string (e.g., "16px", "24px")
- "fontWeight": "normal" | "bold" | "100" | "200" | "300" | "400" | "500" | "600" | "700" | "800" | "900"
- "fontStyle": "normal" | "italic"
- "textDecoration": "none" | "underline" | "line-through"
- "fontFamily": string (e.g., "Inter", "Arial", "Helvetica")
- "padding": {top, right, bottom, left} numbers
- "margin": {top, right, bottom, left} numbers
- "borderRadius": number
- "border": string (e.g., "1px solid #ccc")
- "borderColor": hex color
- "borderWidth": number
- "boxShadow": string (e.g., "0 2px 4px rgba(0,0,0,0.1)")
- "opacity": number (0-1)
- "visible": boolean
- "content": string (for text content changes)

For hero elements:
- "content": string (main hero title/header)
- "subtitle": string (hero subtext/description)
- "title": string (alternative for main title)
- "ctaText": string (call-to-action button text)
- "ctaUrl": string (call-to-action button URL)
- "textColor": hex color
- "backgroundColor": hex color
- "textAlign": "left" | "center" | "right"
- "fontSize": number
- "padding": {top, right, bottom, left} numbers

For pricing elements:
- "content": string (main pricing section title)
- "subtitle": string (pricing section description)
- "plans": array of plan objects with name, price, period, features, popular
- "planBackground": hex color
- "planBorderColor": hex color
- "planBorderRadius": number
- "popularPlanColor": hex color
- "priceColor": hex color
- "featureColor": hex color
- "buttonText": string (CTA button text)
- "buttonBackground": hex color
- "buttonTextColor": hex color
- "columns": number (1-4 for plan layout)

For testimonials elements:
- "content": string (main testimonials section title)
- "subtitle": string (testimonials section description)
- "testimonials": array of testimonial objects with quote, author, role, avatar, rating
- "testimonialBackground": hex color
- "testimonialBorderColor": hex color
- "testimonialBorderRadius": number
- "quoteColor": hex color
- "authorColor": hex color
- "roleColor": hex color
- "starColor": hex color
- "avatarBackground": hex color
- "columns": number (1-4 for testimonial layout)

For features elements:
- "columns": number (1-4)
- "featureBackground": hex color
- "featureBorderColor": hex color
- "featureBorderRadius": number
- "featureGap": number
- "iconBackground": hex color
- "iconSize": number
- "featureTitleColor": hex color
- "featureDescriptionColor": hex color
- "ctaBackground": hex color
- "ctaTextColor": hex color
- "features": array of feature objects
- "subheading": string
- "content": string (main heading)

For buttons:
- "backgroundColor": hex color
- "textColor": hex color
- "content": string (button text)

For headings/text:
- "content": string (the actual text content)
- "fontSize": number
- "textColor": hex color

CONTENT CHANGE EXAMPLES:
- "Change the top header to say 'Grow your business today'" → [{"elementId": "header1", "changes": {"content": "Grow your business today"}}]
- "Change the top hero to say 'Grow your business today'" → [{"elementId": "hero1", "changes": {"content": "Grow your business today"}}]
- "Change the hero subtext to 'Start your journey today'" → [{"elementId": "hero1", "changes": {"subtitle": "Start your journey today"}}]
- "Update the hero subtitle to 'Transform your business'" → [{"elementId": "hero1", "changes": {"subtitle": "Transform your business"}}]
- "Change the hero description to 'Join thousands of successful entrepreneurs'" → [{"elementId": "hero1", "changes": {"subtitle": "Join thousands of successful entrepreneurs"}}]
- "Update the main heading to 'Welcome to our platform'" → [{"elementId": "heading1", "changes": {"content": "Welcome to our platform"}}]
- "Change the hero section to say 'Build your empire'" → [{"elementId": "hero1", "changes": {"content": "Build your empire"}}]
- "Change the button text to 'Get Started Now'" → [{"elementId": "button1", "changes": {"content": "Get Started Now"}}]
- "Update the subheading to 'Everything you need to succeed'" → [{"elementId": "features1", "changes": {"subheading": "Everything you need to succeed"}}]
- "Change the pricing title to 'Choose Your Plan'" → [{"elementId": "pricing1", "changes": {"content": "Choose Your Plan"}}]
- "Update the pricing subtitle to 'Select the perfect plan for your needs'" → [{"elementId": "pricing1", "changes": {"subtitle": "Select the perfect plan for your needs"}}]
- "Change the pricing button text to 'Start Free Trial'" → [{"elementId": "pricing1", "changes": {"buttonText": "Start Free Trial"}}]
- "Make pricing plans blue" → [{"elementId": "pricing1", "changes": {"priceColor": "#3b82f6", "popularPlanColor": "#3b82f6"}}]
- "Change the testimonials title to 'Customer Reviews'" → [{"elementId": "testimonials1", "changes": {"content": "Customer Reviews"}}]
- "Update the testimonials subtitle to 'Hear from our happy customers'" → [{"elementId": "testimonials1", "changes": {"subtitle": "Hear from our happy customers"}}]
- "Make testimonials cards blue" → [{"elementId": "testimonials1", "changes": {"testimonialBackground": "#3b82f6", "testimonialBorderColor": "#3b82f6"}}]
- "Change testimonials to 2 columns" → [{"elementId": "testimonials1", "changes": {"columns": 2}}]

STYLING EXAMPLES:
- "Make all text blue" → [{"elementId": "element1", "changes": {"textColor": "#3b82f6"}}, {"elementId": "element2", "changes": {"textColor": "#3b82f6"}}]
- "Change to dark theme" → [{"elementId": "element1", "changes": {"backgroundColor": "#1f2937", "textColor": "#ffffff"}}]
- "Make all buttons rounded" → [{"elementId": "button1", "changes": {"borderRadius": 20}}]
- "Update all headings to be larger" → [{"elementId": "heading1", "changes": {"fontSize": 48}}]
- "Make the hero background gradient" → [{"elementId": "hero1", "changes": {"background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"}}]
- "Add shadow to all cards" → [{"elementId": "card1", "changes": {"boxShadow": "0 4px 6px rgba(0, 0, 0, 0.1)"}}]
- "Make all text bold" → [{"elementId": "text1", "changes": {"fontWeight": "bold"}}]
- "Change font to Arial" → [{"elementId": "text1", "changes": {"fontFamily": "Arial"}}]
- "Make buttons have a border" → [{"elementId": "button1", "changes": {"border": "2px solid #3b82f6", "borderColor": "#3b82f6"}}]
- "Make text italic" → [{"elementId": "text1", "changes": {"fontStyle": "italic"}}]
- "Add underline to links" → [{"elementId": "link1", "changes": {"textDecoration": "underline"}}]
- "Change opacity to 50%" → [{"elementId": "element1", "changes": {"opacity": 0.5}}]
- "Make background transparent" → [{"elementId": "element1", "changes": {"backgroundColor": "transparent"}}]
- "Add padding to all sections" → [{"elementId": "section1", "changes": {"padding": {"top": 20, "right": 20, "bottom": 20, "left": 20}}}]

ELEMENT IDENTIFICATION:
- "top header", "main header", "hero", "hero section", "top hero" → elements with type "hero"
- "main heading", "primary heading", "title" → elements with type "hero" or first heading element
- "hero subtext", "hero subtitle", "hero description" → subtitle property of hero elements
- "button", "cta", "call to action" → elements with type "cta" or button elements
- "features section", "features", "feature section" → elements with type "features"
- "pricing section", "pricing", "plans section" → elements with type "pricing"
- "pricing title", "pricing header" → content property of pricing elements
- "pricing subtitle", "pricing description" → subtitle property of pricing elements
- "pricing button", "pricing cta" → buttonText property of pricing elements
- "testimonials section", "testimonials", "reviews section" → elements with type "testimonials"
- "testimonials title", "testimonials header" → content property of testimonials elements
- "testimonials subtitle", "testimonials description" → subtitle property of testimonials elements
- "testimonials cards", "testimonial cards" → testimonialBackground and testimonialBorderColor properties
- "faq section", "faq" → elements with type "faq"
- "subheading", "subtitle", "description" → the subheading property of features elements
- "text", "paragraph", "body text" → text elements
- "image", "picture", "photo" → image elements

COMMON VARIATIONS:
- "hero" = elements with type "hero" (the main title/header)
- "cta" = elements with type "cta" (call to action)
- "title" = elements with type "hero" (main title)
- "subtitle" = "subheading" (secondary text)

AVAILABLE ELEMENT TYPES:
Look for these exact types in the current elements: "hero", "features", "pricing", "testimonials", "cta", "faq"

IMPORTANT: 
- Only include elements that actually need changes
- If no changes are needed, return an empty array []
- When modifying features array, preserve all existing features and only change specific ones
- Be specific about which elements to change based on the user's request
- For content changes, always use the "content" property
- If you can't find a specific element (like "hero"), try to find the first heading element instead
- Look at the element types in the current funnel elements to match the request

ELEMENT MATCHING STRATEGY:
1. First, try to find an exact match (e.g., "hero" element)
2. If not found, look for similar elements (e.g., "heading" for "hero")
3. If still not found, use the first element of the most relevant type
4. For "top hero" or "main header", use the first heading element

Return ONLY a JSON array of changes, no explanations or markdown.`;

		// Call OpenAI API
		try {
			console.log("Calling OpenAI GPT-4o-mini for global changes");
			const response = await openai.chat.completions.create({
				model: "gpt-4o-mini",
				messages: [
					{
						role: "system",
						content: systemPrompt,
					},
				],
				temperature: 0.1, // Low temperature for consistent JSON output
				max_tokens: 2000,
			});

			// Validate response
			if (!response || !response.choices || response.choices.length === 0) {
				console.error("Empty response from OpenAI API");
				throw new Error("No response from AI service");
			}

			let text = response.choices[0]?.message?.content || "";
			if (!text || text.trim() === "") {
				console.error("Empty text in OpenAI response:", response);
				throw new Error("AI service returned empty response");
			}

			// Clean up response (remove markdown code blocks if present)
			text = text
				.replace(/```json\n?/g, "")
				.replace(/```\n?/g, "")
				.trim();

			// Parse and validate JSON
			let changes: Array<{
				elementId: string;
				changes: Record<string, string | number | boolean | object>;
			}>;
			try {
				changes = JSON.parse(text);
			} catch (parseError) {
				console.error("Failed to parse OpenAI response:", text);
				console.error("Parse error:", parseError);
				throw new Error("Invalid JSON response from AI");
			}

			// Validate that we got a proper array
			if (!Array.isArray(changes)) {
				throw new Error("AI returned invalid response format - expected array");
			}

			return NextResponse.json({
				success: true,
				changes,
			} as GlobalAIResponse);
			
		} catch (error: unknown) {
			console.error("OpenAI API error:", error instanceof Error ? error.message : String(error));
			throw error;
		}

	} catch (error: unknown) {
		console.error("Error in global-ai API:", error);
		return NextResponse.json(
			{
				success: false,
				error: error instanceof Error ? error.message : "Unknown error occurred",
			} as GlobalAIResponse,
			{ status: 500 },
		);
	}
}
