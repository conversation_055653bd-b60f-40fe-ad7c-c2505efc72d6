import { NextRequest, NextResponse } from 'next/server';
import { deleteFeatureTemplate } from '@/lib/supabase';

// DELETE /api/templates/[id]
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    await deleteFeatureTemplate(params.id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting template:', error);
    return NextResponse.json(
      { error: 'Failed to delete template' },
      { status: 500 }
    );
  }
}
