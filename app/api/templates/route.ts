import { NextRequest, NextResponse } from 'next/server';
import { getFeatureTemplates, createFeatureTemplate } from '@/lib/supabase';

// GET /api/templates?companyId=xxx
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const companyId = searchParams.get('companyId');

    if (!companyId) {
      return NextResponse.json(
        { error: 'companyId is required' },
        { status: 400 }
      );
    }

    const templates = await getFeatureTemplates(companyId);

    return NextResponse.json({ success: true, templates });
  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    );
  }
}

// POST /api/templates
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { companyId, userId, name, properties } = body;

    if (!companyId || !userId || !name || !properties) {
      return NextResponse.json(
        { error: 'companyId, userId, name, and properties are required' },
        { status: 400 }
      );
    }

    const template = await createFeatureTemplate(
      companyId,
      userId,
      name,
      properties
    );

    return NextResponse.json({ success: true, template });
  } catch (error) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    );
  }
}
