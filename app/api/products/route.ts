import { NextRequest, NextResponse } from "next/server";
import { getWhopSdk, getWhopClient } from "@/lib/whop-sdk";
import { headers } from "next/headers";

// GET /api/products - Fetch all products for the company
export async function GET(request: NextRequest) {
	try {
		const headersList = await headers();
		const whopSdk = getWhopSdk();
		
		// Verify user token
		const { userId } = await whopSdk.verifyUserToken(headersList);
		
		// Get company ID from query params or use default from env
		const { searchParams } = new URL(request.url);
		const companyId = searchParams.get('companyId') || process.env.NEXT_PUBLIC_WHOP_COMPANY_ID;
		
		if (!companyId) {
			return NextResponse.json(
				{ error: 'Company ID is required' },
				{ status: 400 }
			);
		}

		// Verify user has access to company
		const result = await whopSdk.access.checkIfUserHasAccessToCompany({
			userId,
			companyId,
		});

		if (!result.hasAccess || result.accessLevel !== 'admin') {
			return NextResponse.json(
				{ error: 'Unauthorized - Admin access required' },
				{ status: 403 }
			);
		}

		// Fetch products from Whop using the new SDK
		const products: Array<{
			id: string;
			title: string;
			headline?: string;
			visibility: string;
			created_at: string;
			member_count?: number;
		}> = [];
		
		try {
			const whopClient = getWhopClient();
			// Use the new Whop SDK to list products
			for await (const product of whopClient.products.list({ company_id: companyId })) {
				products.push({
					id: product.id,
					title: product.title,
					headline: product.headline ?? undefined,
					visibility: product.visibility,
					created_at: product.created_at,
					member_count: product.member_count ?? undefined,
				});
			}
		} catch (sdkError) {
			console.error('Error fetching products from Whop SDK:', sdkError);
			// Return empty array if no products or error
		}

		return NextResponse.json({
			success: true,
			products,
		});
	} catch (error) {
		console.error('Error fetching products:', error);
		return NextResponse.json(
			{ 
				error: 'Failed to fetch products',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}

// POST /api/products - Create a new product
export async function POST(request: NextRequest) {
	try {
		const headersList = await headers();
		const whopSdk = getWhopSdk();
		
		// Verify user token
		const { userId } = await whopSdk.verifyUserToken(headersList);
		
		const body = await request.json();
		const { name, description, price, currency = 'usd', billingPeriod = 'month', companyId } = body;

		const targetCompanyId = companyId || process.env.NEXT_PUBLIC_WHOP_COMPANY_ID;
		
		if (!targetCompanyId) {
			return NextResponse.json(
				{ error: 'Company ID is required' },
				{ status: 400 }
			);
		}

		// Verify user has access to company
		const result = await whopSdk.access.checkIfUserHasAccessToCompany({
			userId,
			companyId: targetCompanyId,
		});

		if (!result.hasAccess || result.accessLevel !== 'admin') {
			return NextResponse.json(
				{ error: 'Unauthorized - Admin access required' },
				{ status: 403 }
			);
		}

		// Validate required fields
		if (!name || price === undefined) {
			return NextResponse.json(
				{ error: 'Name and price are required' },
				{ status: 400 }
			);
		}

		// In production, create product in your database or via Whop's API
		// The Whop SDK may not expose all plan/product methods yet
		// You would store product data in your own database
		
		// For demo purposes, return a mock created product
		const newProduct = {
			id: `plan_${Date.now()}`,
			name,
			description,
			visibility: 'visible',
			created_at: Date.now(),
			pricing: {
				type: billingPeriod,
				amount: Math.round(price * 100), // Convert to cents
				currency: currency.toLowerCase(),
			},
		};

		console.log('Created product:', newProduct);

		return NextResponse.json({
			success: true,
			product: newProduct,
		});
	} catch (error) {
		console.error('Error creating product:', error);
		return NextResponse.json(
			{ 
				error: 'Failed to create product',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}
