import { getWhopSdk } from "@/lib/whop-sdk";
import { headers } from "next/headers";
import { NextResponse } from "next/server";

// Store temporary session tokens in memory (in production, use Redis or a database)
const sessionTokens = new Map<
	string,
	{ userId: string; companyId: string; expiresAt: number }
>();

// Clean up expired tokens periodically
setInterval(() => {
	const now = Date.now();
	for (const [token, data] of sessionTokens.entries()) {
		if (data.expiresAt < now) {
			sessionTokens.delete(token);
		}
	}
}, 60000); // Clean up every minute

export async function POST() {
	try {
		const headersList = await headers();
		const whopSdk = getWhopSdk();
		const { userId } = await whopSdk.verifyUserToken(headersList);

		// Get user's company (we'll use the first company they have admin access to)
		// In a production app, you might want to let the user select which company
		// For now, we'll use the company ID from env as default
		// In a real implementation, you'd query the user's accessible companies
		const companyId = process.env.NEXT_PUBLIC_WHOP_COMPANY_ID || "";

		// Generate a random session token
		const token = `session_${Math.random().toString(36).substring(2, 15)}_${Date.now()}`;

		// Store the token with user info, expires in 5 minutes
		sessionTokens.set(token, {
			userId,
			companyId,
			expiresAt: Date.now() + 5 * 60 * 1000, // 5 minutes
		});

		return NextResponse.json({ token, companyId });
	} catch (error) {
		console.error("Error generating session token:", error);
		return NextResponse.json(
			{ error: "Failed to generate session token" },
			{ status: 500 },
		);
	}
}

// Endpoint to verify and consume a session token
export async function GET(request: Request) {
	try {
		const { searchParams } = new URL(request.url);
		const token = searchParams.get("token");

		if (!token) {
			return NextResponse.json({ error: "Token is required" }, { status: 400 });
		}

		const sessionData = sessionTokens.get(token);

		if (!sessionData) {
			return NextResponse.json(
				{ error: "Invalid or expired token" },
				{ status: 401 },
			);
		}

		if (sessionData.expiresAt < Date.now()) {
			sessionTokens.delete(token);
			return NextResponse.json({ error: "Token has expired" }, { status: 401 });
		}

		// Token is valid, return the user info
		// Don't delete it yet, as we might need it for the redirect
		return NextResponse.json({
			userId: sessionData.userId,
			companyId: sessionData.companyId,
		});
	} catch (error) {
		console.error("Error verifying session token:", error);
		return NextResponse.json(
			{ error: "Failed to verify session token" },
			{ status: 500 },
		);
	}
}
