import { NextRequest, NextResponse } from 'next/server';
import { findFunnelByHostname } from '@/lib/supabase/mapped-domains';
import { getFunnel } from '@/lib/supabase';

// GET /api/domain-lookup?host=example.com
export async function GET(request: NextRequest) {
  try {
    const host = request.nextUrl.searchParams.get('host');
    const headerHost = request.headers.get('host');
    const hostname = (host || headerHost || '').split(':')[0];

    if (!hostname) {
      return NextResponse.json({ error: 'host is required' }, { status: 400 });
    }

    const mapping = await findFunnelByHostname(hostname);
    if (!mapping) {
      return NextResponse.json({ success: true, found: false });
    }

    const funnel = await getFunnel(mapping.funnelId);
    if (!funnel || funnel.status !== 'published') {
      return NextResponse.json({ success: true, found: false });
    }

    return NextResponse.json({ success: true, found: true, funnel: {
      id: funnel.id,
      title: funnel.title,
      description: funnel.description,
      pages: funnel.pages,
      sections: funnel.sections,
      theme: funnel.theme,
      layout: funnel.layout,
      status: funnel.status,
      slug: funnel.slug,
    } });
  } catch (error) {
    console.error('Error in domain lookup:', error);
    return NextResponse.json({ error: 'Failed to lookup domain' }, { status: 500 });
  }
}
