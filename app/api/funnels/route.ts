import { NextRequest, NextResponse } from 'next/server';
import { getFunnels, createFunnel } from '@/lib/supabase';
import type { FunnelSchema } from '@/lib/types/funnel';

// GET /api/funnels?companyId=xxx
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const companyId = searchParams.get('companyId');

    if (!companyId) {
      return NextResponse.json(
        { error: 'companyId is required' },
        { status: 400 }
      );
    }

    const funnels = await getFunnels(companyId);

    return NextResponse.json({ success: true, funnels });
  } catch (error) {
    console.error('Error fetching funnels:', error);
    return NextResponse.json(
      { error: 'Failed to fetch funnels' },
      { status: 500 }
    );
  }
}

// POST /api/funnels
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { companyId, userId, funnel, status } = body;

    if (!companyId || !userId || !funnel) {
      return NextResponse.json(
        { error: 'companyId, userId, and funnel are required' },
        { status: 400 }
      );
    }

    // Validate and fix layout field to ensure it matches database constraints
    const validatedFunnel = { ...funnel };
    if (!validatedFunnel.layout || !['single-page', 'multi-step'].includes(validatedFunnel.layout)) {
      validatedFunnel.layout = 'single-page';
    }

    const newFunnel = await createFunnel(
      companyId,
      userId,
      validatedFunnel as FunnelSchema,
      status || 'draft'
    );

    return NextResponse.json({ success: true, funnel: newFunnel });
  } catch (error) {
    console.error('Error creating funnel:', error);
    return NextResponse.json(
      { error: 'Failed to create funnel' },
      { status: 500 }
    );
  }
}
