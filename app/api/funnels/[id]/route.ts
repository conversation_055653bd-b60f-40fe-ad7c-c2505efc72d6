import { NextRequest, NextResponse } from 'next/server';
import { getFunnel, updateFunnel, deleteFunnel } from '@/lib/supabase';

// GET /api/funnels/[id]
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const funnel = await getFunnel(params.id);

    if (!funnel) {
      return NextResponse.json(
        { error: 'Funnel not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true, funnel });
  } catch (error) {
    console.error('Error fetching funnel:', error);
    return NextResponse.json(
      { error: 'Failed to fetch funnel' },
      { status: 500 }
    );
  }
}

// PUT /api/funnels/[id]
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const body = await request.json();
    const { updates } = body;

    if (!updates) {
      return NextResponse.json(
        { error: 'updates are required' },
        { status: 400 }
      );
    }

    const updatedFunnel = await updateFunnel(params.id, updates);

    return NextResponse.json({ success: true, funnel: updatedFunnel });
  } catch (error) {
    console.error('Error updating funnel:', error);
    return NextResponse.json(
      { error: 'Failed to update funnel' },
      { status: 500 }
    );
  }
}

// DELETE /api/funnels/[id]
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    await deleteFunnel(params.id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting funnel:', error);
    return NextResponse.json(
      { error: 'Failed to delete funnel' },
      { status: 500 }
    );
  }
}
