import OpenAI from "openai";
import { NextResponse } from "next/server";

// Initialize OpenAI
const openai = new OpenAI({
	apiKey: process.env.OPENAI_API_KEY || "",
});

interface EditFeaturesRequest {
	prompt: string;
	currentProperties: Record<string, string | number | boolean | object>;
	elementType: string;
}

interface EditFeaturesResponse {
	success: boolean;
	changes?: Record<string, string | number | boolean | object>;
	error?: string;
}

export async function POST(request: Request): Promise<Response> {
	try {
		const body: EditFeaturesRequest = await request.json();
		const { prompt, currentProperties, elementType } = body;

		// Validate input
		if (!prompt || !currentProperties || !elementType) {
			return NextResponse.json(
				{
					success: false,
					error: "Missing required fields: prompt, currentProperties, or elementType",
				} as EditFeaturesResponse,
				{ status: 400 },
			);
		}

		// Check if API key is configured
		if (!process.env.OPENAI_API_KEY) {
			console.error("OPENAI_API_KEY is not configured");
			return NextResponse.json(
				{
					success: false,
					error: "AI service is not configured. Please contact support.",
				} as EditFeaturesResponse,
				{ status: 500 },
			);
		}

		// Create system prompt for features editing
		const systemPrompt = `You are an expert web designer and developer. You need to interpret natural language requests for editing a features section and return ONLY the specific property changes as JSON.

CRITICAL RULES:
1. Only change the properties that are explicitly mentioned in the user's request
2. Do NOT delete or modify existing features unless explicitly asked
3. When editing content, preserve the existing features array structure
4. Be precise and only change what's requested

Current features section properties:
${JSON.stringify(currentProperties, null, 2)}

User request: "${prompt}"

You can change both CONTENT and STYLING properties. Return ONLY the specific properties that need to be changed:

For layout:
- "columns": number (1-4)

For feature card styling:
- "featureBackground": hex color (e.g., "#3b82f6")
- "featureBorderColor": hex color (e.g., "#e5e7eb") 
- "featureBorderRadius": number (0-24)
- "featureGap": number (8-64)

For icon styling:
- "iconBackground": hex color (e.g., "#667eea")
- "iconSize": number (24-80)

For typography:
- "featureTitleColor": hex color (e.g., "#111827")
- "featureDescriptionColor": hex color (e.g., "#6b7280")

For CTA buttons:
- "ctaBackground": hex color (e.g., "#667eea")
- "ctaTextColor": hex color (e.g., "#ffffff")

For content changes:
- "features": array of feature objects with title, description, icon, iconUrl, ctaText, ctaUrl
- "subheading": string (section subheading)
- "content": string (section main heading)

Examples:
- "Make cards blue" → {"featureBackground": "#3b82f6"}
- "Change to 2 columns" → {"columns": 2}
- "Make icons larger" → {"iconSize": 80}
- "Change text color to red" → {"featureTitleColor": "#ef4444", "featureDescriptionColor": "#ef4444"}
- "Change the first feature title to 'Amazing Feature'" → {"features": [{"title": "Amazing Feature", "description": "Powerful tools to help you succeed", "icon": "⚡", "iconUrl": "", "ctaText": "", "ctaUrl": ""}, ...existing features]}
- "Update the subheading to 'Everything you need'" → {"subheading": "Everything you need"}

IMPORTANT: When modifying the features array, preserve all existing features and only change the specific ones mentioned. If changing a single feature, include all existing features in the array.

Return ONLY the JSON object, no explanations or markdown.`;

		// Call OpenAI API
		try {
			console.log("Calling OpenAI GPT-4o-mini");
			const response = await openai.chat.completions.create({
				model: "gpt-4o-mini",
				messages: [
					{
						role: "system",
						content: systemPrompt,
					},
				],
				temperature: 0.1, // Low temperature for consistent JSON output
				max_tokens: 1000,
			});

			// Validate response
			if (!response || !response.choices || response.choices.length === 0) {
				console.error("Empty response from OpenAI API");
				throw new Error("No response from AI service");
			}

			let text = response.choices[0]?.message?.content || "";
			if (!text || text.trim() === "") {
				console.error("Empty text in OpenAI response:", response);
				throw new Error("AI service returned empty response");
			}

			// Clean up response (remove markdown code blocks if present)
			text = text
				.replace(/```json\n?/g, "")
				.replace(/```\n?/g, "")
				.trim();

			// Parse and validate JSON
			let changes: Record<string, string | number | boolean | object>;
			try {
				changes = JSON.parse(text);
			} catch (parseError) {
				console.error("Failed to parse OpenAI response:", text);
				console.error("Parse error:", parseError);
				throw new Error("Invalid JSON response from AI");
			}

			// Validate that we got a proper object
			if (typeof changes !== "object" || changes === null) {
				throw new Error("AI returned invalid response format");
			}

			return NextResponse.json({
				success: true,
				changes,
			} as EditFeaturesResponse);
			
		} catch (error: unknown) {
			console.error("OpenAI API error:", error instanceof Error ? error.message : String(error));
			throw error;
		}

	} catch (error) {
		console.error("Error in edit-features API:", error);
		return NextResponse.json(
			{
				success: false,
				error: error instanceof Error ? error.message : "Unknown error occurred",
			} as EditFeaturesResponse,
			{ status: 500 },
		);
	}
}
