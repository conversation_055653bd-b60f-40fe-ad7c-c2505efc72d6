import { NextRequest, NextResponse } from 'next/server';
import { addDomain, listDomains, removeDomain, setDomainStatus } from '@/lib/supabase/mapped-domains';

// GET /api/domains?funnelId=xxx
export async function GET(request: NextRequest) {
  try {
    const funnelId = request.nextUrl.searchParams.get('funnelId');
    if (!funnelId) {
      return NextResponse.json({ error: 'funnelId is required' }, { status: 400 });
    }
    const domains = await listDomains(funnelId);
    return NextResponse.json({ success: true, domains });
  } catch (error) {
    console.error('Error listing domains:', error);
    return NextResponse.json({ error: 'Failed to list domains' }, { status: 500 });
  }
}

// POST /api/domains
// body: { funnelId, whopCompanyId, hostname }
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { funnelId, whopCompanyId, hostname } = body;
    if (!funnelId || !whopCompanyId || !hostname) {
      return NextResponse.json({ error: 'funnelId, whopCompanyId, hostname required' }, { status: 400 });
    }
    const domain = await addDomain({ funnelId, whopCompanyId, hostname });
    return NextResponse.json({ success: true, domain });
  } catch (error) {
    console.error('Error adding domain:', error);
    return NextResponse.json({ error: 'Failed to add domain' }, { status: 500 });
  }
}

// PUT /api/domains
// body: { domainId, status }
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { domainId, status } = body as { domainId: string; status: 'pending' | 'active' | 'disabled' };
    if (!domainId || !status) {
      return NextResponse.json({ error: 'domainId and status required' }, { status: 400 });
    }
    const domain = await setDomainStatus(domainId, status);
    return NextResponse.json({ success: true, domain });
  } catch (error) {
    console.error('Error updating domain:', error);
    return NextResponse.json({ error: 'Failed to update domain' }, { status: 500 });
  }
}

// DELETE /api/domains?domainId=xxx
export async function DELETE(request: NextRequest) {
  try {
    const domainId = request.nextUrl.searchParams.get('domainId');
    if (!domainId) {
      return NextResponse.json({ error: 'domainId is required' }, { status: 400 });
    }
    await removeDomain(domainId);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error removing domain:', error);
    return NextResponse.json({ error: 'Failed to remove domain' }, { status: 500 });
  }
}
