import { Card, <PERSON><PERSON>, <PERSON>, Text } from "frosted-ui";
import { OpenInNewTabButton } from "@/components/OpenInNewTabButton";
import { getWhopSdk } from "@/lib/whop-sdk";
import { headers } from "next/headers";

export const dynamic = 'force-dynamic';

export default async function Page() {
	// Get user authentication info from iframe headers
	let userId: string | null = null;
	let userName = "User";

	try {
		const headersList = await headers();
		const whopSdk = getWhopSdk();
		const result = await whopSdk.verifyUserToken(headersList);
		userId = result.userId;

		// Get user details
		const user = await whopSdk.users.getUser({ userId });
		userName = user.name || user.username || "User";
	} catch (error) {
		// User is not authenticated or not in iframe context
		console.error("Error getting user info:", error);
	}

	return (
		<div
			style={{ minHeight: "100vh", background: "var(--gray-2)" }}
			className="py-9"
		>
			<div className="max-w-3xl mx-auto px-4">
				<div className="flex flex-col gap-8">
					<div className="flex flex-col items-center gap-3 mb-4">
						<Heading size="9" align="center">
							{userId ? `Welcome back, ${userName}!` : "Welcome to Your Whop App"}
						</Heading>
						<Text size="4" color="gray" align="center">
							{userId
								? "Ready to create amazing funnels with AI"
								: "Follow these steps to get started with your Whop application"}
						</Text>
					</div>

					{userId && (
						<Card>
							<div className="flex flex-col gap-4">
								<div className="flex flex-col gap-2">
									<Heading size="5">Open in New Tab</Heading>
									<Text color="gray">
										Want to use FunnelFlow AI in a full browser window? Click the
										button below to open the app in a new tab with your
										authentication.
									</Text>
								</div>
								<OpenInNewTabButton />
							</div>
						</Card>
					)}

					<div className="flex flex-col gap-6">
						<Card>
							<div className="flex gap-3 mb-3">
								<div
									className="flex items-center justify-center"
									style={{
										width: "32px",
										height: "32px",
										borderRadius: "50%",
										background: "var(--accent-9)",
										color: "white",
										flexShrink: 0,
									}}
								>
									<Text weight="bold">1</Text>
								</div>
								<Heading size="5">Create your Whop app</Heading>
							</div>
							<div style={{ marginLeft: "44px" }}>
								<Text color="gray">
									Go to your{" "}
									<Link
										href="https://whop.com/dashboard"
										target="_blank"
										rel="noopener noreferrer"
									>
										Whop Dashboard
									</Link>{" "}
									and create a new app in the Developer section.
								</Text>
							</div>
						</Card>

						<Card>
							<div className="flex gap-3 mb-3">
								<div
									className="flex items-center justify-center"
									style={{
										width: "32px",
										height: "32px",
										borderRadius: "50%",
										background: "var(--accent-9)",
										color: "white",
										flexShrink: 0,
									}}
								>
									<Text weight="bold">2</Text>
								</div>
								<Heading size="5">Set up environment variables</Heading>
							</div>
							<div style={{ marginLeft: "44px" }}>
								<Text
									color="gray"
									style={{ display: "block", marginBottom: "12px" }}
								>
									Copy the .env file from your dashboard and create a new .env
									file in your project root. This will contain all the necessary
									environment variables for your app.
								</Text>
								{process.env.NODE_ENV === "development" && (
									<div
										className="p-3"
										style={{
											background: "var(--gray-3)",
											borderRadius: "var(--radius-3)",
											fontFamily: "monospace",
											fontSize: "12px",
										}}
									>
										<code>
											WHOP_API_KEY={process.env.WHOP_API_KEY?.slice(0, 5)}...
											<br />
											NEXT_PUBLIC_WHOP_AGENT_USER_ID=
											{process.env.NEXT_PUBLIC_WHOP_AGENT_USER_ID}
											<br />
											NEXT_PUBLIC_WHOP_APP_ID=
											{process.env.NEXT_PUBLIC_WHOP_APP_ID}
											<br />
											NEXT_PUBLIC_WHOP_COMPANY_ID=
											{process.env.NEXT_PUBLIC_WHOP_COMPANY_ID}
										</code>
									</div>
								)}
							</div>
						</Card>

						<Card>
							<div className="flex gap-3 mb-3">
								<div
									className="flex items-center justify-center"
									style={{
										width: "32px",
										height: "32px",
										borderRadius: "50%",
										background: "var(--accent-9)",
										color: "white",
										flexShrink: 0,
									}}
								>
									<Text weight="bold">3</Text>
								</div>
								<Heading size="5">Install your app into your whop</Heading>
							</div>
							<div style={{ marginLeft: "44px" }}>
								{process.env.NEXT_PUBLIC_WHOP_APP_ID ? (
									<Link
										href={`https://whop.com/apps/${process.env.NEXT_PUBLIC_WHOP_APP_ID}/install`}
										target="_blank"
										rel="noopener noreferrer"
									>
										Click here to install your app
									</Link>
								) : (
									<Text color="amber">
										Please set your environment variables to see the
										installation link
									</Text>
								)}
							</div>
						</Card>
					</div>

					<div className="mt-4">
						<Text size="2" color="gray" align="center">
							Need help? Visit the{" "}
							<Link
								href="https://dev.whop.com"
								target="_blank"
								rel="noopener noreferrer"
							>
								Whop Documentation
							</Link>
						</Text>
					</div>
				</div>
			</div>
		</div>
	);
}
