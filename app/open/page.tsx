"use client";

import { Suspense, useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { Heading, Text } from "frosted-ui";

function OpenPageContent() {
	const searchParams = useSearchParams();
	const [error, setError] = useState<string | null>(null);
	const [isVerifying, setIsVerifying] = useState(true);

	useEffect(() => {
		const verifyAndRedirect = async () => {
			try {
				const token = searchParams.get("token");
				const redirect = searchParams.get("redirect");

				if (!token) {
					setError("No token provided");
					setIsVerifying(false);
					return;
				}

				// Verify the token
				const response = await fetch(`/api/session-token?token=${token}`);

				if (!response.ok) {
					const data = await response.json();
					setError(data.error || "Failed to verify session");
					setIsVerifying(false);
					return;
				}

				const { userId, companyId } = await response.json();

				// Store user info in localStorage so the app can use it
				// This allows the app to work outside the iframe context
				localStorage.setItem(
					"whop_standalone_session",
					JSON.stringify({
						userId,
						companyId,
						timestamp: Date.now(),
					}),
				);

				// Redirect to the specified path or default to dashboard
				const redirectPath = redirect || `/dashboard/${companyId}`;
				window.location.href = redirectPath;
			} catch (err) {
				console.error("Error during verification:", err);
				setError("An unexpected error occurred");
				setIsVerifying(false);
			}
		};

		verifyAndRedirect();
	}, [searchParams]);

	return (
		<div
			className="flex items-center justify-center"
			style={{ minHeight: "100vh", background: "var(--gray-2)" }}
		>
			<div className="flex flex-col items-center gap-4 max-w-md px-4">
				{isVerifying ? (
					<>
						<Heading size="7" align="center">
							Opening FunnelFlow AI...
						</Heading>
						<Text color="gray" align="center">
							Please wait while we verify your session and redirect you to the
							dashboard.
						</Text>
						<div
							className="animate-spin rounded-full h-12 w-12 border-b-2"
							style={{ borderColor: "var(--accent-9)" }}
						/>
					</>
				) : error ? (
					<>
						<Heading size="7" align="center" color="red">
							Authentication Failed
						</Heading>
						<Text color="gray" align="center">
							{error}
						</Text>
						<Text size="2" color="gray" align="center">
							Please try again from the main app or contact support if the issue
							persists.
						</Text>
					</>
				) : null}
			</div>
		</div>
	);
}

export default function OpenPage() {
	return (
		<Suspense
			fallback={
				<div
					className="flex items-center justify-center"
					style={{ minHeight: "100vh", background: "var(--gray-2)" }}
				>
					<div className="flex flex-col items-center gap-4 max-w-md px-4">
						<Heading size="7" align="center">
							Loading...
						</Heading>
						<div
							className="animate-spin rounded-full h-12 w-12 border-b-2"
							style={{ borderColor: "var(--accent-9)" }}
						/>
					</div>
				</div>
			}
		>
			<OpenPageContent />
		</Suspense>
	);
}
