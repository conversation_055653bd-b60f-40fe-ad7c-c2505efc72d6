import { headers } from 'next/headers';
import { findFunnelByHostname } from '@/lib/supabase/mapped-domains';
import { getFunnel } from '@/lib/supabase';
import { notFound } from 'next/navigation';

export const dynamic = 'force-dynamic';

export default async function DomainFunnelPage({ params }: { params: Promise<{ domain: string }> }) {
  const { domain: host } = await params;
  const headersList = await headers();
  const headerHost = headersList.get('host') || host;
  const hostname = (headerHost || '').split(':')[0].toLowerCase();

  const mapping = await findFunnelByHostname(hostname);
  if (!mapping) return notFound();

  const funnel = await getFunnel(mapping.funnelId);
  if (!funnel || funnel.status !== 'published') return notFound();

  // Render first page (landing) for now
  const page = funnel.pages?.[0];
  return (
    <main style={{ minHeight: '100vh', background: funnel.theme?.backgroundColor || '#fff', color: funnel.theme?.textColor || '#000' }}>
      <section style={{ maxWidth: 960, margin: '0 auto', padding: 24 }}>
        <h1 style={{ fontSize: 36, marginBottom: 12 }}>{funnel.title}</h1>
        {page ? (
          <div>
            {page.sections?.map((section) => (
              <div key={section.id} style={{ marginBottom: 24 }}>
                <h2 style={{ fontSize: 24 }}>{section.heading}</h2>
                <p style={{ opacity: 0.8 }}>{section.content as string}</p>
              </div>
            ))}
          </div>
        ) : (
          <p>No content yet.</p>
        )}
      </section>
    </main>
  );
}
