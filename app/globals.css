/* ===================================
   GOOGLE FONTS - Must be at the top
   =================================== */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@100;300;400;700;900&display=swap');

@layer theme, base, frosted_ui, components, utilities;

@import "tailwindcss/theme.css" layer(theme);
@import "tailwindcss/preflight.css" layer(base);
@import "tailwindcss/utilities.css" layer(utilities);
@import "@whop/react/styles.css" layer(frosted_ui);
@import "frosted-ui/styles.css" layer(frosted_ui);

@config '../tailwind.config.ts';

/* ===================================
   GLOBAL STYLES
   =================================== */

body {
	background: var(--color-background);
	color: var(--color-text);
	font-family: var(--font-family-default);
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* ===================================
   ANIMATIONS
   =================================== */

@keyframes slideInFromRight {
	from {
		transform: translateX(100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes scaleIn {
	from {
		transform: scale(0.95);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

@keyframes shimmer {
	0% {
		background-position: -1000px 0;
	}
	100% {
		background-position: 1000px 0;
	}
}

/* ===================================
   INTERACTIVE ELEMENTS
   =================================== */

/* Enhanced button styles */
button {
	transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

button:not(:disabled):hover {
	transform: translateY(-1px);
}

button:not(:disabled):active {
	transform: translateY(0);
}

button:disabled {
	cursor: not-allowed;
	opacity: 0.6;
}

/* Enhanced input styles */
input,
textarea,
select {
	transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

input:focus,
textarea:focus,
select:focus {
	outline: 2px solid var(--accent-8);
	outline-offset: 2px;
	border-color: var(--accent-7) !important;
}

input:hover:not(:focus):not(:disabled),
textarea:hover:not(:focus):not(:disabled),
select:hover:not(:focus):not(:disabled) {
	border-color: var(--gray-8);
}

input::placeholder,
textarea::placeholder {
	color: var(--gray-9);
}

/* ===================================
   SIDEBAR COMPONENTS
   =================================== */

.whop-sidebar-button {
	transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
	border-radius: 8px;
	position: relative;
	overflow: hidden;
}

.whop-sidebar-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
	opacity: 0;
	transition: opacity 0.2s ease;
}

.whop-sidebar-button:hover::before {
	opacity: 1;
}

.whop-sidebar-button:hover {
	transform: translateY(-2px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.whop-sidebar-button:active {
	transform: translateY(0);
}

/* ===================================
   TOAST NOTIFICATIONS
   =================================== */

.whop-toast {
	animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===================================
   CARD COMPONENTS
   =================================== */

.whop-card {
	border: 1px solid var(--gray-4);
	border-radius: 12px;
	background: var(--color-panel-solid);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.whop-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
}

.whop-card:hover {
	border-color: var(--gray-6);
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
	transform: translateY(-2px);
}

.whop-card:hover::before {
	opacity: 1;
}

/* Clickable cards */
.whop-card[role="button"],
.whop-card.clickable {
	cursor: pointer;
}

.whop-card[role="button"]:active,
.whop-card.clickable:active {
	transform: translateY(0);
}

/* ===================================
   TABLE STYLES
   =================================== */

table {
	border-collapse: collapse;
}

table thead tr {
	background: var(--gray-3);
	border-bottom: 1px solid var(--gray-6);
}

table thead th {
	font-size: 11px;
	font-weight: 700;
	letter-spacing: 0.5px;
	text-transform: uppercase;
	color: var(--gray-11);
	padding: 14px 20px;
}

table tbody tr {
	border-bottom: 1px solid var(--gray-4);
	transition: background-color 0.15s ease;
}

table tbody tr:hover {
	background: var(--gray-2);
}

table tbody tr:last-child {
	border-bottom: none;
}

table tbody td {
	padding: 16px 20px;
}

/* ===================================
   UTILITY CLASSES
   =================================== */

/* Border utilities */
.whop-border {
	border-color: var(--gray-4) !important;
}

.whop-border-soft {
	border-color: var(--gray-5) !important;
}

/* Shadow utilities */
.whop-shadow-sm {
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
}

.whop-shadow-md {
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06), 0 2px 4px rgba(0, 0, 0, 0.03);
}

.whop-shadow-lg {
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
}

/* Loading state */
.whop-loading {
	background: linear-gradient(
		90deg,
		var(--gray-3) 0px,
		var(--gray-4) 40px,
		var(--gray-3) 80px
	);
	background-size: 1000px 100%;
	animation: shimmer 1.5s infinite;
}

/* Smooth scrolling */
.whop-smooth-scroll {
	scroll-behavior: smooth;
	-webkit-overflow-scrolling: touch;
}

/* Custom scrollbar */
.whop-scrollbar::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

.whop-scrollbar::-webkit-scrollbar-track {
	background: var(--gray-2);
	border-radius: 4px;
}

.whop-scrollbar::-webkit-scrollbar-thumb {
	background: var(--gray-6);
	border-radius: 4px;
	transition: background 0.2s ease;
}

.whop-scrollbar::-webkit-scrollbar-thumb:hover {
	background: var(--gray-7);
}

/* Focus ring */
.whop-focus-ring:focus-visible {
	outline: 2px solid var(--accent-8);
	outline-offset: 2px;
	border-radius: 6px;
}

/* ===================================
   RESPONSIVE UTILITIES
   =================================== */

@media (max-width: 768px) {
	.whop-card {
		border-radius: 8px;
	}
	
	table {
		font-size: 14px;
	}
	
	table thead th,
	table tbody td {
		padding: 12px 16px;
	}
}
