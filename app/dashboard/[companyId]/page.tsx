import { DashboardContent } from "@/components/DashboardContent";
import { getWhopSdk } from "@/lib/whop-sdk";
import { headers } from "next/headers";

export const dynamic = 'force-dynamic';

export default async function DashboardPage({
	params,
}: {
	params: Promise<{ companyId: string }>;
}) {
	// The headers contains the user token
	const headersList = await headers();

	// The companyId is a path param
	const { companyId } = await params;

	// Get whopSdk instance
	const whopSdk = getWhopSdk();

	// The user token is in the headers
	const { userId } = await whopSdk.verifyUserToken(headersList);

	const result = await whopSdk.access.checkIfUserHasAccessToCompany({
		userId,
		companyId,
	});

	const user = await whopSdk.users.getUser({ userId });
	const company = await whopSdk.companies.getCompany({ companyId });

	// Either: 'admin' | 'no_access';
	// 'admin' means the user is an admin of the company, such as an owner or moderator
	// 'no_access' means the user is not an authorized member of the company
	const { accessLevel } = result;

	// Redirect if user doesn't have access
	if (!result.hasAccess || accessLevel !== "admin") {
		const { Heading, Text } = await import("frosted-ui");
		return (
			<div
				className="flex items-center justify-center px-8"
				style={{ minHeight: "100vh" }}
			>
				<div className="flex flex-col items-center gap-2">
					<Heading size="7">Access Denied</Heading>
					<Text color="gray" align="center">
						You do not have permission to access this company&apos;s dashboard.
					</Text>
				</div>
			</div>
		);
	}

	return (
		<DashboardContent
			userId={userId}
			companyId={companyId}
			userName={user.name || "User"}
			username={user.username || "unknown"}
			companyTitle={company.title || "Company"}
		/>
	);
}
